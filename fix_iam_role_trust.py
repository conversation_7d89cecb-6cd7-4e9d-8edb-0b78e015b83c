#!/usr/bin/env python3
"""
Fix IAM role trust relationship for Snowflake
"""

import boto3
import json
import logging
from pathlib import Path
import snowflake.connector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_snowflake_aws_info():
    """Get Snowflake AWS account information."""
    try:
        # Load Snowflake connection config
        import yaml
        with open('config/snowflake_config.yml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Connect to Snowflake
        conn = snowflake.connector.connect(
            user=config['snowflake']['user'],
            password=config['snowflake']['password'],
            account=config['snowflake']['account'],
            warehouse=config['snowflake']['warehouse'],
            database=config['snowflake']['database'],
            schema=config['snowflake']['schema']
        )
        
        cursor = conn.cursor()
        
        # Get Snowflake AWS account info
        cursor.execute("SELECT SYSTEM$GET_AWS_SNS_IAM_POLICY('S3_LIVE_INTEGRATION')")
        result = cursor.fetchone()
        
        if result and result[0]:
            aws_info = json.loads(result[0])
            logger.info(f"✅ Snowflake AWS Account ID: {aws_info.get('aws_account_id')}")
            logger.info(f"✅ Snowflake AWS User ARN: {aws_info.get('aws_user_arn')}")
            logger.info(f"✅ External ID: {aws_info.get('external_id')}")
            
            cursor.close()
            conn.close()
            return aws_info
        else:
            logger.error("❌ Could not get Snowflake AWS information")
            cursor.close()
            conn.close()
            return None
            
    except Exception as e:
        logger.error(f"❌ Failed to get Snowflake AWS info: {e}")
        return None

def update_iam_role_trust_policy():
    """Update the IAM role trust policy with correct Snowflake information."""
    
    role_name = "SnowflakeS3AccessRole"
    
    try:
        # Get Snowflake AWS information
        logger.info("🔍 Getting Snowflake AWS information...")
        snowflake_info = get_snowflake_aws_info()
        
        if not snowflake_info:
            logger.error("❌ Cannot proceed without Snowflake AWS information")
            return False
        
        # Create correct trust policy
        trust_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "AWS": snowflake_info["aws_user_arn"]
                    },
                    "Action": "sts:AssumeRole",
                    "Condition": {
                        "StringEquals": {
                            "sts:ExternalId": snowflake_info["external_id"]
                        }
                    }
                }
            ]
        }
        
        # Update IAM role trust policy
        logger.info(f"🔄 Updating IAM role trust policy for: {role_name}")
        
        iam_client = boto3.client('iam')
        
        response = iam_client.update_assume_role_policy(
            RoleName=role_name,
            PolicyDocument=json.dumps(trust_policy)
        )
        
        logger.info("✅ IAM role trust policy updated successfully!")
        
        # Verify the role
        role_response = iam_client.get_role(RoleName=role_name)
        role_arn = role_response['Role']['Arn']
        logger.info(f"✅ Role ARN: {role_arn}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to update IAM role trust policy: {e}")
        return False

def test_snowflake_integration():
    """Test if Snowflake can now access the external stages."""
    try:
        # Load Snowflake connection config
        import yaml
        with open('config/snowflake_config.yml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Connect to Snowflake
        conn = snowflake.connector.connect(
            user=config['snowflake']['user'],
            password=config['snowflake']['password'],
            account=config['snowflake']['account'],
            warehouse=config['snowflake']['warehouse'],
            database=config['snowflake']['database'],
            schema=config['snowflake']['schema']
        )
        
        cursor = conn.cursor()
        
        # Test the storage integration
        logger.info("🧪 Testing storage integration...")
        cursor.execute("DESC INTEGRATION S3_LIVE_INTEGRATION")
        integration_info = cursor.fetchall()
        
        for row in integration_info:
            if row[0] == 'STORAGE_AWS_IAM_USER_ARN':
                logger.info(f"✅ Storage AWS IAM User ARN: {row[1]}")
            elif row[0] == 'STORAGE_AWS_EXTERNAL_ID':
                logger.info(f"✅ Storage AWS External ID: {row[1]}")
        
        # Test listing files in a stage
        logger.info("🧪 Testing stage access...")
        try:
            cursor.execute("LIST @S3_LIVE_USERS_STAGE")
            files = cursor.fetchall()
            logger.info(f"✅ Stage access successful - found {len(files)} files")
        except Exception as stage_error:
            logger.warning(f"⚠️ Stage access test failed: {stage_error}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Snowflake integration test failed: {e}")
        return False

def main():
    """Main function."""
    logger.info("🚀 Starting IAM role trust policy fix...")
    
    # Update the trust policy
    if update_iam_role_trust_policy():
        logger.info("✅ Trust policy updated successfully!")
        
        # Test the integration
        logger.info("🧪 Testing Snowflake integration...")
        if test_snowflake_integration():
            logger.info("🎉 Snowflake integration is working!")
        else:
            logger.warning("⚠️ Snowflake integration test failed - may need manual intervention")
    else:
        logger.error("❌ Failed to update trust policy")
        return False
    
    logger.info("\n📝 Next steps:")
    logger.info("1. Run the Airflow pipeline again")
    logger.info("2. The run_dbt_models step should now succeed")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
