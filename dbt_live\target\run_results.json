{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T05:32:09.487981Z", "invocation_id": "f6f63e44-1b6c-400b-8d04-e6aed961363f", "env": {}}, "results": [{"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:06.561364Z", "completed_at": "2025-06-04T05:32:06.592639Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:06.636753Z", "completed_at": "2025-06-04T05:32:07.275343Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.7474009990692139, "adapter_response": {}, "message": "Database Error in model stg_events (models/staging/stg_events.sql)\n  091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.\n  compiled code at target/run/live_c360/models/staging/stg_events.sql", "failures": null, "unique_id": "model.live_c360.stg_events", "compiled": true, "compiled_code": "\n\n-- Staging model for live events data\n-- Cleans and standardizes raw event data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_events\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        event_id,\n        user_id,\n        session_id,\n        \n        -- Event details\n        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,\n        lower(trim(platform)) as platform,\n        lower(trim(action)) as action,\n        trim(url) as url,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where event_id is not null  -- Filter out any null event IDs\n      and user_id is not null   -- Filter out events without user IDs\n),\n\nurl_parsed as (\n    select\n        *,\n        -- URL parsing\n        case \n            when url like '%/product/%' then 'Product Page'\n            when url like '%/cart%' then 'Cart'\n            when url like '%/checkout%' then 'Checkout'\n            when url like '%/search%' then 'Search'\n            when url like '%/category/%' then 'Category'\n            when url like '%/home%' or url = 'https://example.com/' then 'Home'\n            else 'Other'\n        end as page_type,\n        \n        -- Extract path from URL\n        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path\n        \n    from cleaned_data\n),\n\nfinal as (\n    select\n        *,\n        -- Date extractions\n        date(event_timestamp) as event_date,\n        extract(year from event_timestamp) as event_year,\n        extract(month from event_timestamp) as event_month,\n        extract(day from event_timestamp) as event_day,\n        extract(hour from event_timestamp) as event_hour,\n        dayname(event_timestamp) as event_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'\n            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'\n            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Platform standardization\n        case \n            when platform in ('ios', 'android') then 'Mobile'\n            when platform = 'web' then 'Web'\n            else 'Unknown'\n        end as device_category,\n        \n        -- Action categorization\n        case \n            when action in ('view', 'click') then 'Engagement'\n            when action = 'log' then 'Authentication'\n            when action = 'purchase' then 'Conversion'\n            else 'Other'\n        end as action_category,\n        \n        -- Funnel stage\n        case \n            when action = 'view' and page_type = 'Home' then 'Awareness'\n            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'\n            when action = 'view' and page_type = 'Product Page' then 'Consideration'\n            when action = 'click' and page_type = 'Cart' then 'Intent'\n            when action = 'purchase' then 'Purchase'\n            else 'Other'\n        end as funnel_stage,\n        \n        -- Data quality flags\n        case \n            when event_timestamp > current_timestamp() then true\n            else false\n        end as has_future_event_timestamp,\n        \n        -- Recency\n        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,\n        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,\n        datediff('day', event_timestamp, current_timestamp()) as days_since_event\n        \n    from url_parsed\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_events", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:07.301162Z", "completed_at": "2025-06-04T05:32:07.314100Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:07.315226Z", "completed_at": "2025-06-04T05:32:07.503124Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.20960569381713867, "adapter_response": {}, "message": "Database Error in model stg_orders (models/staging/stg_orders.sql)\n  091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.\n  compiled code at target/run/live_c360/models/staging/stg_orders.sql", "failures": null, "unique_id": "model.live_c360.stg_orders", "compiled": true, "compiled_code": "\n\n-- Staging model for live orders data\n-- Cleans and standardizes raw order data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_orders\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as order_id,\n        user_id,\n        \n        -- Transaction details\n        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,\n        item_count,\n        amount,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null order IDs\n      and user_id is not null  -- Filter out orders without user IDs\n      and amount > 0  -- Filter out invalid amounts\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        round(amount / item_count, 2) as avg_item_price,\n        \n        -- Date extractions\n        date(transaction_date) as transaction_date_only,\n        extract(year from transaction_date) as transaction_year,\n        extract(month from transaction_date) as transaction_month,\n        extract(day from transaction_date) as transaction_day,\n        extract(hour from transaction_date) as transaction_hour,\n        dayname(transaction_date) as transaction_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n            when extract(hour from transaction_date) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Order size segments\n        case \n            when amount < 25 then 'Small'\n            when amount < 75 then 'Medium'\n            when amount < 150 then 'Large'\n            else 'Extra Large'\n        end as order_size_segment,\n        \n        -- Item quantity segments\n        case \n            when item_count = 1 then 'Single Item'\n            when item_count <= 3 then 'Few Items'\n            when item_count <= 5 then 'Multiple Items'\n            else 'Bulk Order'\n        end as quantity_segment,\n        \n        -- Data quality flags\n        case \n            when transaction_date > current_timestamp() then true\n            else false\n        end as has_future_transaction_date,\n        \n        case \n            when avg_item_price > 500 then true  -- Flag unusually expensive items\n            else false\n        end as has_high_item_price,\n        \n        -- Recency\n        datediff('day', transaction_date, current_timestamp()) as days_since_transaction\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_orders", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:06.539133Z", "completed_at": "2025-06-04T05:32:06.582204Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:06.630552Z", "completed_at": "2025-06-04T05:32:07.731944Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.2046635150909424, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccc2c-3204-7eab-0002-4ad6000611ca"}, "message": "SUCCESS 1", "failures": null, "unique_id": "model.live_c360.dbt_test_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: dbt Test Results Monitoring\n-- Tracks dbt test execution and results\n\nwith test_metadata as (\n    -- This would typically come from dbt artifacts or a custom logging solution\n    -- For now, we'll create a mock structure based on our known tests\n    select 'source_not_null_live_external_ext_live_users_id' as test_name,\n           'source' as test_type,\n           'ext_live_users' as model_name,\n           'id' as column_name,\n           'not_null' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_unique_live_external_ext_live_users_id' as test_name,\n           'source' as test_type,\n           'ext_live_users' as model_name,\n           'id' as column_name,\n           'unique' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_not_null_live_external_ext_live_orders_id' as test_name,\n           'source' as test_type,\n           'ext_live_orders' as model_name,\n           'id' as column_name,\n           'not_null' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,\n           'source' as test_type,\n           'ext_live_orders' as model_name,\n           'user_id' as column_name,\n           'relationships' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,\n           'source' as test_type,\n           'ext_live_events' as model_name,\n           'action' as column_name,\n           'accepted_values' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n),\n\ntest_summary as (\n    select\n        date(execution_time) as test_date,\n        test_type,\n        model_name,\n        test_category,\n        \n        -- Test counts\n        count(*) as total_tests,\n        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,\n        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,\n        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,\n        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,\n        \n        -- Failure details\n        sum(failures) as total_failures,\n        max(execution_time) as latest_execution,\n        \n        -- Calculate pass rate\n        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate\n        \n    from test_metadata\n    group by date(execution_time), test_type, model_name, test_category\n),\n\nmodel_coverage as (\n    -- Calculate test coverage per model\n    select\n        model_name,\n        count(distinct test_category) as test_types_covered,\n        count(*) as total_tests_on_model,\n        \n        -- Check for essential test coverage\n        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,\n        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,\n        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,\n        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests\n        \n    from test_metadata\n    group by model_name\n),\n\nhealth_scores as (\n    select\n        ts.*,\n        mc.test_types_covered,\n        mc.total_tests_on_model,\n        mc.has_not_null_tests,\n        mc.has_unique_tests,\n        mc.has_relationship_tests,\n        mc.has_accepted_values_tests,\n        \n        -- Test reliability score\n        case \n            when pass_rate = 100 then 100\n            when pass_rate >= 95 then 90\n            when pass_rate >= 90 then 80\n            when pass_rate >= 80 then 70\n            else 50\n        end as reliability_score,\n        \n        -- Test coverage score\n        case \n            when test_types_covered >= 4 then 100\n            when test_types_covered >= 3 then 80\n            when test_types_covered >= 2 then 60\n            when test_types_covered >= 1 then 40\n            else 20\n        end as coverage_score\n        \n    from test_summary ts\n    left join model_coverage mc on ts.model_name = mc.model_name\n)\n\nselect\n    test_date,\n    test_type,\n    model_name,\n    test_category,\n    total_tests,\n    passed_tests,\n    failed_tests,\n    warning_tests,\n    skipped_tests,\n    total_failures,\n    pass_rate,\n    test_types_covered,\n    total_tests_on_model,\n    has_not_null_tests,\n    has_unique_tests,\n    has_relationship_tests,\n    has_accepted_values_tests,\n    reliability_score,\n    coverage_score,\n    \n    -- Overall test health score\n    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,\n    \n    -- Test status\n    case \n        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'\n        when pass_rate >= 95 and coverage_score >= 60 then 'Good'\n        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'\n        when pass_rate >= 80 then 'Poor'\n        else 'Critical'\n    end as test_health_status,\n    \n    latest_execution,\n    current_timestamp() as health_check_timestamp\n    \nfrom health_scores\norder by test_date desc, model_name, test_category", "relation_name": "MYDB.LIVE_DATA.dbt_test_health", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-1 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.fact_orders", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA.fact_orders", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-1 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.mv_fact_orders", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:07.523593Z", "completed_at": "2025-06-04T05:32:07.540714Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:07.542217Z", "completed_at": "2025-06-04T05:32:07.791637Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.27582788467407227, "adapter_response": {}, "message": "Database Error in model stg_users (models/staging/stg_users.sql)\n  091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.\n  compiled code at target/run/live_c360/models/staging/stg_users.sql", "failures": null, "unique_id": "model.live_c360.stg_users", "compiled": true, "compiled_code": "\n\n-- Staging model for live users data\n-- Cleans and standardizes raw user data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_users\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as user_id,\n        \n        -- Personal information (anonymized)\n        sha1(email) as email_hash,\n        initcap(trim(firstname)) as firstname,\n        initcap(trim(lastname)) as lastname,\n        trim(address) as address,\n        upper(trim(canal)) as acquisition_channel,\n        upper(trim(country)) as country,\n        \n        -- Dates\n        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,\n        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,\n        \n        -- Demographics\n        case \n            when gender = 0 then 'F'\n            when gender = 1 then 'M'\n            else 'Unknown'\n        end as gender,\n        age_group,\n        \n        -- Behavioral flags\n        churn as is_churned,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null user IDs\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        datediff('day', creation_date, current_timestamp()) as days_since_creation,\n        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,\n        \n        -- Data quality flags\n        case \n            when creation_date > current_timestamp() then true\n            else false\n        end as has_future_creation_date,\n        \n        case \n            when last_activity_date < creation_date then true\n            else false\n        end as has_invalid_activity_date,\n        \n        -- Segmentation\n        case \n            when days_since_last_activity <= 7 then 'Active'\n            when days_since_last_activity <= 30 then 'Recent'\n            when days_since_last_activity <= 90 then 'Dormant'\n            else 'Inactive'\n        end as activity_segment\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_users", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-1 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.dim_users", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA.dim_users", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-4 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.dim_users_scd2", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA.dim_users_scd2", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-1 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.data_quality_health", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA.data_quality_health", "batch_results": null}, {"status": "skipped", "timing": [], "thread_id": "Thread-4 (worker)", "execution_time": 0, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.etl_health_dashboard", "compiled": false, "compiled_code": null, "relation_name": "MYDB.LIVE_DATA.etl_health_dashboard", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:06.555132Z", "completed_at": "2025-06-04T05:32:06.574686Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:06.576924Z", "completed_at": "2025-06-04T05:32:09.101654Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 2.5713412761688232, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccc2c-3204-7f80-0002-4ad600067102"}, "message": "SUCCESS 1", "failures": null, "unique_id": "model.live_c360.query_history_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Query History Monitoring\n-- Tracks query performance, failures, and runtime metrics\n\nwith query_history as (\n    select\n        query_id,\n        query_text,\n        database_name,\n        schema_name,\n        query_type,\n        session_id,\n        user_name,\n        role_name,\n        warehouse_name,\n        warehouse_size,\n        warehouse_type,\n        cluster_number,\n        query_tag,\n        execution_status,\n        error_code,\n        error_message,\n        start_time,\n        end_time,\n        total_elapsed_time,\n        bytes_scanned,\n        percentage_scanned_from_cache,\n        bytes_written,\n        bytes_written_to_result,\n        bytes_read_from_result,\n        rows_produced,\n        rows_inserted,\n        rows_updated,\n        rows_deleted,\n        rows_unloaded,\n        bytes_deleted,\n        partitions_scanned,\n        partitions_total,\n        bytes_spilled_to_local_storage,\n        bytes_spilled_to_remote_storage,\n        bytes_sent_over_the_network,\n        compilation_time,\n        execution_time,\n        queued_provisioning_time,\n        queued_repair_time,\n        queued_overload_time,\n        transaction_blocked_time,\n        outbound_data_transfer_cloud,\n        outbound_data_transfer_region,\n        outbound_data_transfer_bytes,\n        inbound_data_transfer_cloud,\n        inbound_data_transfer_region,\n        inbound_data_transfer_bytes,\n        list_external_files_time,\n        credits_used_cloud_services\n    from snowflake.account_usage.query_history\n    where start_time >= current_date - 7  -- Last 7 days\n      and database_name = 'MYDB'  -- Focus on our database\n),\n\netl_queries as (\n    select\n        *,\n        -- Categorize queries\n        case \n            when query_text ilike '%dbt%' then 'dbt'\n            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'\n            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'\n            when query_text ilike '%copy into%' then 'Data_Load'\n            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'\n            else 'Other'\n        end as query_category,\n        \n        -- Performance flags\n        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes\n        case when execution_status = 'FAIL' then true else false end as is_failed,\n        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,\n        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,\n        \n        -- Time segments\n        date(start_time) as execution_date,\n        extract(hour from start_time) as execution_hour,\n        case \n            when extract(hour from start_time) between 6 and 11 then 'Morning'\n            when extract(hour from start_time) between 12 and 17 then 'Afternoon'\n            when extract(hour from start_time) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_segment\n        \n    from query_history\n),\n\nhealth_metrics as (\n    select\n        execution_date,\n        query_category,\n        time_segment,\n        warehouse_name,\n        \n        -- Count metrics\n        count(*) as total_queries,\n        sum(case when is_failed then 1 else 0 end) as failed_queries,\n        sum(case when is_long_running then 1 else 0 end) as long_running_queries,\n        sum(case when has_spill then 1 else 0 end) as queries_with_spill,\n        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,\n        \n        -- Performance metrics\n        avg(total_elapsed_time) as avg_elapsed_time_ms,\n        max(total_elapsed_time) as max_elapsed_time_ms,\n        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,\n        \n        -- Data metrics\n        sum(bytes_scanned) as total_bytes_scanned,\n        sum(rows_produced) as total_rows_produced,\n        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,\n        \n        -- Cost metrics\n        sum(credits_used_cloud_services) as total_credits_used,\n        \n        -- Latest execution\n        max(start_time) as latest_execution_time\n        \n    from etl_queries\n    group by execution_date, query_category, time_segment, warehouse_name\n)\n\nselect\n    *,\n    -- Health scores (0-100)\n    case \n        when failed_queries = 0 then 100\n        when failed_queries::float / total_queries <= 0.01 then 95\n        when failed_queries::float / total_queries <= 0.05 then 80\n        when failed_queries::float / total_queries <= 0.10 then 60\n        else 30\n    end as reliability_score,\n    \n    case \n        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds\n        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes\n        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes\n        else 30\n    end as performance_score,\n    \n    case \n        when avg_cache_hit_rate >= 80 then 100\n        when avg_cache_hit_rate >= 60 then 80\n        when avg_cache_hit_rate >= 40 then 60\n        else 30\n    end as efficiency_score,\n    \n    -- Overall health score\n    round((\n        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +\n        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +\n        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3\n    ), 0) as overall_health_score,\n    \n    current_timestamp() as health_check_timestamp\n    \nfrom health_metrics\norder by execution_date desc, query_category, warehouse_name", "relation_name": "MYDB.LIVE_DATA.query_history_health", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T05:32:06.549771Z", "completed_at": "2025-06-04T05:32:06.576237Z"}, {"name": "execute", "started_at": "2025-06-04T05:32:06.613975Z", "completed_at": "2025-06-04T05:32:09.111321Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 2.582422971725464, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccc2c-3204-7f80-0002-4ad600067106"}, "message": "SUCCESS 1", "failures": null, "unique_id": "model.live_c360.pipeline_runtime_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Pipeline Runtime Monitoring\n-- Tracks pipeline execution times, success rates, and performance trends\n\nwith dbt_run_history as (\n    -- Extract dbt-specific queries from query history\n    select\n        query_id,\n        query_text,\n        start_time,\n        end_time,\n        total_elapsed_time,\n        execution_status,\n        error_message,\n        warehouse_name,\n        user_name,\n        \n        -- Extract model name from dbt queries\n        case \n            when query_text ilike '%create or replace%view%stg_%' then \n                regexp_substr(query_text, 'view\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%create or replace%table%dim_%' then \n                regexp_substr(query_text, 'table\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%create or replace%table%fact_%' then \n                regexp_substr(query_text, 'table\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%merge into%' then \n                regexp_substr(query_text, 'merge\\\\s+into\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            else 'unknown'\n        end as model_name,\n        \n        -- Categorize model types\n        case \n            when query_text ilike '%stg_%' then 'staging'\n            when query_text ilike '%dim_%' then 'dimension'\n            when query_text ilike '%fact_%' then 'fact'\n            when query_text ilike '%mv_%' then 'materialized_view'\n            else 'other'\n        end as model_type,\n        \n        date(start_time) as execution_date,\n        extract(hour from start_time) as execution_hour\n        \n    from snowflake.account_usage.query_history\n    where start_time >= current_date - 7\n      and database_name = 'MYDB'\n      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')\n      and query_text not ilike '%information_schema%'\n      and query_text not ilike '%show%'\n),\n\npipeline_runs as (\n    -- Group queries into pipeline runs (by date and hour)\n    select\n        execution_date,\n        execution_hour,\n        model_type,\n        warehouse_name,\n        \n        -- Run metrics\n        count(*) as models_executed,\n        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,\n        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,\n        \n        -- Timing metrics\n        sum(total_elapsed_time) as total_pipeline_time_ms,\n        avg(total_elapsed_time) as avg_model_time_ms,\n        max(total_elapsed_time) as max_model_time_ms,\n        min(start_time) as pipeline_start_time,\n        max(end_time) as pipeline_end_time,\n        \n        -- Calculate actual pipeline duration\n        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,\n        \n        -- Error details\n        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages\n        \n    from dbt_run_history\n    where model_name != 'unknown'\n    group by execution_date, execution_hour, model_type, warehouse_name\n),\n\nmodel_performance as (\n    -- Individual model performance tracking\n    select\n        model_name,\n        model_type,\n        execution_date,\n        \n        -- Performance metrics\n        count(*) as execution_count,\n        avg(total_elapsed_time) as avg_execution_time_ms,\n        max(total_elapsed_time) as max_execution_time_ms,\n        min(total_elapsed_time) as min_execution_time_ms,\n        stddev(total_elapsed_time) as stddev_execution_time_ms,\n        \n        -- Success rate\n        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,\n        \n        -- Latest execution\n        max(start_time) as latest_execution_time,\n        \n        -- Performance trend (compare to previous day)\n        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time\n        \n    from dbt_run_history\n    where model_name != 'unknown'\n    group by model_name, model_type, execution_date\n),\n\nhealth_metrics as (\n    select\n        pr.*,\n        \n        -- Success rate\n        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,\n        \n        -- Performance scores\n        case \n            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds\n            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes\n            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes\n            else 30\n        end as performance_score,\n        \n        case \n            when pipeline_success_rate = 100 then 100\n            when pipeline_success_rate >= 95 then 90\n            when pipeline_success_rate >= 90 then 80\n            when pipeline_success_rate >= 80 then 70\n            else 50\n        end as reliability_score,\n        \n        -- Duration score (based on total pipeline time)\n        case \n            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes\n            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes\n            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes\n            else 30\n        end as duration_score\n        \n    from pipeline_runs pr\n)\n\nselect\n    execution_date,\n    execution_hour,\n    model_type,\n    warehouse_name,\n    models_executed,\n    successful_models,\n    failed_models,\n    pipeline_success_rate,\n    total_pipeline_time_ms,\n    avg_model_time_ms,\n    max_model_time_ms,\n    pipeline_duration_ms,\n    pipeline_start_time,\n    pipeline_end_time,\n    performance_score,\n    reliability_score,\n    duration_score,\n    \n    -- Overall pipeline health score\n    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,\n    \n    -- Pipeline status\n    case \n        when failed_models = 0 and performance_score >= 80 then 'Healthy'\n        when failed_models = 0 and performance_score >= 60 then 'Good'\n        when failed_models <= 1 and performance_score >= 60 then 'Fair'\n        when failed_models <= 2 then 'Poor'\n        else 'Critical'\n    end as pipeline_status,\n    \n    error_messages,\n    current_timestamp() as health_check_timestamp\n    \nfrom health_metrics\norder by execution_date desc, execution_hour desc, model_type", "relation_name": "MYDB.LIVE_DATA.pipeline_runtime_health", "batch_results": null}], "elapsed_time": 4.4562883377075195, "args": {"state_modified_compare_vars": false, "log_level_file": "debug", "warn_error_options": {"include": [], "exclude": []}, "defer": false, "cache_selected_only": false, "introspect": true, "project_dir": "/opt/airflow/workspace/dbt_live", "require_yaml_configuration_for_mf_time_spines": false, "macro_debugging": false, "require_resource_names_without_spaces": false, "populate_cache": true, "use_colors": true, "select": [], "write_json": true, "log_format_file": "debug", "partial_parse_file_diff": true, "log_path": "/opt/airflow/workspace/dbt_live/logs", "exclude": [], "indirect_selection": "eager", "show_resource_report": false, "log_level": "info", "require_nested_cumulative_type_params": false, "partial_parse": true, "print": true, "send_anonymous_usage_stats": true, "source_freshness_run_project_hooks": false, "vars": {}, "favor_state": false, "require_explicit_package_overrides_for_builtin_materializations": true, "empty": false, "invocation_command": "dbt celery worker", "version_check": true, "skip_nodes_if_on_run_start_fails": false, "state_modified_compare_more_unrendered_values": false, "which": "run", "printer_width": 80, "profiles_dir": "/opt/airflow/workspace/dbt_live", "require_batched_execution_for_custom_microbatch_strategy": false, "static_parser": true, "quiet": false, "use_colors_file": true, "log_format": "default", "log_file_max_bytes": 10485760, "strict_mode": false}}