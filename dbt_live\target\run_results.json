{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T04:53:56.130889Z", "invocation_id": "38b832d6-a7d7-4c6c-ae46-f12ea01605bb", "env": {}}, "results": [{"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T04:53:53.814192Z", "completed_at": "2025-06-04T04:53:53.847784Z"}, {"name": "execute", "started_at": "2025-06-04T04:53:53.847784Z", "completed_at": "2025-06-04T04:53:54.993153Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.1923367977142334, "adapter_response": {}, "message": "Database Error in model stg_events (models\\staging\\stg_events.sql)\n  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.\n  compiled code at target\\run\\live_c360\\models\\staging\\stg_events.sql", "failures": null, "unique_id": "model.live_c360.stg_events", "compiled": true, "compiled_code": "\n\n-- Staging model for live events data\n-- Cleans and standardizes raw event data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_events\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        event_id,\n        user_id,\n        session_id,\n        \n        -- Event details\n        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,\n        lower(trim(platform)) as platform,\n        lower(trim(action)) as action,\n        trim(url) as url,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where event_id is not null  -- Filter out any null event IDs\n      and user_id is not null   -- Filter out events without user IDs\n),\n\nurl_parsed as (\n    select\n        *,\n        -- URL parsing\n        case \n            when url like '%/product/%' then 'Product Page'\n            when url like '%/cart%' then 'Cart'\n            when url like '%/checkout%' then 'Checkout'\n            when url like '%/search%' then 'Search'\n            when url like '%/category/%' then 'Category'\n            when url like '%/home%' or url = 'https://example.com/' then 'Home'\n            else 'Other'\n        end as page_type,\n        \n        -- Extract path from URL\n        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path\n        \n    from cleaned_data\n),\n\nfinal as (\n    select\n        *,\n        -- Date extractions\n        date(event_timestamp) as event_date,\n        extract(year from event_timestamp) as event_year,\n        extract(month from event_timestamp) as event_month,\n        extract(day from event_timestamp) as event_day,\n        extract(hour from event_timestamp) as event_hour,\n        dayname(event_timestamp) as event_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'\n            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'\n            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Platform standardization\n        case \n            when platform in ('ios', 'android') then 'Mobile'\n            when platform = 'web' then 'Web'\n            else 'Unknown'\n        end as device_category,\n        \n        -- Action categorization\n        case \n            when action in ('view', 'click') then 'Engagement'\n            when action = 'log' then 'Authentication'\n            when action = 'purchase' then 'Conversion'\n            else 'Other'\n        end as action_category,\n        \n        -- Funnel stage\n        case \n            when action = 'view' and page_type = 'Home' then 'Awareness'\n            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'\n            when action = 'view' and page_type = 'Product Page' then 'Consideration'\n            when action = 'click' and page_type = 'Cart' then 'Intent'\n            when action = 'purchase' then 'Purchase'\n            else 'Other'\n        end as funnel_stage,\n        \n        -- Data quality flags\n        case \n            when event_timestamp > current_timestamp() then true\n            else false\n        end as has_future_event_timestamp,\n        \n        -- Recency\n        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,\n        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,\n        datediff('day', event_timestamp, current_timestamp()) as days_since_event\n        \n    from url_parsed\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_events", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T04:53:53.840641Z", "completed_at": "2025-06-04T04:53:53.873573Z"}, {"name": "execute", "started_at": "2025-06-04T04:53:53.924899Z", "completed_at": "2025-06-04T04:53:54.996520Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.1923367977142334, "adapter_response": {}, "message": "Database Error in model stg_users (models\\staging\\stg_users.sql)\n  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.\n  compiled code at target\\run\\live_c360\\models\\staging\\stg_users.sql", "failures": null, "unique_id": "model.live_c360.stg_users", "compiled": true, "compiled_code": "\n\n-- Staging model for live users data\n-- Cleans and standardizes raw user data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_users\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as user_id,\n        \n        -- Personal information (anonymized)\n        sha1(email) as email_hash,\n        initcap(trim(firstname)) as firstname,\n        initcap(trim(lastname)) as lastname,\n        trim(address) as address,\n        upper(trim(canal)) as acquisition_channel,\n        upper(trim(country)) as country,\n        \n        -- Dates\n        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,\n        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,\n        \n        -- Demographics\n        case \n            when gender = 0 then 'F'\n            when gender = 1 then 'M'\n            else 'Unknown'\n        end as gender,\n        age_group,\n        \n        -- Behavioral flags\n        churn as is_churned,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null user IDs\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        datediff('day', creation_date, current_timestamp()) as days_since_creation,\n        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,\n        \n        -- Data quality flags\n        case \n            when creation_date > current_timestamp() then true\n            else false\n        end as has_future_creation_date,\n        \n        case \n            when last_activity_date < creation_date then true\n            else false\n        end as has_invalid_activity_date,\n        \n        -- Segmentation\n        case \n            when days_since_last_activity <= 7 then 'Active'\n            when days_since_last_activity <= 30 then 'Recent'\n            when days_since_last_activity <= 90 then 'Dormant'\n            else 'Inactive'\n        end as activity_segment\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_users", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T04:53:53.839584Z", "completed_at": "2025-06-04T04:53:53.873573Z"}, {"name": "execute", "started_at": "2025-06-04T04:53:53.873573Z", "completed_at": "2025-06-04T04:53:54.994872Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.1923367977142334, "adapter_response": {}, "message": "Database Error in model stg_orders (models\\staging\\stg_orders.sql)\n  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.\n  compiled code at target\\run\\live_c360\\models\\staging\\stg_orders.sql", "failures": null, "unique_id": "model.live_c360.stg_orders", "compiled": true, "compiled_code": "\n\n-- Staging model for live orders data\n-- Cleans and standardizes raw order data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_orders\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as order_id,\n        user_id,\n        \n        -- Transaction details\n        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,\n        item_count,\n        amount,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null order IDs\n      and user_id is not null  -- Filter out orders without user IDs\n      and amount > 0  -- Filter out invalid amounts\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        round(amount / item_count, 2) as avg_item_price,\n        \n        -- Date extractions\n        date(transaction_date) as transaction_date_only,\n        extract(year from transaction_date) as transaction_year,\n        extract(month from transaction_date) as transaction_month,\n        extract(day from transaction_date) as transaction_day,\n        extract(hour from transaction_date) as transaction_hour,\n        dayname(transaction_date) as transaction_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n            when extract(hour from transaction_date) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Order size segments\n        case \n            when amount < 25 then 'Small'\n            when amount < 75 then 'Medium'\n            when amount < 150 then 'Large'\n            else 'Extra Large'\n        end as order_size_segment,\n        \n        -- Item quantity segments\n        case \n            when item_count = 1 then 'Single Item'\n            when item_count <= 3 then 'Few Items'\n            when item_count <= 5 then 'Multiple Items'\n            else 'Bulk Order'\n        end as quantity_segment,\n        \n        -- Data quality flags\n        case \n            when transaction_date > current_timestamp() then true\n            else false\n        end as has_future_transaction_date,\n        \n        case \n            when avg_item_price > 500 then true  -- Flag unusually expensive items\n            else false\n        end as has_high_item_price,\n        \n        -- Recency\n        datediff('day', transaction_date, current_timestamp()) as days_since_transaction\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_orders", "batch_results": null}], "elapsed_time": 5.983428478240967, "args": {"partial_parse_file_diff": true, "quiet": false, "skip_nodes_if_on_run_start_fails": false, "state_modified_compare_more_unrendered_values": false, "which": "run", "profiles_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "log_format": "default", "require_yaml_configuration_for_mf_time_spines": false, "strict_mode": false, "indirect_selection": "eager", "state_modified_compare_vars": false, "warn_error_options": {"include": [], "exclude": []}, "use_colors_file": true, "empty": false, "defer": false, "partial_parse": true, "printer_width": 80, "log_format_file": "debug", "exclude": [], "require_nested_cumulative_type_params": false, "source_freshness_run_project_hooks": false, "favor_state": false, "require_batched_execution_for_custom_microbatch_strategy": false, "log_file_max_bytes": 10485760, "introspect": true, "show_resource_report": false, "static_parser": true, "populate_cache": true, "log_path": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs", "invocation_command": "dbt run --models staging", "require_resource_names_without_spaces": false, "version_check": true, "cache_selected_only": false, "require_explicit_package_overrides_for_builtin_materializations": true, "select": ["staging"], "write_json": true, "send_anonymous_usage_stats": true, "use_colors": true, "vars": {}, "print": true, "log_level_file": "debug", "macro_debugging": false, "project_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "log_level": "info"}}