{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T02:52:10.230124Z", "invocation_id": "3a5ca569-63a5-45f4-bcf7-436afe4ec72d", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.506249Z", "completed_at": "2025-06-04T02:52:09.538695Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.538695Z", "completed_at": "2025-06-04T02:52:09.538695Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.0448606014251709, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.dbt_test_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: dbt Test Results Monitoring\n-- Tracks dbt test execution and results\n\nwith test_metadata as (\n    -- This would typically come from dbt artifacts or a custom logging solution\n    -- For now, we'll create a mock structure based on our known tests\n    select 'source_not_null_live_external_ext_live_users_id' as test_name,\n           'source' as test_type,\n           'ext_live_users' as model_name,\n           'id' as column_name,\n           'not_null' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_unique_live_external_ext_live_users_id' as test_name,\n           'source' as test_type,\n           'ext_live_users' as model_name,\n           'id' as column_name,\n           'unique' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_not_null_live_external_ext_live_orders_id' as test_name,\n           'source' as test_type,\n           'ext_live_orders' as model_name,\n           'id' as column_name,\n           'not_null' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,\n           'source' as test_type,\n           'ext_live_orders' as model_name,\n           'user_id' as column_name,\n           'relationships' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n    \n    union all\n    \n    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,\n           'source' as test_type,\n           'ext_live_events' as model_name,\n           'action' as column_name,\n           'accepted_values' as test_category,\n           'pass' as status,\n           0 as failures,\n           current_timestamp() - interval '1 hour' as execution_time\n),\n\ntest_summary as (\n    select\n        date(execution_time) as test_date,\n        test_type,\n        model_name,\n        test_category,\n        \n        -- Test counts\n        count(*) as total_tests,\n        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,\n        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,\n        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,\n        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,\n        \n        -- Failure details\n        sum(failures) as total_failures,\n        max(execution_time) as latest_execution,\n        \n        -- Calculate pass rate\n        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate\n        \n    from test_metadata\n    group by date(execution_time), test_type, model_name, test_category\n),\n\nmodel_coverage as (\n    -- Calculate test coverage per model\n    select\n        model_name,\n        count(distinct test_category) as test_types_covered,\n        count(*) as total_tests_on_model,\n        \n        -- Check for essential test coverage\n        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,\n        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,\n        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,\n        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests\n        \n    from test_metadata\n    group by model_name\n),\n\nhealth_scores as (\n    select\n        ts.*,\n        mc.test_types_covered,\n        mc.total_tests_on_model,\n        mc.has_not_null_tests,\n        mc.has_unique_tests,\n        mc.has_relationship_tests,\n        mc.has_accepted_values_tests,\n        \n        -- Test reliability score\n        case \n            when pass_rate = 100 then 100\n            when pass_rate >= 95 then 90\n            when pass_rate >= 90 then 80\n            when pass_rate >= 80 then 70\n            else 50\n        end as reliability_score,\n        \n        -- Test coverage score\n        case \n            when test_types_covered >= 4 then 100\n            when test_types_covered >= 3 then 80\n            when test_types_covered >= 2 then 60\n            when test_types_covered >= 1 then 40\n            else 20\n        end as coverage_score\n        \n    from test_summary ts\n    left join model_coverage mc on ts.model_name = mc.model_name\n)\n\nselect\n    test_date,\n    test_type,\n    model_name,\n    test_category,\n    total_tests,\n    passed_tests,\n    failed_tests,\n    warning_tests,\n    skipped_tests,\n    total_failures,\n    pass_rate,\n    test_types_covered,\n    total_tests_on_model,\n    has_not_null_tests,\n    has_unique_tests,\n    has_relationship_tests,\n    has_accepted_values_tests,\n    reliability_score,\n    coverage_score,\n    \n    -- Overall test health score\n    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,\n    \n    -- Test status\n    case \n        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'\n        when pass_rate >= 95 and coverage_score >= 60 then 'Good'\n        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'\n        when pass_rate >= 80 then 'Poor'\n        else 'Critical'\n    end as test_health_status,\n    \n    latest_execution,\n    current_timestamp() as health_check_timestamp\n    \nfrom health_scores\norder by test_date desc, model_name, test_category", "relation_name": "MYDB.LIVE_DATA.dbt_test_health", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.518459Z", "completed_at": "2025-06-04T02:52:09.538695Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.543431Z", "completed_at": "2025-06-04T02:52:09.543431Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.04959678649902344, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.pipeline_runtime_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Pipeline Runtime Monitoring\n-- Tracks pipeline execution times, success rates, and performance trends\n\nwith dbt_run_history as (\n    -- Extract dbt-specific queries from query history\n    select\n        query_id,\n        query_text,\n        start_time,\n        end_time,\n        total_elapsed_time,\n        execution_status,\n        error_message,\n        warehouse_name,\n        user_name,\n        \n        -- Extract model name from dbt queries\n        case \n            when query_text ilike '%create or replace%view%stg_%' then \n                regexp_substr(query_text, 'view\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%create or replace%table%dim_%' then \n                regexp_substr(query_text, 'table\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%create or replace%table%fact_%' then \n                regexp_substr(query_text, 'table\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            when query_text ilike '%merge into%' then \n                regexp_substr(query_text, 'merge\\\\s+into\\\\s+[^.]+\\\\.([^\\\\s]+)', 1, 1, 'i', 1)\n            else 'unknown'\n        end as model_name,\n        \n        -- Categorize model types\n        case \n            when query_text ilike '%stg_%' then 'staging'\n            when query_text ilike '%dim_%' then 'dimension'\n            when query_text ilike '%fact_%' then 'fact'\n            when query_text ilike '%mv_%' then 'materialized_view'\n            else 'other'\n        end as model_type,\n        \n        date(start_time) as execution_date,\n        extract(hour from start_time) as execution_hour\n        \n    from snowflake.account_usage.query_history\n    where start_time >= current_date - 7\n      and database_name = 'MYDB'\n      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')\n      and query_text not ilike '%information_schema%'\n      and query_text not ilike '%show%'\n),\n\npipeline_runs as (\n    -- Group queries into pipeline runs (by date and hour)\n    select\n        execution_date,\n        execution_hour,\n        model_type,\n        warehouse_name,\n        \n        -- Run metrics\n        count(*) as models_executed,\n        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,\n        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,\n        \n        -- Timing metrics\n        sum(total_elapsed_time) as total_pipeline_time_ms,\n        avg(total_elapsed_time) as avg_model_time_ms,\n        max(total_elapsed_time) as max_model_time_ms,\n        min(start_time) as pipeline_start_time,\n        max(end_time) as pipeline_end_time,\n        \n        -- Calculate actual pipeline duration\n        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,\n        \n        -- Error details\n        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages\n        \n    from dbt_run_history\n    where model_name != 'unknown'\n    group by execution_date, execution_hour, model_type, warehouse_name\n),\n\nmodel_performance as (\n    -- Individual model performance tracking\n    select\n        model_name,\n        model_type,\n        execution_date,\n        \n        -- Performance metrics\n        count(*) as execution_count,\n        avg(total_elapsed_time) as avg_execution_time_ms,\n        max(total_elapsed_time) as max_execution_time_ms,\n        min(total_elapsed_time) as min_execution_time_ms,\n        stddev(total_elapsed_time) as stddev_execution_time_ms,\n        \n        -- Success rate\n        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,\n        \n        -- Latest execution\n        max(start_time) as latest_execution_time,\n        \n        -- Performance trend (compare to previous day)\n        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time\n        \n    from dbt_run_history\n    where model_name != 'unknown'\n    group by model_name, model_type, execution_date\n),\n\nhealth_metrics as (\n    select\n        pr.*,\n        \n        -- Success rate\n        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,\n        \n        -- Performance scores\n        case \n            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds\n            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes\n            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes\n            else 30\n        end as performance_score,\n        \n        case \n            when pipeline_success_rate = 100 then 100\n            when pipeline_success_rate >= 95 then 90\n            when pipeline_success_rate >= 90 then 80\n            when pipeline_success_rate >= 80 then 70\n            else 50\n        end as reliability_score,\n        \n        -- Duration score (based on total pipeline time)\n        case \n            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes\n            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes\n            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes\n            else 30\n        end as duration_score\n        \n    from pipeline_runs pr\n)\n\nselect\n    execution_date,\n    execution_hour,\n    model_type,\n    warehouse_name,\n    models_executed,\n    successful_models,\n    failed_models,\n    pipeline_success_rate,\n    total_pipeline_time_ms,\n    avg_model_time_ms,\n    max_model_time_ms,\n    pipeline_duration_ms,\n    pipeline_start_time,\n    pipeline_end_time,\n    performance_score,\n    reliability_score,\n    duration_score,\n    \n    -- Overall pipeline health score\n    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,\n    \n    -- Pipeline status\n    case \n        when failed_models = 0 and performance_score >= 80 then 'Healthy'\n        when failed_models = 0 and performance_score >= 60 then 'Good'\n        when failed_models <= 1 and performance_score >= 60 then 'Fair'\n        when failed_models <= 2 then 'Poor'\n        else 'Critical'\n    end as pipeline_status,\n    \n    error_messages,\n    current_timestamp() as health_check_timestamp\n    \nfrom health_metrics\norder by execution_date desc, execution_hour desc, model_type", "relation_name": "MYDB.LIVE_DATA.pipeline_runtime_health", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.525932Z", "completed_at": "2025-06-04T02:52:09.538695Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.543955Z", "completed_at": "2025-06-04T02:52:09.543955Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.05012083053588867, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.query_history_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Query History Monitoring\n-- Tracks query performance, failures, and runtime metrics\n\nwith query_history as (\n    select\n        query_id,\n        query_text,\n        database_name,\n        schema_name,\n        query_type,\n        session_id,\n        user_name,\n        role_name,\n        warehouse_name,\n        warehouse_size,\n        warehouse_type,\n        cluster_number,\n        query_tag,\n        execution_status,\n        error_code,\n        error_message,\n        start_time,\n        end_time,\n        total_elapsed_time,\n        bytes_scanned,\n        percentage_scanned_from_cache,\n        bytes_written,\n        bytes_written_to_result,\n        bytes_read_from_result,\n        rows_produced,\n        rows_inserted,\n        rows_updated,\n        rows_deleted,\n        rows_unloaded,\n        bytes_deleted,\n        partitions_scanned,\n        partitions_total,\n        bytes_spilled_to_local_storage,\n        bytes_spilled_to_remote_storage,\n        bytes_sent_over_the_network,\n        compilation_time,\n        execution_time,\n        queued_provisioning_time,\n        queued_repair_time,\n        queued_overload_time,\n        transaction_blocked_time,\n        outbound_data_transfer_cloud,\n        outbound_data_transfer_region,\n        outbound_data_transfer_bytes,\n        inbound_data_transfer_cloud,\n        inbound_data_transfer_region,\n        inbound_data_transfer_bytes,\n        list_external_files_time,\n        credits_used_cloud_services\n    from snowflake.account_usage.query_history\n    where start_time >= current_date - 7  -- Last 7 days\n      and database_name = 'MYDB'  -- Focus on our database\n),\n\netl_queries as (\n    select\n        *,\n        -- Categorize queries\n        case \n            when query_text ilike '%dbt%' then 'dbt'\n            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'\n            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'\n            when query_text ilike '%copy into%' then 'Data_Load'\n            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'\n            else 'Other'\n        end as query_category,\n        \n        -- Performance flags\n        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes\n        case when execution_status = 'FAIL' then true else false end as is_failed,\n        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,\n        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,\n        \n        -- Time segments\n        date(start_time) as execution_date,\n        extract(hour from start_time) as execution_hour,\n        case \n            when extract(hour from start_time) between 6 and 11 then 'Morning'\n            when extract(hour from start_time) between 12 and 17 then 'Afternoon'\n            when extract(hour from start_time) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_segment\n        \n    from query_history\n),\n\nhealth_metrics as (\n    select\n        execution_date,\n        query_category,\n        time_segment,\n        warehouse_name,\n        \n        -- Count metrics\n        count(*) as total_queries,\n        sum(case when is_failed then 1 else 0 end) as failed_queries,\n        sum(case when is_long_running then 1 else 0 end) as long_running_queries,\n        sum(case when has_spill then 1 else 0 end) as queries_with_spill,\n        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,\n        \n        -- Performance metrics\n        avg(total_elapsed_time) as avg_elapsed_time_ms,\n        max(total_elapsed_time) as max_elapsed_time_ms,\n        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,\n        \n        -- Data metrics\n        sum(bytes_scanned) as total_bytes_scanned,\n        sum(rows_produced) as total_rows_produced,\n        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,\n        \n        -- Cost metrics\n        sum(credits_used_cloud_services) as total_credits_used,\n        \n        -- Latest execution\n        max(start_time) as latest_execution_time\n        \n    from etl_queries\n    group by execution_date, query_category, time_segment, warehouse_name\n)\n\nselect\n    *,\n    -- Health scores (0-100)\n    case \n        when failed_queries = 0 then 100\n        when failed_queries::float / total_queries <= 0.01 then 95\n        when failed_queries::float / total_queries <= 0.05 then 80\n        when failed_queries::float / total_queries <= 0.10 then 60\n        else 30\n    end as reliability_score,\n    \n    case \n        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds\n        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes\n        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes\n        else 30\n    end as performance_score,\n    \n    case \n        when avg_cache_hit_rate >= 80 then 100\n        when avg_cache_hit_rate >= 60 then 80\n        when avg_cache_hit_rate >= 40 then 60\n        else 30\n    end as efficiency_score,\n    \n    -- Overall health score\n    round((\n        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +\n        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +\n        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3\n    ), 0) as overall_health_score,\n    \n    current_timestamp() as health_check_timestamp\n    \nfrom health_metrics\norder by execution_date desc, query_category, warehouse_name", "relation_name": "MYDB.LIVE_DATA.query_history_health", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.530452Z", "completed_at": "2025-06-04T02:52:09.542845Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.543955Z", "completed_at": "2025-06-04T02:52:09.543955Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.038779497146606445, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.stg_events", "compiled": true, "compiled_code": "\n\n-- Staging model for live events data\n-- Cleans and standardizes raw event data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_events\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        event_id,\n        user_id,\n        session_id,\n        \n        -- Event details\n        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,\n        lower(trim(platform)) as platform,\n        lower(trim(action)) as action,\n        trim(url) as url,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where event_id is not null  -- Filter out any null event IDs\n      and user_id is not null   -- Filter out events without user IDs\n),\n\nurl_parsed as (\n    select\n        *,\n        -- URL parsing\n        case \n            when url like '%/product/%' then 'Product Page'\n            when url like '%/cart%' then 'Cart'\n            when url like '%/checkout%' then 'Checkout'\n            when url like '%/search%' then 'Search'\n            when url like '%/category/%' then 'Category'\n            when url like '%/home%' or url = 'https://example.com/' then 'Home'\n            else 'Other'\n        end as page_type,\n        \n        -- Extract path from URL\n        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path\n        \n    from cleaned_data\n),\n\nfinal as (\n    select\n        *,\n        -- Date extractions\n        date(event_timestamp) as event_date,\n        extract(year from event_timestamp) as event_year,\n        extract(month from event_timestamp) as event_month,\n        extract(day from event_timestamp) as event_day,\n        extract(hour from event_timestamp) as event_hour,\n        dayname(event_timestamp) as event_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'\n            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'\n            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Platform standardization\n        case \n            when platform in ('ios', 'android') then 'Mobile'\n            when platform = 'web' then 'Web'\n            else 'Unknown'\n        end as device_category,\n        \n        -- Action categorization\n        case \n            when action in ('view', 'click') then 'Engagement'\n            when action = 'log' then 'Authentication'\n            when action = 'purchase' then 'Conversion'\n            else 'Other'\n        end as action_category,\n        \n        -- Funnel stage\n        case \n            when action = 'view' and page_type = 'Home' then 'Awareness'\n            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'\n            when action = 'view' and page_type = 'Product Page' then 'Consideration'\n            when action = 'click' and page_type = 'Cart' then 'Intent'\n            when action = 'purchase' then 'Purchase'\n            else 'Other'\n        end as funnel_stage,\n        \n        -- Data quality flags\n        case \n            when event_timestamp > current_timestamp() then true\n            else false\n        end as has_future_event_timestamp,\n        \n        -- Recency\n        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,\n        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,\n        datediff('day', event_timestamp, current_timestamp()) as days_since_event\n        \n    from url_parsed\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_events", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.555312Z", "completed_at": "2025-06-04T02:52:09.581520Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.594120Z", "completed_at": "2025-06-04T02:52:09.594120Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.05016493797302246, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.stg_orders", "compiled": true, "compiled_code": "\n\n-- Staging model for live orders data\n-- Cleans and standardizes raw order data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_orders\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as order_id,\n        user_id,\n        \n        -- Transaction details\n        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,\n        item_count,\n        amount,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null order IDs\n      and user_id is not null  -- Filter out orders without user IDs\n      and amount > 0  -- Filter out invalid amounts\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        round(amount / item_count, 2) as avg_item_price,\n        \n        -- Date extractions\n        date(transaction_date) as transaction_date_only,\n        extract(year from transaction_date) as transaction_year,\n        extract(month from transaction_date) as transaction_month,\n        extract(day from transaction_date) as transaction_day,\n        extract(hour from transaction_date) as transaction_hour,\n        dayname(transaction_date) as transaction_day_name,\n        \n        -- Time-based segments\n        case \n            when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n            when extract(hour from transaction_date) between 18 and 22 then 'Evening'\n            else 'Night'\n        end as time_of_day_segment,\n        \n        -- Order size segments\n        case \n            when amount < 25 then 'Small'\n            when amount < 75 then 'Medium'\n            when amount < 150 then 'Large'\n            else 'Extra Large'\n        end as order_size_segment,\n        \n        -- Item quantity segments\n        case \n            when item_count = 1 then 'Single Item'\n            when item_count <= 3 then 'Few Items'\n            when item_count <= 5 then 'Multiple Items'\n            else 'Bulk Order'\n        end as quantity_segment,\n        \n        -- Data quality flags\n        case \n            when transaction_date > current_timestamp() then true\n            else false\n        end as has_future_transaction_date,\n        \n        case \n            when avg_item_price > 500 then true  -- Flag unusually expensive items\n            else false\n        end as has_high_item_price,\n        \n        -- Recency\n        datediff('day', transaction_date, current_timestamp()) as days_since_transaction\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_orders", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.563411Z", "completed_at": "2025-06-04T02:52:09.593614Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.594120Z", "completed_at": "2025-06-04T02:52:09.594120Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.05016493797302246, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.stg_users", "compiled": true, "compiled_code": "\n\n-- Staging model for live users data\n-- Cleans and standardizes raw user data from S3 external table\n\nwith source_data as (\n    select * from MYDB.LIVE_DATA.ext_live_users\n),\n\ncleaned_data as (\n    select\n        -- Primary key\n        id as user_id,\n        \n        -- Personal information (anonymized)\n        sha1(email) as email_hash,\n        initcap(trim(firstname)) as firstname,\n        initcap(trim(lastname)) as lastname,\n        trim(address) as address,\n        upper(trim(canal)) as acquisition_channel,\n        upper(trim(country)) as country,\n        \n        -- Dates\n        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,\n        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,\n        \n        -- Demographics\n        case \n            when gender = 0 then 'F'\n            when gender = 1 then 'M'\n            else 'Unknown'\n        end as gender,\n        age_group,\n        \n        -- Behavioral flags\n        churn as is_churned,\n        \n        -- Metadata\n        batch_id,\n        try_to_timestamp(generated_at) as generated_at,\n        current_timestamp() as processed_at\n        \n    from source_data\n    where id is not null  -- Filter out any null user IDs\n),\n\nfinal as (\n    select\n        *,\n        -- Derived fields\n        datediff('day', creation_date, current_timestamp()) as days_since_creation,\n        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,\n        \n        -- Data quality flags\n        case \n            when creation_date > current_timestamp() then true\n            else false\n        end as has_future_creation_date,\n        \n        case \n            when last_activity_date < creation_date then true\n            else false\n        end as has_invalid_activity_date,\n        \n        -- Segmentation\n        case \n            when days_since_last_activity <= 7 then 'Active'\n            when days_since_last_activity <= 30 then 'Recent'\n            when days_since_last_activity <= 90 then 'Dormant'\n            else 'Inactive'\n        end as activity_segment\n        \n    from cleaned_data\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.stg_users", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.568431Z", "completed_at": "2025-06-04T02:52:09.594120Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.594120Z", "completed_at": "2025-06-04T02:52:09.594120Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.05016493797302246, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0.50380c3cc8", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not amount > 0\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_038429b5ca81df79a58a497617b75c52", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.581520Z", "completed_at": "2025-06-04T02:52:09.594120Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.594120Z", "completed_at": "2025-06-04T02:52:09.594120Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.05016493797302246, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1.93502d99cd", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count >= 1\n    -- records with a value <= max_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count <= 100\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_2fa5c5eaa1e5c8f251141e01fd7779e8", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.605423Z", "completed_at": "2025-06-04T02:52:09.643746Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.655004Z", "completed_at": "2025-06-04T02:52:09.655004Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.06088399887084961, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase.6a0240ce21", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        action as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by action\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'view','click','log','purchase'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_e2a83a0f133a781a89c6730f46beefed", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.626377Z", "completed_at": "2025-06-04T02:52:09.655004Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.655004Z", "completed_at": "2025-06-04T02:52:09.655004Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.06088399887084961, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None.553832ca75", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        platform as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by platform\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'ios','android','web','None'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_255d50e75759bebae188ba25e47211e0", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.635976Z", "completed_at": "2025-06-04T02:52:09.655004Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.655004Z", "completed_at": "2025-06-04T02:52:09.655004Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.04958152770996094, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_event_id.ff0aeb9e92", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.648793Z", "completed_at": "2025-06-04T02:52:09.655004Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.655004Z", "completed_at": "2025-06-04T02:52:09.655004Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.04958152770996094, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_generated_at.96d76fb529", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_generated_at", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.669201Z", "completed_at": "2025-06-04T02:52:09.695155Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.696661Z", "completed_at": "2025-06-04T02:52:09.696661Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.0416562557220459, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_session_id.342957c8ec", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere session_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_session_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.674487Z", "completed_at": "2025-06-04T02:52:09.696661Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.696661Z", "completed_at": "2025-06-04T02:52:09.696661Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.032479286193847656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_user_id.f112ba0802", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_user_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.679037Z", "completed_at": "2025-06-04T02:52:09.696661Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.696661Z", "completed_at": "2025-06-04T02:52:09.696661Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.032479286193847656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_amount.679c5c05e3", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere amount is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_amount", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.688602Z", "completed_at": "2025-06-04T02:52:09.696661Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.705052Z", "completed_at": "2025-06-04T02:52:09.705052Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.03585028648376465, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_generated_at.a66c929306", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_generated_at", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.705052Z", "completed_at": "2025-06-04T02:52:09.729614Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.743408Z", "completed_at": "2025-06-04T02:52:09.743408Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.03835654258728027, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_id.43cabbf964", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.718366Z", "completed_at": "2025-06-04T02:52:09.744336Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.744336Z", "completed_at": "2025-06-04T02:52:09.744336Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.039284706115722656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_item_count.7dac40310e", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere item_count is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_item_count", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.728962Z", "completed_at": "2025-06-04T02:52:09.744336Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.744336Z", "completed_at": "2025-06-04T02:52:09.744336Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.039284706115722656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_user_id.129a7fc228", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_user_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.729614Z", "completed_at": "2025-06-04T02:52:09.744336Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.744336Z", "completed_at": "2025-06-04T02:52:09.744336Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.030716419219970703, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_email.d823bafd9b", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere email is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_email", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.755096Z", "completed_at": "2025-06-04T02:52:09.773469Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.794079Z", "completed_at": "2025-06-04T02:52:09.794079Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.049742937088012695, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_generated_at.60675c4e9c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_generated_at", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.763139Z", "completed_at": "2025-06-04T02:52:09.794079Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.794079Z", "completed_at": "2025-06-04T02:52:09.794079Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.049742937088012695, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_id.b0694be37c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.773469Z", "completed_at": "2025-06-04T02:52:09.794079Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.800149Z", "completed_at": "2025-06-04T02:52:09.800149Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.045052528381347656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_.ba15555d86", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_events\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.782960Z", "completed_at": "2025-06-04T02:52:09.794079Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.800149Z", "completed_at": "2025-06-04T02:52:09.800149Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.05015230178833008, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_.8d45efa758", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_orders\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.805249Z", "completed_at": "2025-06-04T02:52:09.836003Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.843986Z", "completed_at": "2025-06-04T02:52:09.843986Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.04383730888366699, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_unique_live_external_ext_live_events_event_id.eeb702f8fc", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    event_id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is not null\ngroup by event_id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.822262Z", "completed_at": "2025-06-04T02:52:09.843986Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.850532Z", "completed_at": "2025-06-04T02:52:09.850532Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.045282840728759766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_unique_live_external_ext_live_orders_id.82585e5b4d", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_orders_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.829995Z", "completed_at": "2025-06-04T02:52:09.843986Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.850532Z", "completed_at": "2025-06-04T02:52:09.850532Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.045282840728759766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.live_c360.source_unique_live_external_ext_live_users_id.4af1be4e1f", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_users_id", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.843213Z", "completed_at": "2025-06-04T02:52:09.850532Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.855179Z", "completed_at": "2025-06-04T02:52:09.855179Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.03657031059265137, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.fact_orders", "compiled": true, "compiled_code": "\n\nwith \n-- Get the 90th percentile order amount for high-value threshold\norder_thresholds as (\n    select \n        percentile_cont(0.9) within group (order by amount) as high_value_threshold\n    from MYDB.LIVE_DATA.stg_orders\n    where amount is not null\n),\n\n-- Get basic order metrics\norder_metrics as (\n    select\n        -- Primary key and foreign key\n        order_id,\n        user_id,\n        \n        -- Core order details\n        transaction_date,\n        item_count,\n        amount as order_amount,\n        \n        -- Basic metrics\n        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,\n        \n        -- Time since last order\n        datediff(\n            'day',\n            lag(transaction_date) over (partition by user_id order by transaction_date),\n            transaction_date\n        ) as days_since_previous_order,\n        \n        -- Running totals\n        sum(amount) over (order by transaction_date) as running_total_amount,\n        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count\n        \n    from MYDB.LIVE_DATA.stg_orders\n    where transaction_date is not null\n    qualify row_number() over (partition by order_id order by generated_at desc) = 1\n)\n\n-- Final select with basic segmentation\nselect\n    om.*,\n    \n    -- Simple customer type\n    case \n        when user_order_sequence = 1 then 'New Customer'\n        when days_since_previous_order is null then 'New Customer'\n        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'\n        else 'Reactivated Customer'\n    end as customer_type,\n    \n    -- High value flag (using pre-calculated threshold)\n    case \n        when order_amount > (select high_value_threshold from order_thresholds) then true\n        else false\n    end as is_high_value_order,\n    \n    -- Large order flag\n    case \n        when item_count > 10 then true\n        else false\n    end as is_large_order,\n    \n    -- Time of day\n    case \n        when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n        when extract(hour from transaction_date) between 18 and 21 then 'Evening'\n        else 'Night'\n    end as time_of_day\n    \nfrom order_metrics om", "relation_name": "MYDB.LIVE_DATA.fact_orders", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.855179Z", "completed_at": "2025-06-04T02:52:09.905448Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.905448Z", "completed_at": "2025-06-04T02:52:09.905448Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.050269126892089844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.mv_fact_orders", "compiled": true, "compiled_code": "\n\nwith \n-- Get the 90th percentile order amount for high-value threshold\norder_thresholds as (\n    select \n        percentile_cont(0.9) within group (order by amount) as high_value_threshold\n    from MYDB.LIVE_DATA.stg_orders\n    where amount is not null\n),\n\n-- Get basic order metrics\norder_metrics as (\n    select\n        -- Primary key and foreign key\n        order_id,\n        user_id,\n        \n        -- Core order details\n        transaction_date,\n        item_count,\n        amount as order_amount,\n        \n        -- Basic metrics\n        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,\n        \n        -- Time since last order\n        datediff(\n            'day',\n            lag(transaction_date) over (partition by user_id order by transaction_date),\n            transaction_date\n        ) as days_since_previous_order,\n        \n        -- Running totals\n        sum(amount) over (order by transaction_date) as running_total_amount,\n        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,\n        \n        -- Include the generated_at for incremental logic if needed\n        generated_at\n        \n    from MYDB.LIVE_DATA.stg_orders\n    where transaction_date is not null\n    qualify row_number() over (partition by order_id order by generated_at desc) = 1\n)\n\n-- Final select with basic segmentation\nselect\n    om.*,\n    \n    -- Simple customer type\n    case \n        when user_order_sequence = 1 then 'New Customer'\n        when days_since_previous_order is null then 'New Customer'\n        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'\n        else 'Reactivated Customer'\n    end as customer_type,\n    \n    -- High value flag (using pre-calculated threshold)\n    case \n        when order_amount > (select high_value_threshold from order_thresholds) then true\n        else false\n    end as is_high_value_order,\n    \n    -- Large order flag\n    case \n        when item_count > 10 then true\n        else false\n    end as is_large_order,\n    \n    -- Time of day\n    case \n        when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n        when extract(hour from transaction_date) between 18 and 21 then 'Evening'\n        else 'Night'\n    end as time_of_day,\n    \n    -- Add a timestamp for when this record was created\n    current_timestamp() as dbt_updated_at\n    \nfrom order_metrics om", "relation_name": "MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.868279Z", "completed_at": "2025-06-04T02:52:09.905448Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.905448Z", "completed_at": "2025-06-04T02:52:09.905448Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.050269126892089844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.dim_users", "compiled": true, "compiled_code": "\n\n-- User dimension table for live data pipeline\n-- Provides a clean, analytics-ready view of user data\n\nwith users_base as (\n    select * from MYDB.LIVE_DATA.stg_users\n),\n\nuser_metrics as (\n    select\n        user_id,\n        count(*) as total_orders,\n        sum(amount) as total_spent,\n        avg(amount) as avg_order_value,\n        min(transaction_date) as first_order_date,\n        max(transaction_date) as last_order_date,\n        sum(item_count) as total_items_purchased\n    from MYDB.LIVE_DATA.stg_orders\n    group by user_id\n),\n\nuser_events as (\n    select\n        user_id,\n        count(*) as total_events,\n        count(distinct session_id) as total_sessions,\n        count(distinct event_date) as active_days,\n        min(event_timestamp) as first_event_date,\n        max(event_timestamp) as last_event_date,\n        \n        -- Event type counts\n        sum(case when action = 'view' then 1 else 0 end) as view_events,\n        sum(case when action = 'click' then 1 else 0 end) as click_events,\n        sum(case when action = 'log' then 1 else 0 end) as login_events,\n        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,\n        \n        -- Platform usage\n        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,\n        sum(case when device_category = 'Web' then 1 else 0 end) as web_events\n        \n    from MYDB.LIVE_DATA.stg_events\n    group by user_id\n),\n\nfinal as (\n    select\n        -- User identifiers\n        u.user_id,\n        u.email_hash,\n        \n        -- Personal information\n        u.firstname,\n        u.lastname,\n        u.address,\n        u.acquisition_channel,\n        u.country,\n        u.gender,\n        u.age_group,\n        \n        -- Dates\n        u.creation_date,\n        u.last_activity_date,\n        \n        -- Behavioral flags\n        u.is_churned,\n        u.activity_segment,\n        \n        -- Derived user metrics\n        u.days_since_creation,\n        u.days_since_last_activity,\n        \n        -- Order metrics\n        coalesce(om.total_orders, 0) as total_orders,\n        coalesce(om.total_spent, 0) as total_spent,\n        coalesce(om.avg_order_value, 0) as avg_order_value,\n        om.first_order_date,\n        om.last_order_date,\n        coalesce(om.total_items_purchased, 0) as total_items_purchased,\n        \n        -- Event metrics\n        coalesce(ue.total_events, 0) as total_events,\n        coalesce(ue.total_sessions, 0) as total_sessions,\n        coalesce(ue.active_days, 0) as active_days,\n        ue.first_event_date,\n        ue.last_event_date,\n        \n        -- Event type metrics\n        coalesce(ue.view_events, 0) as view_events,\n        coalesce(ue.click_events, 0) as click_events,\n        coalesce(ue.login_events, 0) as login_events,\n        coalesce(ue.purchase_events, 0) as purchase_events,\n        \n        -- Platform metrics\n        coalesce(ue.mobile_events, 0) as mobile_events,\n        coalesce(ue.web_events, 0) as web_events,\n        \n        -- Calculated metrics\n        case \n            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)\n            else 0\n        end as events_per_order,\n        \n        case \n            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)\n            else 0\n        end as events_per_session,\n        \n        case \n            when om.total_orders > 0 then \n                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders\n            else 0\n        end as avg_days_between_orders,\n        \n        -- Customer segments\n        case \n            when om.total_orders = 0 then 'No Orders'\n            when om.total_orders = 1 then 'One-time Buyer'\n            when om.total_orders <= 3 then 'Occasional Buyer'\n            when om.total_orders <= 10 then 'Regular Buyer'\n            else 'Frequent Buyer'\n        end as purchase_segment,\n        \n        case \n            when om.total_spent = 0 then 'No Spend'\n            when om.total_spent < 50 then 'Low Value'\n            when om.total_spent < 200 then 'Medium Value'\n            when om.total_spent < 500 then 'High Value'\n            else 'Premium Value'\n        end as value_segment,\n        \n        case \n            when ue.mobile_events > ue.web_events then 'Mobile Preferred'\n            when ue.web_events > ue.mobile_events then 'Web Preferred'\n            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'\n            else 'Unknown'\n        end as platform_preference,\n        \n        -- Data quality flags\n        u.has_future_creation_date,\n        u.has_invalid_activity_date,\n        \n        -- Metadata\n        u.batch_id,\n        u.generated_at,\n        u.processed_at,\n        current_timestamp() as mart_created_at\n        \n    from users_base u\n    left join user_metrics om on u.user_id = om.user_id\n    left join user_events ue on u.user_id = ue.user_id\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.dim_users", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.872105Z", "completed_at": "2025-06-04T02:52:09.905448Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.905448Z", "completed_at": "2025-06-04T02:52:09.905448Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.050269126892089844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.dim_users_scd2", "compiled": true, "compiled_code": "\n\nwith \n\n-- Get the latest user data from staging\nsource_data as (\n    select \n        user_id,\n        firstname,\n        lastname,\n        email_hash,\n        address,\n        acquisition_channel as canal,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        is_churned as churn,\n        batch_id,\n        current_timestamp() as dbt_updated_at,\n        -- Track when this record was processed\n        current_timestamp() as processed_at\n    from MYDB.LIVE_DATA.stg_users\n    where user_id is not null\n    qualify row_number() over (partition by user_id order by generated_at desc) = 1\n    \n    \n    -- Only process users that are new or have changed since the last run\n    and user_id in (\n        select user_id\n        from MYDB.LIVE_DATA.stg_users\n        where generated_at > (\n            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)\n            from MYDB.LIVE_DATA.dim_users_scd2\n            where is_current_version = true\n        )\n    )\n    \n),\n\n-- Get the current version of each user from the dimension\nexisting_dimension as (\n    \n    select\n        user_sk,\n        user_id,\n        firstname,\n        lastname,\n        email,\n        address,\n        acquisition_channel,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        churn,\n        batch_id,\n        dbt_valid_from,\n        dbt_valid_to,\n        is_current_version,\n        dbt_change_type,\n        current_timestamp() as processed_at  -- Add processed_at for consistency\n    from MYDB.LIVE_DATA.dim_users_scd2\n    where is_current_version = true\n    \n),\n\n-- Identify new and changed records\nchanges_to_apply as (\n    select\n        s.*,\n        case\n            when e.user_sk is null then 'insert'\n            when e.firstname != s.firstname \n              or e.lastname != s.lastname\n              or e.email != s.email_hash\n              or e.address != s.address\n              or e.acquisition_channel != s.canal\n              or e.country != s.country\n              or e.gender != s.gender\n              or e.age_group != s.age_group\n              or e.churn != s.churn then 'update'\n            else 'no_change'\n        end as change_type\n    from source_data s\n    left join existing_dimension e \n        on s.user_id = e.user_id\n)\n\n-- For initial load, just insert all records\n\n\n-- For incremental loads, handle inserts and updates\nselect * from (\n    -- New or updated records\n    select\n        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,\n        user_id,\n        firstname,\n        lastname,\n        email_hash as email,\n        address,\n        canal as acquisition_channel,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        churn,\n        batch_id,\n        dbt_updated_at as dbt_valid_from,\n        cast(null as timestamp_ntz) as dbt_valid_to,\n        true as is_current_version,\n        change_type as dbt_change_type,\n        processed_at\n    from changes_to_apply\n    where change_type in ('insert', 'update')\n    \n    union all\n    \n    -- Expire old versions of updated records\n    select\n        e.user_sk,\n        e.user_id,\n        e.firstname,\n        e.lastname,\n        e.email,\n        e.address,\n        e.acquisition_channel,\n        e.country,\n        e.gender,\n        e.age_group,\n        e.creation_date,\n        e.last_activity_date,\n        e.churn,\n        e.batch_id,\n        e.dbt_valid_from,\n        current_timestamp() as dbt_valid_to,\n        false as is_current_version,\n        'expire' as dbt_change_type,\n        e.processed_at\n    from existing_dimension e\n    inner join changes_to_apply c \n        on e.user_id = c.user_id\n        and c.change_type = 'update'\n)\n\n", "relation_name": "MYDB.LIVE_DATA.dim_users_scd2", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.916503Z", "completed_at": "2025-06-04T02:52:09.923702Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.923702Z", "completed_at": "2025-06-04T02:52:09.923702Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.010228872299194336, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.data_quality_health", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Data Quality Monitoring\n-- Tracks data quality metrics across all tables\n\nwith table_stats as (\n    -- Users table quality\n    select\n        'stg_users' as table_name,\n        'staging' as layer,\n        count(*) as total_rows,\n        count(distinct user_id) as unique_keys,\n        sum(case when user_id is null then 1 else 0 end) as null_primary_keys,\n        sum(case when email_hash is null then 1 else 0 end) as null_critical_fields,\n        sum(case when has_future_creation_date then 1 else 0 end) as data_quality_issues,\n        max(generated_at) as latest_data_timestamp,\n        current_timestamp() as check_timestamp\n    from MYDB.LIVE_DATA.stg_users\n    \n    union all\n    \n    -- Orders table quality\n    select\n        'stg_orders' as table_name,\n        'staging' as layer,\n        count(*) as total_rows,\n        count(distinct order_id) as unique_keys,\n        sum(case when order_id is null then 1 else 0 end) as null_primary_keys,\n        sum(case when user_id is null or amount is null then 1 else 0 end) as null_critical_fields,\n        sum(case when has_future_transaction_date or has_high_item_price then 1 else 0 end) as data_quality_issues,\n        max(generated_at) as latest_data_timestamp,\n        current_timestamp() as check_timestamp\n    from MYDB.LIVE_DATA.stg_orders\n    \n    union all\n    \n    -- Events table quality\n    select\n        'stg_events' as table_name,\n        'staging' as layer,\n        count(*) as total_rows,\n        count(distinct event_id) as unique_keys,\n        sum(case when event_id is null then 1 else 0 end) as null_primary_keys,\n        sum(case when user_id is null or session_id is null then 1 else 0 end) as null_critical_fields,\n        sum(case when has_future_event_timestamp then 1 else 0 end) as data_quality_issues,\n        max(generated_at) as latest_data_timestamp,\n        current_timestamp() as check_timestamp\n    from MYDB.LIVE_DATA.stg_events\n    \n    union all\n    \n    -- Dimension Users quality\n    select\n        'dim_users' as table_name,\n        'marts' as layer,\n        count(*) as total_rows,\n        count(distinct user_id) as unique_keys,\n        sum(case when user_id is null then 1 else 0 end) as null_primary_keys,\n        sum(case when email_hash is null then 1 else 0 end) as null_critical_fields,\n        sum(case when has_future_creation_date or has_invalid_activity_date then 1 else 0 end) as data_quality_issues,\n        max(generated_at) as latest_data_timestamp,\n        current_timestamp() as check_timestamp\n    from MYDB.LIVE_DATA.dim_users\n    \n    union all\n    \n    -- Fact Orders quality\n    select\n        'fact_orders' as table_name,\n        'marts' as layer,\n        count(*) as total_rows,\n        count(distinct order_id) as unique_keys,\n        sum(case when order_id is null then 1 else 0 end) as null_primary_keys,\n        sum(case when user_id is null or order_amount is null then 1 else 0 end) as null_critical_fields,\n        0 as data_quality_issues,  -- No specific DQ flags in fact table yet\n        max(transaction_date) as latest_data_timestamp,\n        current_timestamp() as check_timestamp\n    from MYDB.LIVE_DATA.fact_orders\n),\n\nquality_metrics as (\n    select\n        *,\n        -- Calculate quality scores\n        case when total_rows = unique_keys then 100 else round((unique_keys::float / total_rows) * 100, 2) end as uniqueness_score,\n        case when null_primary_keys = 0 then 100 else round(((total_rows - null_primary_keys)::float / total_rows) * 100, 2) end as completeness_score,\n        case when data_quality_issues = 0 then 100 else round(((total_rows - data_quality_issues)::float / total_rows) * 100, 2) end as validity_score,\n        \n        -- Freshness check (data should be within last 24 hours for live pipeline)\n        case \n            when latest_data_timestamp >= current_timestamp() - interval '1 hour' then 100\n            when latest_data_timestamp >= current_timestamp() - interval '6 hours' then 80\n            when latest_data_timestamp >= current_timestamp() - interval '24 hours' then 60\n            else 30\n        end as freshness_score,\n        \n        -- Data volume check (flag significant changes)\n        lag(total_rows) over (partition by table_name order by check_timestamp) as previous_row_count\n        \n    from table_stats\n),\n\nfinal_metrics as (\n    select\n        *,\n        -- Volume change detection\n        case \n            when previous_row_count is null then 0  -- First run\n            when previous_row_count = 0 then 0  -- Avoid division by zero\n            else round(((total_rows - previous_row_count)::float / previous_row_count) * 100, 2)\n        end as volume_change_pct,\n        \n        -- Overall quality score\n        round((uniqueness_score * 0.25 + completeness_score * 0.35 + validity_score * 0.25 + freshness_score * 0.15), 0) as overall_quality_score\n        \n    from quality_metrics\n)\n\nselect\n    table_name,\n    layer,\n    total_rows,\n    unique_keys,\n    null_primary_keys,\n    null_critical_fields,\n    data_quality_issues,\n    latest_data_timestamp,\n    uniqueness_score,\n    completeness_score,\n    validity_score,\n    freshness_score,\n    overall_quality_score,\n    volume_change_pct,\n    \n    -- Quality status\n    case \n        when overall_quality_score >= 95 then 'Excellent'\n        when overall_quality_score >= 85 then 'Good'\n        when overall_quality_score >= 70 then 'Fair'\n        when overall_quality_score >= 50 then 'Poor'\n        else 'Critical'\n    end as quality_status,\n    \n    -- Volume change status\n    case \n        when abs(volume_change_pct) <= 10 then 'Normal'\n        when abs(volume_change_pct) <= 25 then 'Moderate Change'\n        when abs(volume_change_pct) <= 50 then 'Significant Change'\n        else 'Extreme Change'\n    end as volume_status,\n    \n    -- Freshness status\n    case \n        when freshness_score >= 80 then 'Fresh'\n        when freshness_score >= 60 then 'Acceptable'\n        else 'Stale'\n    end as freshness_status,\n    \n    check_timestamp\n    \nfrom final_metrics\norder by layer, table_name", "relation_name": "MYDB.LIVE_DATA.data_quality_health", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-04T02:52:09.929208Z", "completed_at": "2025-06-04T02:52:09.930224Z"}, {"name": "execute", "started_at": "2025-06-04T02:52:09.936261Z", "completed_at": "2025-06-04T02:52:09.936261Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.01255941390991211, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.live_c360.etl_health_dashboard", "compiled": true, "compiled_code": "\n\n-- ETL Health Check: Overall Dashboard Summary\n-- Combines all health metrics into a single dashboard view\n\nwith latest_data_quality as (\n    select\n        'Data Quality' as health_category,\n        avg(overall_quality_score) as avg_health_score,\n        min(overall_quality_score) as min_health_score,\n        max(overall_quality_score) as max_health_score,\n        sum(total_rows) as metric_value,\n        sum(data_quality_issues) as issue_count,\n        max(check_timestamp) as latest_check_time\n    from MYDB.LIVE_DATA.data_quality_health\n    where date(check_timestamp) = current_date\n),\n\ncombined_health as (\n    select * from latest_data_quality\n),\n\nhealth_summary as (\n    select\n        health_category,\n        avg_health_score,\n        min_health_score,\n        max_health_score,\n        latest_check_time,\n        metric_value as total_items,\n        issue_count as total_issues,\n        \n        -- Health status\n        case \n            when avg_health_score >= 95 then 'Excellent'\n            when avg_health_score >= 85 then 'Good'\n            when avg_health_score >= 70 then 'Fair'\n            when avg_health_score >= 50 then 'Poor'\n            else 'Critical'\n        end as health_status,\n        \n        -- Trend indicator (simplified)\n        case \n            when min_health_score = max_health_score then 'Stable'\n            when max_health_score - min_health_score <= 10 then 'Stable'\n            when max_health_score - min_health_score <= 20 then 'Variable'\n            else 'Unstable'\n        end as trend_indicator\n        \n    from combined_health\n),\n\noverall_summary as (\n    select\n        current_date as dashboard_date,\n        current_timestamp() as dashboard_timestamp,\n        avg(avg_health_score) as overall_health_score,\n        sum(case when health_status = 'Excellent' then 1 else 0 end) as excellent_categories,\n        sum(case when health_status = 'Good' then 1 else 0 end) as good_categories,\n        sum(case when health_status = 'Fair' then 1 else 0 end) as fair_categories,\n        sum(case when health_status = 'Poor' then 1 else 0 end) as poor_categories,\n        sum(case when health_status = 'Critical' then 1 else 0 end) as critical_categories,\n        max(case when health_status in ('Poor', 'Critical') then 1 else 0 end) as has_critical_issues,\n        max(case when trend_indicator = 'Unstable' then 1 else 0 end) as has_unstable_trends,\n        count(*) as total_categories_monitored\n    from health_summary\n)\n\n-- Final output combining summary and details\nselect\n    'OVERALL' as health_category,\n    overall_health_score as avg_health_score,\n    overall_health_score as min_health_score,\n    overall_health_score as max_health_score,\n    dashboard_timestamp as latest_check_time,\n    total_categories_monitored as total_items,\n    critical_categories as total_issues,\n    \n    case \n        when overall_health_score >= 95 then 'Excellent'\n        when overall_health_score >= 85 then 'Good'\n        when overall_health_score >= 70 then 'Fair'\n        when overall_health_score >= 50 then 'Poor'\n        else 'Critical'\n    end as health_status,\n    \n    case \n        when has_unstable_trends = 1 then 'Unstable'\n        when has_critical_issues = 1 then 'Variable'\n        else 'Stable'\n    end as trend_indicator\n    \nfrom overall_summary\n\nunion all\n\n-- Individual category details\nselect\n    health_category,\n    avg_health_score,\n    min_health_score,\n    max_health_score,\n    latest_check_time,\n    total_items,\n    total_issues,\n    health_status,\n    trend_indicator\n    \nfrom health_summary\n\norder by \n    case health_category \n        when 'OVERALL' then 0\n        when 'Data Quality' then 1\n        when 'Pipeline Runtime' then 2\n        when 'dbt Tests' then 3\n        when 'Query Performance' then 4\n        else 5\n    end", "relation_name": "MYDB.LIVE_DATA.etl_health_dashboard", "batch_results": null}], "elapsed_time": 4.203338146209717, "args": {"populate_cache": true, "use_colors": true, "favor_state": false, "require_explicit_package_overrides_for_builtin_materializations": true, "compile": true, "quiet": false, "log_file_max_bytes": 10485760, "log_format": "default", "print": true, "state_modified_compare_vars": false, "vars": {}, "write_json": true, "partial_parse": true, "macro_debugging": false, "printer_width": 80, "require_resource_names_without_spaces": false, "use_colors_file": true, "exclude": [], "static_parser": true, "require_yaml_configuration_for_mf_time_spines": false, "invocation_command": "dbt docs generate", "show_resource_report": false, "indirect_selection": "eager", "require_batched_execution_for_custom_microbatch_strategy": false, "select": [], "source_freshness_run_project_hooks": false, "partial_parse_file_diff": true, "state_modified_compare_more_unrendered_values": false, "log_format_file": "debug", "static": false, "introspect": true, "log_path": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs", "strict_mode": false, "profiles_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "empty_catalog": false, "version_check": true, "warn_error_options": {"include": [], "exclude": []}, "cache_selected_only": false, "defer": false, "log_level": "info", "which": "generate", "project_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "log_level_file": "debug", "skip_nodes_if_on_run_start_fails": false, "send_anonymous_usage_stats": true, "require_nested_cumulative_type_params": false}}