[2025-06-04T01:46:03.667+0000] {processor.py:161} INFO - Started process (PID=193) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:03.669+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:46:03.672+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.672+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:03.693+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:03.801+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.800+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:simple_etl_pipeline
[2025-06-04T01:46:03.813+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.812+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:simple_etl_pipeline
[2025-06-04T01:46:03.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.819+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:simple_etl_pipeline
[2025-06-04T01:46:03.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.820+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:03.830+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.830+0000] {dag.py:3058} INFO - Creating ORM DAG for simple_etl_pipeline
[2025-06-04T01:46:03.842+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:03.842+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:46:03.873+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.209 seconds
[2025-06-04T01:46:34.802+0000] {processor.py:161} INFO - Started process (PID=202) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:34.803+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:46:34.806+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:34.806+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:34.831+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:46:34.869+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:34.869+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:34.893+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:34.892+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:46:34.918+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T01:47:05.886+0000] {processor.py:161} INFO - Started process (PID=211) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:05.887+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:47:05.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:05.890+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:05.914+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:05.947+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:05.947+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:05.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:05.970+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:47:06.005+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T01:47:36.195+0000] {processor.py:161} INFO - Started process (PID=220) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:36.196+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:47:36.200+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:36.199+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:36.225+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:47:36.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:36.257+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:36.280+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:36.280+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:47:36.309+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T01:48:06.410+0000] {processor.py:161} INFO - Started process (PID=229) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:06.412+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:48:06.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:06.416+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:06.447+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:06.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:06.495+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:06.519+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:06.519+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:48:06.543+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.138 seconds
[2025-06-04T01:48:37.328+0000] {processor.py:161} INFO - Started process (PID=238) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:37.330+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:48:37.333+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:37.333+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:37.360+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:48:37.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:37.396+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:37.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:37.423+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:48:37.446+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T01:49:08.485+0000] {processor.py:161} INFO - Started process (PID=247) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:08.486+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:49:08.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:08.488+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:08.509+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:08.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:08.536+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:08.559+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:08.559+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:49:08.579+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.098 seconds
[2025-06-04T01:49:38.917+0000] {processor.py:161} INFO - Started process (PID=255) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:38.918+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:49:38.921+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:38.921+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:38.946+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:49:38.986+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:38.986+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:39.010+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:39.010+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:49:39.037+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.156 seconds
[2025-06-04T01:50:11.039+0000] {processor.py:161} INFO - Started process (PID=264) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:11.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:50:11.046+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:11.045+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:11.077+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:11.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:11.126+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:11.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:11.164+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:50:11.206+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.172 seconds
[2025-06-04T01:50:41.263+0000] {processor.py:161} INFO - Started process (PID=273) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:41.266+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:50:41.269+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:41.269+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:41.297+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:50:41.335+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:41.335+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:41.360+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:41.360+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:50:41.387+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T01:51:12.524+0000] {processor.py:161} INFO - Started process (PID=282) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:12.526+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:51:12.529+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:12.529+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:12.554+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:12.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:12.594+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:12.620+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:12.620+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:51:12.645+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T01:51:43.892+0000] {processor.py:161} INFO - Started process (PID=290) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:43.894+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:51:43.896+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:43.896+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:43.923+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:51:43.955+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:43.955+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:43.980+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:43.980+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:51:44.002+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.114 seconds
[2025-06-04T01:52:15.948+0000] {processor.py:161} INFO - Started process (PID=299) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:15.949+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:52:15.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:15.953+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:15.983+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:16.020+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.019+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:16.044+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.043+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:52:16.073+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T01:52:47.094+0000] {processor.py:161} INFO - Started process (PID=308) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:47.096+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:52:47.100+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.099+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:47.131+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:52:47.170+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.170+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:47.196+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.196+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:52:47.223+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T01:53:19.277+0000] {processor.py:161} INFO - Started process (PID=318) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:19.279+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:53:19.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.281+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:19.313+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:19.350+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.350+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:19.375+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.375+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:53:19.403+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T01:53:50.729+0000] {processor.py:161} INFO - Started process (PID=327) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:50.731+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:53:50.737+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.736+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:50.790+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:53:50.835+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.834+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:50.868+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.867+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:53:50.895+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.175 seconds
[2025-06-04T01:54:21.571+0000] {processor.py:161} INFO - Started process (PID=336) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:21.575+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:54:21.577+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.577+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:21.613+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:21.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.649+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:21.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.681+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:54:21.716+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.148 seconds
[2025-06-04T01:54:52.067+0000] {processor.py:161} INFO - Started process (PID=345) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:52.068+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:54:52.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.070+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:52.104+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:54:52.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.164+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:52.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.212+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:54:52.267+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.204 seconds
[2025-06-04T01:55:22.409+0000] {processor.py:161} INFO - Started process (PID=354) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:22.410+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:55:22.415+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.414+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:22.440+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:22.480+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.480+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:22.514+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.514+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-03 00:00:00+00:00, run_after=2025-06-04 00:00:00+00:00
[2025-06-04T01:55:22.555+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.150 seconds
[2025-06-04T01:55:52.836+0000] {processor.py:161} INFO - Started process (PID=363) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:52.838+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:55:52.841+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:52.841+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:52.872+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:55:52.912+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:52.912+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:52.960+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T01:56:23.906+0000] {processor.py:161} INFO - Started process (PID=372) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:23.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:56:23.910+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:23.910+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:23.936+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:23.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:23.973+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:24.025+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T01:56:54.925+0000] {processor.py:161} INFO - Started process (PID=381) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:54.927+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:56:54.932+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:54.931+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:54.971+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:56:55.013+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.013+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:55.081+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.161 seconds
[2025-06-04T01:57:25.996+0000] {processor.py:161} INFO - Started process (PID=390) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:25.999+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:57:26.003+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.003+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:26.032+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:26.065+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.065+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:26.116+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T01:57:56.164+0000] {processor.py:161} INFO - Started process (PID=400) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:56.166+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:57:56.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.168+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:56.201+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:57:56.240+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.240+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:56.305+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.147 seconds
[2025-06-04T01:58:26.466+0000] {processor.py:161} INFO - Started process (PID=409) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:26.468+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:58:26.470+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:26.470+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:26.500+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:26.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:26.744+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:27.183+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.721 seconds
[2025-06-04T01:58:58.094+0000] {processor.py:161} INFO - Started process (PID=418) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:58.095+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:58:58.098+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.097+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:58.138+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:58:58.176+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.175+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:58.784+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.696 seconds
[2025-06-04T01:59:28.973+0000] {processor.py:161} INFO - Started process (PID=421) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:28.974+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:59:28.977+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:28.976+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:29.009+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:29.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:29.051+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:29.102+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T01:59:59.240+0000] {processor.py:161} INFO - Started process (PID=429) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:59.241+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T01:59:59.244+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:59.243+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:59.269+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T01:59:59.307+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:59.306+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:59.355+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T02:00:29.416+0000] {processor.py:161} INFO - Started process (PID=437) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:00:29.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:00:29.421+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:29.420+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:00:29.448+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:00:29.482+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:29.482+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:29.532+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T02:01:00.491+0000] {processor.py:161} INFO - Started process (PID=446) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:00.492+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:01:00.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:00.495+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:00.531+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:00.564+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:00.564+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:00.606+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T02:01:30.679+0000] {processor.py:161} INFO - Started process (PID=455) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:30.680+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:01:30.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:30.682+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:30.716+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:01:30.749+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:30.748+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:30.798+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T02:02:00.884+0000] {processor.py:161} INFO - Started process (PID=465) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:00.887+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:02:00.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:00.890+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:00.917+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:00.952+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:00.952+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:00.999+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T02:02:31.146+0000] {processor.py:161} INFO - Started process (PID=474) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:31.147+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:02:31.150+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:31.149+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:31.179+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:02:31.217+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:31.216+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:31.261+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T02:03:02.104+0000] {processor.py:161} INFO - Started process (PID=483) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:02.105+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:03:02.109+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:02.108+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:02.143+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:02.188+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:02.188+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:02.246+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.150 seconds
[2025-06-04T02:03:32.313+0000] {processor.py:161} INFO - Started process (PID=492) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:32.315+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:03:32.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:32.317+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:32.350+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:03:32.381+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:32.381+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:32.425+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T02:04:02.630+0000] {processor.py:161} INFO - Started process (PID=501) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:02.632+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:04:02.635+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:02.635+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:02.668+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:02.704+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:02.703+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:02.755+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T02:04:32.878+0000] {processor.py:161} INFO - Started process (PID=510) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:32.880+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:04:32.883+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:32.882+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:32.910+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:04:32.944+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:32.943+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:32.991+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T02:05:03.041+0000] {processor.py:161} INFO - Started process (PID=520) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:03.044+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:05:03.046+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:03.046+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:03.074+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:03.107+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:03.107+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:03.162+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T02:05:33.315+0000] {processor.py:161} INFO - Started process (PID=528) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:33.317+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:05:33.319+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:33.319+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:33.341+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:05:33.371+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:33.371+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:33.414+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.103 seconds
[2025-06-04T02:06:03.753+0000] {processor.py:161} INFO - Started process (PID=537) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:03.754+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:06:03.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.758+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:03.790+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:03.826+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.826+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:03.848+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.848+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:06:03.870+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T02:06:34.827+0000] {processor.py:161} INFO - Started process (PID=546) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:34.829+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:06:34.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.832+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:34.866+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:06:34.905+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.905+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:34.932+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.932+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:06:34.956+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T02:07:05.864+0000] {processor.py:161} INFO - Started process (PID=555) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:05.866+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:07:05.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:05.879+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:06.077+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:06.235+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.235+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:06.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.270+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:07:06.307+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.447 seconds
[2025-06-04T02:07:36.470+0000] {processor.py:161} INFO - Started process (PID=565) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:36.472+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:07:36.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.474+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:36.494+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:07:36.524+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.524+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:36.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.547+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:07:36.568+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T02:08:06.817+0000] {processor.py:161} INFO - Started process (PID=573) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:06.819+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:08:06.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:06.821+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:06.854+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:07.020+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.020+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:07.082+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.081+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:08:07.124+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.316 seconds
[2025-06-04T02:08:38.485+0000] {processor.py:161} INFO - Started process (PID=582) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:38.486+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:08:38.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.490+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:38.518+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:08:38.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:38.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.575+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:08:38.603+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T02:09:10.486+0000] {processor.py:161} INFO - Started process (PID=591) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:10.487+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:09:10.489+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.489+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:10.512+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:10.545+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.545+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:10.571+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.571+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:09:10.594+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T02:09:42.656+0000] {processor.py:161} INFO - Started process (PID=600) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:42.657+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:09:42.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.659+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:42.698+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:09:42.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.734+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:42.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.757+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:09:42.782+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T02:10:14.112+0000] {processor.py:161} INFO - Started process (PID=609) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:14.114+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:10:14.116+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.116+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:14.141+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:14.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.179+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:14.208+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.207+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:10:14.239+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T02:10:20.362+0000] {processor.py:161} INFO - Started process (PID=612) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:20.363+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:10:20.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:20.366+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:20.404+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:20.439+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:20.438+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:20.467+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:20.467+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:10:20.500+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.143 seconds
[2025-06-04T02:10:50.682+0000] {processor.py:161} INFO - Started process (PID=621) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:50.684+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:10:50.687+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:50.686+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:50.711+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:10:50.740+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:50.740+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:50.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:50.762+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:10:50.782+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.105 seconds
[2025-06-04T02:11:20.887+0000] {processor.py:161} INFO - Started process (PID=630) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:20.894+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:11:20.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:20.898+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:20.928+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:20.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:20.978+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:21.043+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:21.043+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:11:21.097+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.215 seconds
[2025-06-04T02:11:52.128+0000] {processor.py:161} INFO - Started process (PID=639) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:52.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:11:52.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:52.132+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:52.168+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:11:52.202+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:52.202+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:52.227+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:52.227+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:11:52.245+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T02:17:18.081+0000] {processor.py:161} INFO - Started process (PID=117) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:17:18.083+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:17:18.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.087+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:17:18.177+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:17:18.524+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.524+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:17:18.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.574+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:17:18.623+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.554 seconds
[2025-06-04T02:20:34.221+0000] {processor.py:161} INFO - Started process (PID=61) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:20:34.241+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:20:34.244+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.243+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:20:34.292+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:20:34.699+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.699+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:20:34.720+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.720+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:20:35.820+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 1.603 seconds
[2025-06-04T02:21:00.106+0000] {processor.py:161} INFO - Started process (PID=60) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:00.108+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:21:00.111+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.110+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:00.145+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:00.200+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.199+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:00.238+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.238+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:21:00.625+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.527 seconds
[2025-06-04T02:21:17.776+0000] {processor.py:161} INFO - Started process (PID=55) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:17.777+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:21:17.780+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.779+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:17.807+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:17.875+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.875+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:17.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.897+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:21:17.923+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.152 seconds
[2025-06-04T02:21:47.980+0000] {processor.py:161} INFO - Started process (PID=64) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:47.982+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:21:47.984+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:47.984+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:48.008+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:21:48.039+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.039+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:48.061+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.061+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:21:48.083+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T02:22:18.564+0000] {processor.py:161} INFO - Started process (PID=73) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:18.565+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:22:18.568+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.568+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:18.603+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:18.651+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.650+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:18.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.675+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:22:18.706+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.147 seconds
[2025-06-04T02:22:48.902+0000] {processor.py:161} INFO - Started process (PID=83) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:48.903+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:22:48.907+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:48.906+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:48.937+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:22:48.972+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:48.972+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:48.996+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:48.996+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:22:49.027+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T02:38:09.726+0000] {processor.py:161} INFO - Started process (PID=51) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:09.733+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:38:09.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:09.739+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:09.822+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:10.213+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.213+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:10.274+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.273+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:38:10.321+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.606 seconds
[2025-06-04T02:38:40.492+0000] {processor.py:161} INFO - Started process (PID=60) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:40.494+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:38:40.498+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.497+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:40.535+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:38:40.577+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.577+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:40.603+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.603+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:38:40.627+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.140 seconds
[2025-06-04T02:39:11.011+0000] {processor.py:161} INFO - Started process (PID=69) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:11.016+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:39:11.026+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.025+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:11.098+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:11.172+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.171+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:11.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.223+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:39:11.262+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.268 seconds
[2025-06-04T02:39:41.447+0000] {processor.py:161} INFO - Started process (PID=78) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:41.449+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:39:41.451+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.451+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:41.478+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:39:41.513+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:41.539+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.539+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:39:41.569+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T02:40:11.711+0000] {processor.py:161} INFO - Started process (PID=87) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:11.712+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:40:11.715+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.715+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:11.738+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:11.776+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.774+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:11.802+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.801+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:40:11.824+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T02:40:41.973+0000] {processor.py:161} INFO - Started process (PID=96) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:41.974+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:40:41.977+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:41.977+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:42.024+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:40:42.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:42.063+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:42.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:42.087+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:40:42.107+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.139 seconds
[2025-06-04T02:41:12.214+0000] {processor.py:161} INFO - Started process (PID=105) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:12.217+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:41:12.220+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.220+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:12.250+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:12.286+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.285+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:12.310+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.309+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:41:12.333+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T02:41:43.057+0000] {processor.py:161} INFO - Started process (PID=114) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:43.060+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:41:43.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.062+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:43.095+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:41:43.145+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.145+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:43.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.241+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:41:43.337+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.288 seconds
[2025-06-04T02:42:14.057+0000] {processor.py:161} INFO - Started process (PID=124) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:14.060+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:42:14.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.063+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:14.096+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:14.133+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.133+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:14.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.166+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:42:14.195+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.143 seconds
[2025-06-04T02:42:44.560+0000] {processor.py:161} INFO - Started process (PID=133) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:44.562+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:42:44.566+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.565+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:44.594+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:42:44.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.630+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:44.655+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.655+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:42:44.677+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T02:43:14.974+0000] {processor.py:161} INFO - Started process (PID=142) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:14.975+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:43:14.978+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:14.978+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:15.018+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:15.061+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.061+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:15.089+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.088+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:43:15.112+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.143 seconds
[2025-06-04T02:43:45.414+0000] {processor.py:161} INFO - Started process (PID=151) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:45.415+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:43:45.426+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.426+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:45.474+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:43:45.553+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.553+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:45.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.585+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:43:45.634+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.225 seconds
[2025-06-04T02:44:16.022+0000] {processor.py:161} INFO - Started process (PID=160) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:16.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:44:16.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.042+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:16.090+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:16.124+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.124+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:16.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.149+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:44:16.180+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.162 seconds
[2025-06-04T02:44:47.118+0000] {processor.py:161} INFO - Started process (PID=168) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:47.121+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:44:47.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.126+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:47.167+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:44:47.204+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.204+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:47.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.242+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:44:47.333+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.220 seconds
[2025-06-04T02:45:17.900+0000] {processor.py:161} INFO - Started process (PID=177) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:17.901+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:45:17.907+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:17.906+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:17.970+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:18.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.021+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:18.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.051+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:45:18.082+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.186 seconds
[2025-06-04T02:45:48.145+0000] {processor.py:161} INFO - Started process (PID=186) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:48.147+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:45:48.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.149+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:48.175+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:45:48.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.210+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:48.239+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.239+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:45:48.262+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T02:46:18.436+0000] {processor.py:161} INFO - Started process (PID=195) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:18.437+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:46:18.439+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.439+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:18.469+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:18.509+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.509+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:18.543+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.542+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:46:18.579+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.146 seconds
[2025-06-04T02:46:49.143+0000] {processor.py:161} INFO - Started process (PID=204) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:49.144+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:46:49.147+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:49.147+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:49.172+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:46:49.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:49.212+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:49.272+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
[2025-06-04T02:47:19.413+0000] {processor.py:161} INFO - Started process (PID=213) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:19.416+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:47:19.420+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:19.419+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:19.445+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:19.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:19.487+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:19.569+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.166 seconds
[2025-06-04T02:47:50.211+0000] {processor.py:161} INFO - Started process (PID=222) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:50.214+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:47:50.216+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:50.216+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:50.246+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:47:50.361+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:50.361+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:50.408+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.201 seconds
[2025-06-04T02:48:21.664+0000] {processor.py:161} INFO - Started process (PID=231) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:21.666+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:48:21.669+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.668+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:21.698+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:21.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.739+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:21.789+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T02:48:51.849+0000] {processor.py:161} INFO - Started process (PID=240) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:51.851+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:48:51.855+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:51.855+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:51.877+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:48:51.909+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:51.909+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:53.209+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T02:49:25.385+0000] {processor.py:161} INFO - Started process (PID=249) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:25.387+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:49:25.390+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.389+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:25.427+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:25.467+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.466+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:25.517+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.137 seconds
[2025-06-04T02:49:56.757+0000] {processor.py:161} INFO - Started process (PID=258) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:56.760+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:49:56.765+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:56.764+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:56.810+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:49:56.872+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:56.872+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:57.396+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.663 seconds
[2025-06-04T02:50:28.778+0000] {processor.py:161} INFO - Started process (PID=267) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:28.779+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:50:28.782+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.782+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:28.840+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:28.885+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.884+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:28.943+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.170 seconds
[2025-06-04T02:50:59.012+0000] {processor.py:161} INFO - Started process (PID=270) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:59.014+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:50:59.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:59.018+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:59.052+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:50:59.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:59.095+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:59.159+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.151 seconds
[2025-06-04T02:51:32.538+0000] {processor.py:161} INFO - Started process (PID=285) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:51:32.545+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:51:32.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.547+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:51:32.579+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:51:32.614+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.614+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:32.670+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.137 seconds
[2025-06-04T02:52:03.309+0000] {processor.py:161} INFO - Started process (PID=294) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:03.311+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:52:03.313+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.313+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:03.348+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:03.389+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.388+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:03.422+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.422+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:52:03.454+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.153 seconds
[2025-06-04T02:52:33.818+0000] {processor.py:161} INFO - Started process (PID=297) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:33.859+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:52:33.962+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:33.951+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:34.045+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:52:35.928+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:35.928+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:36.176+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.176+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:52:36.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 1.875 seconds
[2025-06-04T02:53:07.701+0000] {processor.py:161} INFO - Started process (PID=306) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:07.705+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:53:07.708+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.707+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:07.746+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:07.800+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.800+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:07.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.832+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:53:07.862+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.165 seconds
[2025-06-04T02:53:39.211+0000] {processor.py:161} INFO - Started process (PID=315) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:39.213+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:53:39.217+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.217+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:39.363+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:53:39.497+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.497+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:39.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.546+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:53:39.580+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.381 seconds
[2025-06-04T02:54:13.833+0000] {processor.py:161} INFO - Started process (PID=324) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:14.415+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:54:16.305+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:15.786+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:17.766+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:18.155+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.155+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:18.269+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.269+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:54:18.318+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 5.561 seconds
[2025-06-04T02:54:48.416+0000] {processor.py:161} INFO - Started process (PID=333) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:48.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:54:48.421+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.421+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:48.455+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:54:48.489+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.488+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:48.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.516+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:54:48.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T02:55:18.644+0000] {processor.py:161} INFO - Started process (PID=342) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:18.645+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:55:18.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.648+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:18.684+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:18.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.730+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:18.759+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.759+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:55:18.783+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T02:55:49.717+0000] {processor.py:161} INFO - Started process (PID=351) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:49.720+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:55:49.742+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.741+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:49.783+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:55:49.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.833+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:49.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.873+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:55:49.915+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.198 seconds
[2025-06-04T02:56:20.078+0000] {processor.py:161} INFO - Started process (PID=361) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:20.082+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:56:20.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.085+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:20.124+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:20.154+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.154+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:20.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.179+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:56:20.206+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T02:56:50.412+0000] {processor.py:161} INFO - Started process (PID=370) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:50.417+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:56:50.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.419+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:50.450+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:56:50.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.501+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:50.534+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.534+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:56:50.557+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.150 seconds
[2025-06-04T02:57:21.651+0000] {processor.py:161} INFO - Started process (PID=379) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:21.653+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:57:21.656+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.656+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:21.686+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:21.725+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.725+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:21.754+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.753+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:57:21.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T02:57:53.673+0000] {processor.py:161} INFO - Started process (PID=388) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:53.675+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:57:53.678+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.677+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:53.710+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:57:53.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.744+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:53.770+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.770+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:57:53.792+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T02:58:26.013+0000] {processor.py:161} INFO - Started process (PID=397) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:26.015+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:58:26.018+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.017+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:26.057+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:26.101+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.100+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:26.150+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.150+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:58:26.311+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.302 seconds
[2025-06-04T02:58:57.498+0000] {processor.py:161} INFO - Started process (PID=406) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:57.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:58:57.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.503+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:57.576+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:58:57.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.611+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:57.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.644+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:58:57.669+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.183 seconds
[2025-06-04T02:59:28.683+0000] {processor.py:161} INFO - Started process (PID=415) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:59:28.685+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T02:59:28.688+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.687+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:59:28.729+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T02:59:28.770+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.770+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:59:28.809+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.809+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T02:59:28.842+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.163 seconds
[2025-06-04T03:00:00.859+0000] {processor.py:161} INFO - Started process (PID=424) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:00.861+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:00:00.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:00.864+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:00.891+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:00.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:00.925+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:00.969+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:00.969+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:00:00.999+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T03:00:32.030+0000] {processor.py:161} INFO - Started process (PID=433) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:32.036+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:00:32.047+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.043+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:32.089+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:00:32.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.167+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:32.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.211+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:00:32.267+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.263 seconds
[2025-06-04T03:01:03.633+0000] {processor.py:161} INFO - Started process (PID=442) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:03.635+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:01:03.638+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.637+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:03.668+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:03.707+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.706+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:03.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.733+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:01:03.760+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T03:01:35.724+0000] {processor.py:161} INFO - Started process (PID=451) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:35.727+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:01:35.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:35.730+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:35.765+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:01:35.801+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:35.801+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:35.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:35.867+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:01:35.980+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.261 seconds
[2025-06-04T03:02:07.055+0000] {processor.py:161} INFO - Started process (PID=460) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:07.057+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:02:07.060+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.060+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:07.090+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:07.130+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.130+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:07.155+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.155+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:02:07.179+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T03:02:39.220+0000] {processor.py:161} INFO - Started process (PID=469) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:39.222+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:02:39.224+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.224+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:39.251+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:02:39.289+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.289+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:39.319+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.319+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:02:39.343+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T03:03:10.740+0000] {processor.py:161} INFO - Started process (PID=478) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:10.745+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:03:10.757+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:10.756+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:10.801+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:10.877+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:10.870+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:10.933+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:10.933+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:03:11.000+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.264 seconds
[2025-06-04T03:03:43.028+0000] {processor.py:161} INFO - Started process (PID=487) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:43.030+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:03:43.032+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.032+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:43.060+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:03:43.098+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.097+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:43.124+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.123+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:03:43.147+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T03:04:14.147+0000] {processor.py:161} INFO - Started process (PID=496) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:14.152+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:04:14.154+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.154+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:14.182+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:14.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.212+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:14.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.243+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:04:14.263+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T03:04:45.732+0000] {processor.py:161} INFO - Started process (PID=505) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:45.733+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:04:45.738+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.738+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:45.761+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:04:45.797+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.797+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:45.827+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.826+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:04:45.856+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T03:05:18.157+0000] {processor.py:161} INFO - Started process (PID=514) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:18.158+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:05:18.161+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.160+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:18.194+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:18.229+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.229+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:18.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.257+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:05:18.296+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.147 seconds
[2025-06-04T03:05:49.476+0000] {processor.py:161} INFO - Started process (PID=523) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:49.479+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:05:49.481+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.481+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:49.514+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:05:49.551+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.551+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:49.578+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.578+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:05:49.601+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T03:06:21.784+0000] {processor.py:161} INFO - Started process (PID=531) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:21.802+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:06:21.809+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:21.809+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:21.937+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:22.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.022+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:22.097+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.093+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:06:22.176+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.396 seconds
[2025-06-04T03:06:52.824+0000] {processor.py:161} INFO - Started process (PID=540) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:52.826+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:06:52.830+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:52.829+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:52.875+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:06:52.911+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:52.911+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:52.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:52.938+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:06:52.974+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.154 seconds
[2025-06-04T03:07:04.390+0000] {processor.py:161} INFO - Started process (PID=550) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:04.404+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:07:04.412+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:04.411+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:04.457+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:04.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:04.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:04.545+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:04.545+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:07:04.607+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.222 seconds
[2025-06-04T03:07:34.725+0000] {processor.py:161} INFO - Started process (PID=560) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:34.730+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:07:34.733+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:34.732+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:34.765+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:07:34.801+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:34.800+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:34.825+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:34.825+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:07:34.849+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T03:08:05.126+0000] {processor.py:161} INFO - Started process (PID=563) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:05.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:08:05.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:05.131+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:05.181+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:05.245+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:05.245+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:08:05.271+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:05.271+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:08:05.300+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.177 seconds
[2025-06-04T03:08:35.410+0000] {processor.py:161} INFO - Started process (PID=573) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:35.413+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:08:35.415+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:35.415+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:35.442+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:08:35.473+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:35.473+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:08:35.509+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:35.509+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:08:35.586+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.180 seconds
[2025-06-04T03:09:06.560+0000] {processor.py:161} INFO - Started process (PID=582) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:06.562+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:09:06.564+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:06.564+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:06.596+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:06.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:06.630+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:06.656+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:06.656+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:09:06.679+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T03:09:37.050+0000] {processor.py:161} INFO - Started process (PID=592) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:37.055+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:09:37.057+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:37.057+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:37.081+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:09:37.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:37.114+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:37.138+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:37.137+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:09:37.157+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T03:10:07.211+0000] {processor.py:161} INFO - Started process (PID=601) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:07.213+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:10:07.215+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:07.215+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:07.241+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:07.277+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:07.277+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:07.302+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:07.301+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:10:07.326+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:10:37.732+0000] {processor.py:161} INFO - Started process (PID=610) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:37.733+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:10:37.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:37.736+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:37.769+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:10:37.806+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:37.806+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:37.841+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:37.841+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:10:37.868+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.141 seconds
[2025-06-04T03:11:08.097+0000] {processor.py:161} INFO - Started process (PID=619) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:08.098+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:11:08.102+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:08.102+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:08.128+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:08.181+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:08.180+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:08.220+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:08.219+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:11:08.272+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.183 seconds
[2025-06-04T03:11:38.464+0000] {processor.py:161} INFO - Started process (PID=628) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:38.465+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:11:38.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:38.467+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:38.493+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:11:38.528+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:38.527+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:38.553+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:38.552+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:11:38.573+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T03:12:09.050+0000] {processor.py:161} INFO - Started process (PID=637) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:09.052+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:12:09.055+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:09.054+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:09.085+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:09.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:09.117+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:09.144+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:09.144+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:12:09.167+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T03:12:39.268+0000] {processor.py:161} INFO - Started process (PID=646) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:39.269+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:12:39.272+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:39.271+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:39.301+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:12:39.344+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:39.344+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:39.373+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:39.373+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:12:39.601+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.337 seconds
[2025-06-04T03:13:09.902+0000] {processor.py:161} INFO - Started process (PID=655) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:09.905+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:13:09.909+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:09.908+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:09.955+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:09.987+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:09.987+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:10.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:10.012+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:13:10.033+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T03:13:40.945+0000] {processor.py:161} INFO - Started process (PID=662) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:40.950+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:13:40.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:40.962+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:41.002+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:13:41.038+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.038+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:41.070+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.069+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:13:41.105+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.163 seconds
[2025-06-04T03:14:12.153+0000] {processor.py:161} INFO - Started process (PID=671) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:12.157+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:14:12.160+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.159+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:12.195+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:12.232+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.232+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:12.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.415+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:14:12.439+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.291 seconds
[2025-06-04T03:14:44.407+0000] {processor.py:161} INFO - Started process (PID=680) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:44.409+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:14:44.411+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.411+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:44.440+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:14:44.476+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.476+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:44.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.503+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:14:44.532+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T03:15:16.572+0000] {processor.py:161} INFO - Started process (PID=689) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:16.574+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:15:16.576+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.576+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:16.606+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:16.642+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.642+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:16.826+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.826+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:15:16.851+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.283 seconds
[2025-06-04T03:15:47.817+0000] {processor.py:161} INFO - Started process (PID=698) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:47.819+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:15:47.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.821+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:47.854+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:15:47.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.890+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:47.919+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.918+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:15:47.942+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T03:16:19.909+0000] {processor.py:161} INFO - Started process (PID=707) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:19.911+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:16:19.919+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:19.919+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:19.951+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:19.984+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:19.984+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:20.205+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:20.202+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:16:20.245+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.341 seconds
[2025-06-04T03:16:51.176+0000] {processor.py:161} INFO - Started process (PID=716) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:51.178+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:16:51.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.180+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:51.209+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:16:51.248+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.247+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:51.277+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.277+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:16:51.299+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T03:17:23.378+0000] {processor.py:161} INFO - Started process (PID=725) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:23.380+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:17:23.383+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.383+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:23.413+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:23.448+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.447+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:23.638+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.635+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:17:23.661+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.288 seconds
[2025-06-04T03:17:54.739+0000] {processor.py:161} INFO - Started process (PID=734) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:54.740+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:17:54.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.743+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:54.770+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:17:54.813+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.813+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:54.839+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.838+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:17:54.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T03:18:26.046+0000] {processor.py:161} INFO - Started process (PID=743) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:26.047+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:18:26.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.049+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:26.077+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:26.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.114+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:26.309+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.308+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:18:26.328+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.287 seconds
[2025-06-04T03:18:56.444+0000] {processor.py:161} INFO - Started process (PID=752) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:56.446+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:18:56.448+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:56.448+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:57.938+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:18:57.972+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:57.972+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:58.000+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:58.000+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:18:58.026+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.350 seconds
[2025-06-04T03:19:30.402+0000] {processor.py:161} INFO - Started process (PID=760) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:19:30.404+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:19:30.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.408+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:19:30.438+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:19:30.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.475+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:19:30.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.501+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:19:30.528+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T03:20:01.731+0000] {processor.py:161} INFO - Started process (PID=769) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:01.734+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:20:01.737+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.736+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:01.762+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:01.802+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.802+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:01.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.832+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:20:01.859+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T03:20:33.864+0000] {processor.py:161} INFO - Started process (PID=779) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:33.866+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:20:33.869+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:33.868+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:33.898+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:20:33.934+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:33.934+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:33.962+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:33.962+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:20:33.984+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T03:21:05.241+0000] {processor.py:161} INFO - Started process (PID=788) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:05.243+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:21:05.245+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.245+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:05.271+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:05.306+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.306+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:05.334+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.332+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:21:05.359+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T03:21:37.546+0000] {processor.py:161} INFO - Started process (PID=797) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:37.550+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:21:37.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.552+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:37.580+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:21:37.613+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.612+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:37.646+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.646+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:21:37.676+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T03:22:09.811+0000] {processor.py:161} INFO - Started process (PID=806) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:09.815+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:22:09.827+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:09.824+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:09.875+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:09.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:09.925+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:09.956+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:09.956+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:22:09.978+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.173 seconds
[2025-06-04T03:22:41.085+0000] {processor.py:161} INFO - Started process (PID=815) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:41.088+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:22:41.091+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.090+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:41.129+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:22:41.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.166+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:41.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.199+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:22:41.240+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.159 seconds
[2025-06-04T03:23:11.304+0000] {processor.py:161} INFO - Started process (PID=824) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:12.546+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:23:12.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.548+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:12.578+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:12.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.612+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:12.636+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.636+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:23:12.660+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:23:45.143+0000] {processor.py:161} INFO - Started process (PID=833) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:45.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:23:45.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:45.174+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:23:45.207+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.206+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:45.235+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.235+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:23:45.260+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:24:16.400+0000] {processor.py:161} INFO - Started process (PID=842) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:16.401+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:24:16.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.404+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:16.428+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:16.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.475+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:16.505+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.504+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:24:16.542+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.146 seconds
[2025-06-04T03:24:46.608+0000] {processor.py:161} INFO - Started process (PID=851) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:46.609+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:24:46.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:46.611+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:46.637+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:24:46.670+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:46.670+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:46.694+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:46.694+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:24:46.717+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T03:25:17.391+0000] {processor.py:161} INFO - Started process (PID=860) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:17.392+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:25:17.395+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:17.394+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:17.420+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:17.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:17.456+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:17.480+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:17.479+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:25:17.502+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T03:25:47.655+0000] {processor.py:161} INFO - Started process (PID=868) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:47.656+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:25:47.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:47.660+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:47.688+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:25:47.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:47.721+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:47.746+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:47.746+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:25:47.768+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T03:26:18.021+0000] {processor.py:161} INFO - Started process (PID=877) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:18.022+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:26:18.025+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:18.025+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:18.049+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:18.081+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:18.081+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:18.107+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:18.107+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:26:18.130+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T03:26:48.311+0000] {processor.py:161} INFO - Started process (PID=886) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:48.313+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:26:48.315+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:48.315+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:48.339+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:26:48.371+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:48.370+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:48.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:48.403+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:26:48.427+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T03:27:18.546+0000] {processor.py:161} INFO - Started process (PID=895) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:18.547+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:27:18.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:18.549+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:18.576+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:18.605+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:18.605+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:27:18.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:18.633+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:27:18.665+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T03:27:48.844+0000] {processor.py:161} INFO - Started process (PID=904) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:48.846+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:27:48.850+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:48.850+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:48.874+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:27:48.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:48.905+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:27:48.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:48.931+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:27:48.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T03:28:19.254+0000] {processor.py:161} INFO - Started process (PID=912) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:19.257+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:28:19.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:19.259+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:19.282+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:19.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:19.316+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:19.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:19.338+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:28:19.362+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T03:28:49.473+0000] {processor.py:161} INFO - Started process (PID=921) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:49.475+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:28:49.477+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:49.476+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:49.504+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:28:49.537+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:49.537+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:49.561+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:49.561+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:28:49.585+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T03:29:19.705+0000] {processor.py:161} INFO - Started process (PID=929) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:19.707+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:29:19.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:19.709+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:19.736+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:19.770+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:19.770+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:19.793+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:19.793+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:29:19.821+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T03:29:50.181+0000] {processor.py:161} INFO - Started process (PID=938) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:50.185+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:29:50.190+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:50.189+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:50.222+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:29:50.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:50.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:50.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:50.283+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:29:50.306+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T03:30:23.443+0000] {processor.py:161} INFO - Started process (PID=947) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:23.445+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:30:23.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:23.449+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:23.476+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:23.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:23.511+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:23.535+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:23.535+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:30:23.559+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T03:30:53.785+0000] {processor.py:161} INFO - Started process (PID=956) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:53.787+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:30:53.789+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:53.789+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:53.812+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:30:53.842+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:53.842+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:53.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:53.865+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:30:53.889+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T03:31:24.106+0000] {processor.py:161} INFO - Started process (PID=965) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:24.109+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:31:24.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:24.111+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:24.142+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:24.177+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:24.176+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:24.200+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:24.200+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:31:24.228+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T03:31:54.453+0000] {processor.py:161} INFO - Started process (PID=968) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:54.456+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:31:54.458+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:54.458+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:54.486+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:31:54.518+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:54.518+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:54.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:54.542+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:31:54.564+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.114 seconds
[2025-06-04T03:32:24.742+0000] {processor.py:161} INFO - Started process (PID=977) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:24.743+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:32:24.745+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:24.745+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:24.774+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:24.808+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:24.807+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:24.831+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:24.831+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:32:24.854+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T03:32:55.188+0000] {processor.py:161} INFO - Started process (PID=986) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:55.190+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:32:55.192+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:55.192+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:55.220+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:32:55.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:55.253+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:55.279+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:55.279+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:32:55.301+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T03:33:26.304+0000] {processor.py:161} INFO - Started process (PID=995) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:26.306+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:33:26.308+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:26.308+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:26.337+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:26.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:26.370+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:26.393+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:26.393+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:33:26.529+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.230 seconds
[2025-06-04T03:33:57.075+0000] {processor.py:161} INFO - Started process (PID=1004) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:57.076+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:33:57.079+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:57.078+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:57.104+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:33:57.138+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:57.137+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:57.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:57.165+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:33:57.186+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T03:34:27.250+0000] {processor.py:161} INFO - Started process (PID=1013) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:27.251+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:34:27.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:27.253+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:27.283+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:27.316+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:27.316+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:27.342+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:27.342+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:34:27.367+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T03:34:57.569+0000] {processor.py:161} INFO - Started process (PID=1022) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:57.571+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:34:57.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:57.573+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:57.605+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:34:57.640+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:57.640+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:57.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:57.676+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:34:57.771+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.206 seconds
[2025-06-04T03:35:27.935+0000] {processor.py:161} INFO - Started process (PID=1030) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:27.937+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:35:27.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:27.939+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:27.967+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:28.017+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:28.017+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:28.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:28.041+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:35:28.064+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
[2025-06-04T03:35:59.056+0000] {processor.py:161} INFO - Started process (PID=1039) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:59.057+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:35:59.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:59.061+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:59.088+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:35:59.125+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:59.125+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:59.151+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:59.151+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:35:59.179+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T03:36:29.660+0000] {processor.py:161} INFO - Started process (PID=1048) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:36:29.661+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:36:29.664+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:29.664+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:36:29.697+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:36:29.730+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:29.730+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:36:29.757+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:29.757+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:36:29.783+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T03:37:02.068+0000] {processor.py:161} INFO - Started process (PID=1057) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:02.070+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:37:02.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:02.075+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:02.108+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:02.172+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:02.171+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:02.206+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:02.205+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:37:02.233+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.170 seconds
[2025-06-04T03:37:33.674+0000] {processor.py:161} INFO - Started process (PID=1066) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:33.675+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:37:33.680+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:33.679+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:33.708+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:37:33.746+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:33.746+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:33.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:33.772+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:37:33.799+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T03:38:06.040+0000] {processor.py:161} INFO - Started process (PID=1075) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:06.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:38:06.044+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:06.043+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:06.071+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:06.102+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:06.102+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:06.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:06.125+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:38:06.149+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T03:38:37.849+0000] {processor.py:161} INFO - Started process (PID=1083) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:37.932+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:38:38.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:38.545+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:39.336+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:38:39.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:39.542+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:39.592+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 1.790 seconds
[2025-06-04T03:39:09.921+0000] {processor.py:161} INFO - Started process (PID=1092) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:09.922+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:39:09.928+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:09.927+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:09.954+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:09.987+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:09.987+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:10.034+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T03:39:41.298+0000] {processor.py:161} INFO - Started process (PID=1101) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:41.300+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:39:41.303+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.302+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:41.333+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:39:41.369+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.369+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:41.422+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T03:40:13.681+0000] {processor.py:161} INFO - Started process (PID=1110) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:13.684+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:40:13.687+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.687+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:13.727+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:13.763+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.763+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:13.815+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.145 seconds
[2025-06-04T03:40:45.209+0000] {processor.py:161} INFO - Started process (PID=1118) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:45.210+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:40:45.213+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.213+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:45.245+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:40:45.279+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.278+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:45.331+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T03:41:17.490+0000] {processor.py:161} INFO - Started process (PID=1126) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:17.491+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:41:17.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.495+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:17.523+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:17.561+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.561+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:17.611+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T03:41:48.977+0000] {processor.py:161} INFO - Started process (PID=1135) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:48.980+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:41:48.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:48.982+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:49.024+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:41:49.057+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.056+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:49.104+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T03:42:21.361+0000] {processor.py:161} INFO - Started process (PID=1144) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:21.362+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:42:21.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:21.395+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:21.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.431+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:21.482+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T03:42:53.042+0000] {processor.py:161} INFO - Started process (PID=1153) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:53.052+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:42:53.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:53.062+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:53.140+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:42:53.252+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:53.240+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:53.404+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.366 seconds
[2025-06-04T03:43:25.416+0000] {processor.py:161} INFO - Started process (PID=1161) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:25.420+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:43:25.422+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.422+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:25.449+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:25.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.487+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:25.534+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:43:56.817+0000] {processor.py:161} INFO - Started process (PID=1170) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:56.819+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:43:56.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:56.822+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:56.848+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:43:56.884+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:56.883+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:56.909+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:56.909+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:43:56.935+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T03:44:29.307+0000] {processor.py:161} INFO - Started process (PID=1179) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:44:29.312+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:44:29.316+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.315+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:44:29.344+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:44:29.384+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.384+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:44:29.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.449+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:44:29.496+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.193 seconds
[2025-06-04T03:45:00.763+0000] {processor.py:161} INFO - Started process (PID=1188) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:00.765+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:45:00.768+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.767+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:00.810+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:00.846+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.845+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:00.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.874+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:45:00.901+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.142 seconds
[2025-06-04T03:45:33.105+0000] {processor.py:161} INFO - Started process (PID=1197) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:33.107+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:45:33.110+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.109+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:33.144+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:45:33.183+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.182+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:33.207+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.207+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:45:33.231+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T03:46:03.356+0000] {processor.py:161} INFO - Started process (PID=1205) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:03.358+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:46:03.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:03.363+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:04.629+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:04.663+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.663+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:04.686+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.686+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:46:04.709+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:46:36.964+0000] {processor.py:161} INFO - Started process (PID=1214) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:36.965+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:46:36.972+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:36.971+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:37.002+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:46:37.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.068+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:37.101+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.100+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:46:37.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.177 seconds
[2025-06-04T03:47:08.844+0000] {processor.py:161} INFO - Started process (PID=1223) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:08.846+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:47:08.854+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:08.853+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:08.889+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:08.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:08.925+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:08.951+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:08.950+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:47:08.980+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.140 seconds
[2025-06-04T03:47:39.099+0000] {processor.py:161} INFO - Started process (PID=1232) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:39.100+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:47:39.102+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:39.102+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:39.134+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:39.171+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:39.170+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:39.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:39.199+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:47:39.225+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T03:47:48.595+0000] {processor.py:161} INFO - Started process (PID=1235) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:48.597+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:47:48.599+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:48.599+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:48.642+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:47:48.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:48.675+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:48.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:48.720+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:47:48.751+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.160 seconds
[2025-06-04T03:48:00.844+0000] {processor.py:161} INFO - Started process (PID=1242) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:00.845+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:48:00.847+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:00.847+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:00.889+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:00.926+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:00.926+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:00.953+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:00.953+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:48:00.981+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.142 seconds
[2025-06-04T03:48:31.413+0000] {processor.py:161} INFO - Started process (PID=1251) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:31.415+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:48:31.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:31.417+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:31.450+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:48:31.492+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:31.491+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:31.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:31.516+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:48:31.541+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T03:49:01.626+0000] {processor.py:161} INFO - Started process (PID=1260) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:01.627+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:49:01.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:01.632+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:01.670+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:01.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:01.712+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:01.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:01.771+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:49:01.815+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.194 seconds
[2025-06-04T03:49:32.345+0000] {processor.py:161} INFO - Started process (PID=1269) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:32.346+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:49:32.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:32.349+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:32.416+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:49:32.472+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:32.472+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:32.518+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:32.518+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:49:32.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.209 seconds
[2025-06-04T03:50:02.733+0000] {processor.py:161} INFO - Started process (PID=1272) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:02.735+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:50:02.740+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:02.739+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:02.865+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:02.897+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:02.897+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:02.918+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:02.918+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:50:02.945+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.215 seconds
[2025-06-04T03:50:33.262+0000] {processor.py:161} INFO - Started process (PID=1282) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:33.264+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:50:33.266+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:33.266+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:33.295+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:50:33.334+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:33.334+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:33.368+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:33.368+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:50:33.399+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.140 seconds
[2025-06-04T03:51:03.781+0000] {processor.py:161} INFO - Started process (PID=1292) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:03.782+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:51:03.785+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:03.784+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:03.808+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:03.839+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:03.839+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:03.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:03.863+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:51:03.886+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T03:51:34.181+0000] {processor.py:161} INFO - Started process (PID=1301) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:34.183+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:51:34.185+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:34.185+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:34.213+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:51:34.250+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:34.250+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:34.275+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:34.275+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:51:34.299+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T03:52:04.744+0000] {processor.py:161} INFO - Started process (PID=1310) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:04.746+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:52:04.750+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:04.749+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:04.778+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:04.811+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:04.811+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:52:04.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:04.837+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:52:04.863+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:52:35.199+0000] {processor.py:161} INFO - Started process (PID=1319) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:35.200+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:52:35.203+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:35.202+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:35.232+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:52:35.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:35.268+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:52:35.322+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T03:53:05.552+0000] {processor.py:161} INFO - Started process (PID=1329) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:05.553+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:53:05.556+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:05.555+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:05.583+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:05.619+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:05.619+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:05.665+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T03:53:36.036+0000] {processor.py:161} INFO - Started process (PID=1337) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:36.038+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:53:36.043+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:36.043+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:36.074+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:53:36.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:36.111+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:36.163+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T03:54:06.440+0000] {processor.py:161} INFO - Started process (PID=1346) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:06.441+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:54:06.447+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:06.446+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:06.472+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:06.510+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:06.509+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:06.562+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T03:54:36.788+0000] {processor.py:161} INFO - Started process (PID=1353) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:36.790+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:54:36.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:36.792+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:36.826+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:54:36.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:36.866+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:36.915+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.131 seconds
[2025-06-04T03:55:06.991+0000] {processor.py:161} INFO - Started process (PID=1362) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:06.993+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:55:06.999+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:06.998+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:07.034+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:07.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:07.074+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:08.373+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.156 seconds
[2025-06-04T03:55:40.670+0000] {processor.py:161} INFO - Started process (PID=1371) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:40.672+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:55:40.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.674+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:40.701+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:55:40.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.739+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:40.799+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T03:56:10.949+0000] {processor.py:161} INFO - Started process (PID=1380) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:10.952+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:56:10.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:10.954+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:10.986+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:11.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:11.019+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:11.068+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T03:56:41.604+0000] {processor.py:161} INFO - Started process (PID=1389) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:41.607+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:56:41.609+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:41.609+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:41.639+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:56:41.671+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:41.671+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:41.719+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:57:11.957+0000] {processor.py:161} INFO - Started process (PID=1398) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:11.965+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:57:11.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:11.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:11.995+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:12.044+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:12.041+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:12.116+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.164 seconds
[2025-06-04T03:57:42.388+0000] {processor.py:161} INFO - Started process (PID=1407) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:42.390+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:57:42.392+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:42.392+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:42.424+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:57:42.460+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:42.460+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:42.487+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:42.487+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:57:42.512+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T03:58:12.797+0000] {processor.py:161} INFO - Started process (PID=1416) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:12.803+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:58:12.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:12.809+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:12.854+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:12.910+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:12.910+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:12.942+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:12.942+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:58:12.968+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.175 seconds
[2025-06-04T03:58:43.398+0000] {processor.py:161} INFO - Started process (PID=1425) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:43.400+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:58:43.403+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:43.402+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:43.435+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:58:43.476+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:43.475+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:43.500+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:43.500+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:58:43.527+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T03:59:13.823+0000] {processor.py:161} INFO - Started process (PID=1434) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:13.824+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:59:13.827+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:13.827+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:13.855+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:13.891+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:13.890+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:13.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:13.915+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:59:13.941+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T03:59:44.194+0000] {processor.py:161} INFO - Started process (PID=1443) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:44.196+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T03:59:44.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:44.198+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:44.227+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T03:59:44.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:44.261+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:44.288+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:44.288+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T03:59:44.316+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T04:00:14.627+0000] {processor.py:161} INFO - Started process (PID=1451) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:14.629+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:00:14.634+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:14.633+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:14.663+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:14.711+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:14.710+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:14.796+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:14.796+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:00:14.860+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.238 seconds
[2025-06-04T04:00:45.178+0000] {processor.py:161} INFO - Started process (PID=1454) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:45.180+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:00:45.186+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:45.185+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:45.223+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:00:45.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:45.282+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:45.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:45.316+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:00:45.360+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.186 seconds
[2025-06-04T04:01:14.489+0000] {processor.py:161} INFO - Started process (PID=1464) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:14.491+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:01:14.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:14.495+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:14.543+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:14.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:14.574+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:14.599+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:14.598+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:01:14.631+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.145 seconds
[2025-06-04T04:01:44.997+0000] {processor.py:161} INFO - Started process (PID=1473) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:45.001+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:01:45.005+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:45.005+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:45.038+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:01:45.073+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:45.073+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:45.099+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:45.099+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:01:45.119+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T04:02:15.417+0000] {processor.py:161} INFO - Started process (PID=1482) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:15.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:02:15.421+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:15.421+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:15.448+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:15.482+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:15.482+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:15.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:15.506+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:02:15.532+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T04:02:45.918+0000] {processor.py:161} INFO - Started process (PID=1491) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:45.919+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:02:45.922+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:45.921+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:45.947+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:02:45.978+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:45.978+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:46.003+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:46.003+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:02:46.026+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T04:03:16.560+0000] {processor.py:161} INFO - Started process (PID=1500) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:16.562+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:03:16.565+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:16.565+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:16.589+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:16.621+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:16.621+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:16.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:16.644+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:03:16.666+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T04:03:46.978+0000] {processor.py:161} INFO - Started process (PID=1509) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:46.980+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:03:46.982+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:46.982+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:47.010+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:03:47.043+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:47.043+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:47.067+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:47.066+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:03:47.091+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T04:04:17.647+0000] {processor.py:161} INFO - Started process (PID=1518) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:17.649+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:04:17.653+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:17.652+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:17.679+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:17.725+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:17.725+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:04:17.756+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:17.756+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:04:17.779+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.136 seconds
[2025-06-04T04:04:48.127+0000] {processor.py:161} INFO - Started process (PID=1527) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:48.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:04:48.131+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:48.131+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:48.157+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:04:48.190+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:48.190+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:04:48.216+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:48.216+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:04:48.240+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T04:05:18.591+0000] {processor.py:161} INFO - Started process (PID=1536) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:18.592+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:05:18.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:18.595+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:18.626+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:18.659+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:18.658+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:05:18.683+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:18.682+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:05:18.704+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T04:05:49.022+0000] {processor.py:161} INFO - Started process (PID=1545) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:49.023+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:05:49.026+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:49.026+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:49.053+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:05:49.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:49.087+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:05:49.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:49.117+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:05:49.143+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T04:06:19.446+0000] {processor.py:161} INFO - Started process (PID=1554) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:19.450+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:06:19.469+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:19.469+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:20.760+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:20.847+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:20.846+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:06:20.891+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:20.891+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:06:20.927+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.267 seconds
[2025-06-04T04:06:53.476+0000] {processor.py:161} INFO - Started process (PID=1563) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:53.480+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:06:53.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:53.483+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:53.562+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:06:53.606+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:53.606+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:06:53.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:53.633+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:06:53.659+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.188 seconds
[2025-06-04T04:07:24.963+0000] {processor.py:161} INFO - Started process (PID=1571) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:24.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:07:24.970+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:24.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:25.009+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:25.096+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:25.096+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:07:25.131+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:25.130+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:07:25.152+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.194 seconds
[2025-06-04T04:07:57.467+0000] {processor.py:161} INFO - Started process (PID=1580) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:57.469+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:07:57.471+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:57.471+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:57.505+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:07:57.539+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:57.539+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:07:57.565+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:57.565+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:07:57.585+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T04:08:28.920+0000] {processor.py:161} INFO - Started process (PID=1589) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:08:28.922+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:08:28.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:08:28.925+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:08:28.958+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:08:29.000+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:08:28.999+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:08:29.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:08:29.023+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:08:29.132+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.217 seconds
[2025-06-04T04:09:00.934+0000] {processor.py:161} INFO - Started process (PID=1599) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:00.935+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:09:00.937+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:00.937+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:00.961+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:00.993+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:00.992+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:09:01.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:01.019+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:09:01.045+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T04:09:33.376+0000] {processor.py:161} INFO - Started process (PID=1608) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:33.377+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:09:33.379+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:33.379+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:33.412+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:09:33.470+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:33.470+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:09:33.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:33.516+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:09:33.557+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.189 seconds
[2025-06-04T04:10:03.633+0000] {processor.py:161} INFO - Started process (PID=1617) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:03.636+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:10:03.638+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:03.638+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:04.903+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:04.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:04.938+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:10:04.964+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:04.964+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:10:04.990+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T04:10:37.336+0000] {processor.py:161} INFO - Started process (PID=1625) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:37.339+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:10:37.341+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:37.341+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:37.373+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:10:37.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:37.423+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:10:37.448+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:37.448+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:10:37.471+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.140 seconds
[2025-06-04T04:11:09.574+0000] {processor.py:161} INFO - Started process (PID=1635) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:09.578+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:11:09.582+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:09.581+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:09.623+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:09.657+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:09.657+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:11:09.691+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:09.690+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:11:09.714+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.145 seconds
[2025-06-04T04:11:41.023+0000] {processor.py:161} INFO - Started process (PID=1643) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:41.025+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:11:41.033+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:41.032+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:41.071+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:11:41.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:41.113+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:11:41.141+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:41.141+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:11:41.162+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T04:12:13.523+0000] {processor.py:161} INFO - Started process (PID=1652) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:13.524+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:12:13.527+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:13.527+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:13.569+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:13.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:13.612+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:12:13.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:13.649+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:12:13.678+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.160 seconds
[2025-06-04T04:12:44.975+0000] {processor.py:161} INFO - Started process (PID=1661) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:44.976+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:12:44.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:44.978+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:45.012+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:12:45.048+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:45.047+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:12:45.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:45.071+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:12:45.093+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T04:13:17.713+0000] {processor.py:161} INFO - Started process (PID=1670) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:17.715+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:13:17.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:17.717+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:17.746+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:17.782+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:17.782+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:13:17.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:17.810+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:13:17.834+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T04:13:49.147+0000] {processor.py:161} INFO - Started process (PID=1679) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:49.149+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:13:49.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:49.155+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:49.186+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:13:49.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:49.223+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:13:49.251+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:49.251+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:13:49.279+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.136 seconds
[2025-06-04T04:14:21.794+0000] {processor.py:161} INFO - Started process (PID=1688) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:21.795+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:14:21.798+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:21.797+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:21.826+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:21.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:21.858+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:14:21.885+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:21.884+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:14:21.921+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T04:14:53.177+0000] {processor.py:161} INFO - Started process (PID=1696) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:53.178+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:14:53.181+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:53.181+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:53.219+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:14:53.258+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:53.258+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:14:53.304+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:53.303+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:14:53.330+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.158 seconds
[2025-06-04T04:15:25.668+0000] {processor.py:161} INFO - Started process (PID=1705) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:25.672+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:15:25.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:25.674+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:25.718+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:25.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:25.758+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:15:25.794+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:25.794+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:15:25.830+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.166 seconds
[2025-06-04T04:15:57.158+0000] {processor.py:161} INFO - Started process (PID=1713) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:57.160+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:15:57.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:57.162+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:57.201+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:15:57.238+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:57.237+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:15:57.265+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:57.265+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:15:57.289+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.136 seconds
[2025-06-04T04:16:29.786+0000] {processor.py:161} INFO - Started process (PID=1722) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:16:29.788+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:16:29.790+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:16:29.790+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:16:29.832+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:16:29.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:16:29.866+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:16:29.893+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:16:29.893+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:16:29.917+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T04:17:01.238+0000] {processor.py:161} INFO - Started process (PID=1731) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:01.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:17:01.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:01.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:01.305+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:01.352+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:01.351+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:17:01.383+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:01.383+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:17:01.413+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.179 seconds
[2025-06-04T04:17:33.800+0000] {processor.py:161} INFO - Started process (PID=1739) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:33.802+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:17:33.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:33.809+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:33.843+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:17:33.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:33.889+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:17:33.942+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:33.942+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:17:33.982+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.187 seconds
[2025-06-04T04:18:05.396+0000] {processor.py:161} INFO - Started process (PID=1748) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:05.397+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:18:05.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:05.400+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:05.432+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:05.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:05.466+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:18:05.493+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:05.493+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:18:05.515+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T04:18:36.420+0000] {processor.py:161} INFO - Started process (PID=1757) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:36.423+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:18:36.429+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:36.428+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:36.451+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:18:37.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:37.507+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:18:37.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:37.573+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:18:37.630+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 1.221 seconds
[2025-06-04T04:27:15.570+0000] {processor.py:161} INFO - Started process (PID=61) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:15.593+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:27:15.596+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:15.595+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:15.669+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:15.793+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:15.792+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:27:15.858+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:15.858+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:27:15.887+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.331 seconds
[2025-06-04T04:27:46.673+0000] {processor.py:161} INFO - Started process (PID=70) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:46.676+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:27:46.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:46.681+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:46.727+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:27:46.770+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:46.770+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:27:46.802+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:46.802+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:27:46.834+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.170 seconds
[2025-06-04T04:28:16.893+0000] {processor.py:161} INFO - Started process (PID=79) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:16.895+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:28:16.899+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:16.899+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:16.920+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:16.957+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:16.956+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:28:16.987+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:16.987+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:28:17.032+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.142 seconds
[2025-06-04T04:28:47.611+0000] {processor.py:161} INFO - Started process (PID=88) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:47.612+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:28:47.618+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:47.617+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:47.656+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:28:47.691+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:47.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:28:47.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:47.718+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:28:47.767+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.162 seconds
[2025-06-04T04:29:18.407+0000] {processor.py:161} INFO - Started process (PID=97) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:18.408+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:29:18.411+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:18.411+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:18.448+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:18.491+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:18.491+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:29:18.534+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:18.534+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:29:18.573+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.171 seconds
[2025-06-04T04:29:49.850+0000] {processor.py:161} INFO - Started process (PID=106) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:49.852+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:29:49.855+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:49.854+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:49.887+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:29:49.941+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:49.941+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:29:49.987+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:49.986+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:29:50.046+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.199 seconds
[2025-06-04T04:30:21.320+0000] {processor.py:161} INFO - Started process (PID=115) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:30:21.322+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:30:21.325+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:30:21.324+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:30:21.380+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:30:21.444+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:30:21.443+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:30:21.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:30:21.498+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:30:21.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.228 seconds
[2025-06-04T04:31:36.756+0000] {processor.py:161} INFO - Started process (PID=57) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:31:36.757+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:31:36.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:31:36.761+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:31:36.855+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:31:37.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:31:37.399+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:31:37.493+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:31:37.493+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:31:37.582+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.832 seconds
[2025-06-04T04:32:08.182+0000] {processor.py:161} INFO - Started process (PID=60) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:08.193+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:32:08.208+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:08.208+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:08.444+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:08.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:08.612+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:32:08.681+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:08.681+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:32:09.138+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.961 seconds
[2025-06-04T04:32:39.453+0000] {processor.py:161} INFO - Started process (PID=68) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:39.456+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:32:39.460+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:39.460+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:39.643+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:32:39.698+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:39.697+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:32:39.730+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:39.730+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:32:39.780+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.344 seconds
[2025-06-04T04:33:10.114+0000] {processor.py:161} INFO - Started process (PID=77) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:10.138+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:33:10.141+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:10.141+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:10.180+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:10.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:10.212+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:33:10.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:10.236+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:33:10.263+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.155 seconds
[2025-06-04T04:33:40.482+0000] {processor.py:161} INFO - Started process (PID=86) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:40.484+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:33:40.509+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:40.506+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:40.582+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:33:40.627+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:40.626+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:33:40.672+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.194 seconds
[2025-06-04T04:34:10.763+0000] {processor.py:161} INFO - Started process (PID=95) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:10.766+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:34:10.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:10.768+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:10.797+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:10.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:10.834+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:34:10.874+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T04:34:41.106+0000] {processor.py:161} INFO - Started process (PID=104) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:41.108+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:34:41.111+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:41.110+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:41.141+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:34:41.175+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:41.175+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:34:41.236+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.136 seconds
[2025-06-04T04:35:11.556+0000] {processor.py:161} INFO - Started process (PID=113) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:11.558+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:35:11.571+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:11.570+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:12.046+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:12.140+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:12.140+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:35:12.308+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.765 seconds
[2025-06-04T04:35:43.129+0000] {processor.py:161} INFO - Started process (PID=122) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:43.170+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:35:43.175+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:43.175+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:43.284+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:35:43.331+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:43.331+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:35:43.396+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.276 seconds
[2025-06-04T04:36:13.562+0000] {processor.py:161} INFO - Started process (PID=131) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:13.564+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:36:13.567+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:13.566+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:13.603+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:13.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:13.632+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:36:13.671+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T04:36:45.224+0000] {processor.py:161} INFO - Started process (PID=140) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:45.228+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:36:45.230+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:45.230+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:45.261+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:36:45.298+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:45.298+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:36:45.346+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T04:37:16.783+0000] {processor.py:161} INFO - Started process (PID=149) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:16.785+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:37:16.787+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:16.787+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:16.816+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:16.851+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:16.850+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:37:16.897+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T04:37:49.222+0000] {processor.py:161} INFO - Started process (PID=158) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:49.224+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:37:49.227+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:49.226+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:49.259+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:37:49.295+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:49.294+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:37:49.352+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T04:38:20.590+0000] {processor.py:161} INFO - Started process (PID=167) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:20.593+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:38:20.596+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:20.596+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:20.623+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:20.653+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:20.653+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:38:20.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T04:38:50.838+0000] {processor.py:161} INFO - Started process (PID=176) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:50.839+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:38:50.842+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:50.842+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:50.873+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:38:50.909+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:50.908+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:38:50.962+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T04:39:21.152+0000] {processor.py:161} INFO - Started process (PID=184) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:21.158+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:39:21.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.166+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:21.218+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:21.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.282+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:39:21.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.325+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:39:21.356+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.218 seconds
[2025-06-04T04:39:51.729+0000] {processor.py:161} INFO - Started process (PID=193) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:51.732+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:39:51.735+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.735+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:51.761+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:39:51.799+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.798+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:39:51.824+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.824+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:39:51.848+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T04:40:22.598+0000] {processor.py:161} INFO - Started process (PID=202) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:22.600+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:40:22.603+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.602+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:22.633+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:22.670+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.670+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:40:22.700+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.699+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:40:22.726+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T04:40:53.050+0000] {processor.py:161} INFO - Started process (PID=212) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:53.051+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:40:53.054+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.054+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:53.086+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:40:53.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.118+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:40:53.144+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.144+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:40:53.166+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T04:41:23.731+0000] {processor.py:161} INFO - Started process (PID=221) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:23.733+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:41:23.735+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.735+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:23.785+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:23.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.834+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:41:23.875+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.874+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:41:23.905+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.179 seconds
[2025-06-04T04:41:54.479+0000] {processor.py:161} INFO - Started process (PID=230) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:54.481+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:41:54.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.487+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:54.524+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:41:54.590+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.590+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:41:54.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.632+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:41:54.667+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.192 seconds
[2025-06-04T04:42:24.841+0000] {processor.py:161} INFO - Started process (PID=239) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:24.843+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:42:24.846+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.846+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:24.872+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:24.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.905+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:42:24.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.939+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:42:24.961+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.124 seconds
[2025-06-04T04:42:55.314+0000] {processor.py:161} INFO - Started process (PID=248) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:55.317+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:42:55.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.319+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:55.368+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:42:55.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.404+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:42:55.433+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.432+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:42:55.455+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.145 seconds
[2025-06-04T04:43:25.930+0000] {processor.py:161} INFO - Started process (PID=257) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:25.931+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:43:25.946+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:25.946+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:25.995+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:26.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:26.050+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:43:26.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:26.086+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:43:26.144+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.219 seconds
[2025-06-04T04:43:56.393+0000] {processor.py:161} INFO - Started process (PID=266) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:56.395+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:43:56.397+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.397+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:56.425+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:43:56.458+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.458+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:43:56.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.490+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:43:56.527+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.138 seconds
[2025-06-04T04:44:26.698+0000] {processor.py:161} INFO - Started process (PID=274) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:26.702+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:44:26.705+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.705+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:26.763+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:26.818+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.817+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:44:26.867+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.867+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:44:26.939+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.248 seconds
[2025-06-04T04:44:57.751+0000] {processor.py:161} INFO - Started process (PID=277) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:57.752+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:44:57.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:57.784+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:57.817+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:44:57.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:57.874+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:44:57.929+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.187 seconds
[2025-06-04T04:45:28.396+0000] {processor.py:161} INFO - Started process (PID=286) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:28.398+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:45:28.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:28.400+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:28.426+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:28.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:28.466+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:45:28.515+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T04:45:58.889+0000] {processor.py:161} INFO - Started process (PID=295) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:58.894+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:45:58.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:58.897+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:58.929+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:45:58.966+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:58.966+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:45:59.013+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.128 seconds
[2025-06-04T04:46:29.377+0000] {processor.py:161} INFO - Started process (PID=304) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:29.380+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:46:29.383+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:29.383+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:29.415+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:29.457+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:29.456+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:46:29.509+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.137 seconds
[2025-06-04T04:46:59.931+0000] {processor.py:161} INFO - Started process (PID=313) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:59.933+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:46:59.936+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:59.936+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:59.965+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:46:59.998+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:59.998+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:47:00.049+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T04:47:30.486+0000] {processor.py:161} INFO - Started process (PID=322) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:47:30.492+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:47:30.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:30.496+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:47:30.541+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:47:30.594+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:30.594+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:47:30.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.215 seconds
[2025-06-04T04:48:01.146+0000] {processor.py:161} INFO - Started process (PID=331) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:01.147+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:48:01.150+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:01.149+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:01.175+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:01.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:01.214+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:48:01.318+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.177 seconds
[2025-06-04T04:48:31.677+0000] {processor.py:161} INFO - Started process (PID=340) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:31.679+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:48:31.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:31.681+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:31.711+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:48:31.743+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:31.743+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:48:31.794+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T04:49:03.171+0000] {processor.py:161} INFO - Started process (PID=349) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:03.172+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:49:03.175+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:03.174+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:03.208+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:03.239+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:03.239+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:49:03.287+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T04:49:35.722+0000] {processor.py:161} INFO - Started process (PID=358) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:35.723+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:49:35.728+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:35.727+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:35.762+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:49:35.805+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:35.805+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:49:35.853+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T04:50:07.344+0000] {processor.py:161} INFO - Started process (PID=367) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:07.346+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:50:07.353+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:07.352+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:07.384+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:07.426+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:07.426+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:50:07.481+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T04:50:38.637+0000] {processor.py:161} INFO - Started process (PID=376) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:38.638+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:50:38.641+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.640+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:38.671+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:50:38.705+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.705+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:50:38.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.730+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:50:38.753+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T04:51:09.094+0000] {processor.py:161} INFO - Started process (PID=385) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:09.095+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:51:09.099+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.098+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:09.133+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:09.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.169+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:51:09.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.193+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:51:09.216+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T04:51:40.101+0000] {processor.py:161} INFO - Started process (PID=394) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:40.102+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:51:40.107+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.106+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:40.134+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:51:40.168+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.168+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:51:40.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.193+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:51:40.213+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T04:52:10.704+0000] {processor.py:161} INFO - Started process (PID=403) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:10.705+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:52:10.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.708+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:10.736+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:10.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.771+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:52:10.795+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.794+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:52:10.818+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T04:52:41.374+0000] {processor.py:161} INFO - Started process (PID=412) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:41.375+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:52:41.378+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.378+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:41.410+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:52:41.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.449+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:52:41.478+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.478+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:52:41.501+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T04:53:11.954+0000] {processor.py:161} INFO - Started process (PID=421) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:11.957+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:53:11.960+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:11.960+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:11.987+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:12.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:12.024+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:53:12.054+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:12.053+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:53:12.089+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T04:53:43.050+0000] {processor.py:161} INFO - Started process (PID=430) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:43.053+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:53:43.056+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.055+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:43.085+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:53:43.120+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.120+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:53:43.152+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.151+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:53:43.179+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T04:54:13.945+0000] {processor.py:161} INFO - Started process (PID=438) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:13.947+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:54:13.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:13.949+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:13.987+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:14.025+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:14.025+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:54:14.054+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:14.054+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:54:14.074+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
[2025-06-04T04:54:44.474+0000] {processor.py:161} INFO - Started process (PID=447) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:44.478+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:54:44.481+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.481+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:44.515+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:54:44.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:54:44.589+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.588+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T04:54:44.614+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.144 seconds
[2025-06-04T04:55:15.193+0000] {processor.py:161} INFO - Started process (PID=456) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:15.195+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:55:15.197+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:15.197+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:15.224+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:15.255+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:15.255+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:55:15.299+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T04:55:45.912+0000] {processor.py:161} INFO - Started process (PID=465) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:45.914+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:55:45.917+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:45.916+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:45.941+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:55:45.974+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:45.974+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:55:46.022+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.114 seconds
[2025-06-04T04:56:16.564+0000] {processor.py:161} INFO - Started process (PID=468) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:16.565+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:56:16.568+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:16.567+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:16.592+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:16.626+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:16.626+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:56:16.677+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T04:56:47.197+0000] {processor.py:161} INFO - Started process (PID=477) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:47.198+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:56:47.201+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:47.201+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:47.231+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:56:47.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:47.260+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:56:47.305+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T04:57:17.761+0000] {processor.py:161} INFO - Started process (PID=486) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:17.762+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:57:17.765+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:17.764+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:17.787+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:17.818+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:17.818+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:57:17.862+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T04:57:48.399+0000] {processor.py:161} INFO - Started process (PID=495) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:48.400+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:57:48.403+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:48.403+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:48.437+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:57:48.473+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:48.472+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:57:48.515+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T04:58:19.019+0000] {processor.py:161} INFO - Started process (PID=504) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:19.020+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:58:19.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:19.022+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:19.049+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:19.082+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:19.081+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:58:19.126+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T04:58:49.690+0000] {processor.py:161} INFO - Started process (PID=513) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:49.692+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:58:49.698+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:49.698+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:49.723+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:58:49.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:49.755+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:58:49.804+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T04:59:20.328+0000] {processor.py:161} INFO - Started process (PID=522) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:20.329+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:59:20.332+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:20.331+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:20.362+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:20.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:20.398+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:59:20.444+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T04:59:51.015+0000] {processor.py:161} INFO - Started process (PID=531) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:51.016+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T04:59:51.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:51.018+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:51.051+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T04:59:51.082+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:51.082+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:59:51.132+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T05:00:21.651+0000] {processor.py:161} INFO - Started process (PID=540) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:21.652+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:00:21.655+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:21.654+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:21.685+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:21.722+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:21.721+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:00:21.984+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.338 seconds
[2025-06-04T05:00:52.494+0000] {processor.py:161} INFO - Started process (PID=549) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:52.498+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:00:52.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:52.534+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:00:52.570+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.570+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:00:52.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.595+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:00:52.622+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T05:01:23.121+0000] {processor.py:161} INFO - Started process (PID=558) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:23.124+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:01:23.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:23.126+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:23.156+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:23.202+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:23.201+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:01:24.811+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:24.810+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:01:24.844+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.342 seconds
[2025-06-04T05:01:55.374+0000] {processor.py:161} INFO - Started process (PID=567) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:55.376+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:01:55.383+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.381+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:55.416+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:01:55.461+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.460+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:01:55.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.489+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:01:56.895+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.145 seconds
[2025-06-04T05:02:29.600+0000] {processor.py:161} INFO - Started process (PID=576) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:29.601+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:02:29.604+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.604+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:29.634+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:29.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.858+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:02:29.885+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.884+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:02:29.917+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.322 seconds
[2025-06-04T05:02:42.997+0000] {processor.py:161} INFO - Started process (PID=578) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:42.999+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:02:43.002+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:43.001+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:43.037+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:43.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:43.068+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:02:43.090+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:43.089+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:02:43.119+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T05:02:51.228+0000] {processor.py:161} INFO - Started process (PID=586) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:51.230+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:02:51.232+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:51.232+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:51.268+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:02:51.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:51.546+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:02:51.566+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:51.566+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:02:51.592+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.368 seconds
[2025-06-04T05:03:22.168+0000] {processor.py:161} INFO - Started process (PID=595) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:22.171+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:03:22.174+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:22.173+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:22.198+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:22.376+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:22.376+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:03:22.397+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:22.397+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:03:22.416+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.255 seconds
[2025-06-04T05:03:52.941+0000] {processor.py:161} INFO - Started process (PID=604) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:52.943+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:03:52.947+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:52.946+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:52.979+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:03:53.027+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:53.026+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:03:53.059+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:53.058+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:03:53.099+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.163 seconds
[2025-06-04T05:04:23.735+0000] {processor.py:161} INFO - Started process (PID=614) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:23.736+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:04:23.740+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:23.740+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:23.762+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:23.794+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:23.794+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:04:23.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:23.821+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:04:23.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
[2025-06-04T05:04:54.368+0000] {processor.py:161} INFO - Started process (PID=624) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:54.370+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:04:54.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:54.372+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:54.397+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:04:54.429+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:54.429+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:04:54.452+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:54.452+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:04:54.472+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T05:05:25.018+0000] {processor.py:161} INFO - Started process (PID=627) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:25.020+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:05:25.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:25.022+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:25.048+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:25.079+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:25.078+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:05:25.101+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:25.101+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:05:25.123+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:05:55.758+0000] {processor.py:161} INFO - Started process (PID=636) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:55.760+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:05:55.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:55.762+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:55.791+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:05:55.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:55.837+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:05:55.878+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:55.877+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:05:55.902+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.149 seconds
[2025-06-04T05:06:26.523+0000] {processor.py:161} INFO - Started process (PID=645) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:26.525+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:06:26.528+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:26.528+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:26.549+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:26.583+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:26.583+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:06:26.607+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:26.606+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:06:26.627+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T05:06:57.550+0000] {processor.py:161} INFO - Started process (PID=654) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:57.552+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:06:57.554+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:57.554+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:57.579+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:06:57.610+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:57.610+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:06:57.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:57.632+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:06:57.655+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:07:28.264+0000] {processor.py:161} INFO - Started process (PID=663) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:28.266+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:07:28.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:28.268+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:28.294+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:28.327+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:28.327+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:07:28.356+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:28.355+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:07:28.388+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T05:07:58.621+0000] {processor.py:161} INFO - Started process (PID=672) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:58.623+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:07:58.625+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:58.625+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:58.648+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:07:58.678+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:58.678+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:07:58.701+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:07:58.701+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:07:58.723+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T05:08:29.375+0000] {processor.py:161} INFO - Started process (PID=681) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:08:29.376+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:08:29.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:08:29.380+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:08:29.405+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:08:29.439+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:08:29.438+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:08:29.464+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:08:29.464+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:08:29.485+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.114 seconds
[2025-06-04T05:09:00.103+0000] {processor.py:161} INFO - Started process (PID=690) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:00.105+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:09:00.107+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:00.107+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:00.131+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:00.163+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:00.163+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:09:00.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:00.186+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:09:00.208+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:09:30.906+0000] {processor.py:161} INFO - Started process (PID=699) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:30.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:09:30.911+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:30.911+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:30.932+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:09:30.977+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:30.977+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:09:31.003+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:09:31.002+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:09:31.025+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T05:10:01.773+0000] {processor.py:161} INFO - Started process (PID=708) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:01.775+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:10:01.778+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:01.777+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:01.806+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:01.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:01.837+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:10:01.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:01.859+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:10:01.880+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T05:10:32.110+0000] {processor.py:161} INFO - Started process (PID=717) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:32.112+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:10:32.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:32.114+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:32.137+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:10:32.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:32.168+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:10:32.191+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:10:32.190+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:10:32.212+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:11:03.761+0000] {processor.py:161} INFO - Started process (PID=727) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:03.763+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:11:03.766+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:03.766+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:03.795+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:03.830+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:03.829+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:11:03.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:03.859+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:11:03.883+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T05:11:34.614+0000] {processor.py:161} INFO - Started process (PID=736) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:34.622+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:11:34.628+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:34.627+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:34.743+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:11:34.795+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:34.794+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:11:34.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:11:34.832+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:11:34.925+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.324 seconds
[2025-06-04T05:12:05.354+0000] {processor.py:161} INFO - Started process (PID=746) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:05.356+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:12:05.359+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:05.359+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:05.387+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:05.428+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:05.428+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:12:05.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:05.453+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:12:05.471+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T05:12:35.972+0000] {processor.py:161} INFO - Started process (PID=755) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:35.974+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:12:35.977+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:35.976+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:36.068+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:12:36.104+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:36.104+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:12:36.131+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:12:36.131+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:12:36.179+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.214 seconds
[2025-06-04T05:13:06.736+0000] {processor.py:161} INFO - Started process (PID=765) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:06.739+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:13:06.742+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:13:06.742+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:06.773+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:06.815+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:13:06.815+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:13:06.841+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:13:06.841+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:13:06.867+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T05:13:37.622+0000] {processor.py:161} INFO - Started process (PID=774) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:37.623+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:13:37.626+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:13:37.626+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:37.651+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:13:37.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:13:37.693+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:13:37.756+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.139 seconds
[2025-06-04T05:14:08.293+0000] {processor.py:161} INFO - Started process (PID=783) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:08.294+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:14:08.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:14:08.296+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:08.320+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:08.353+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:14:08.353+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:14:08.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:14:38.907+0000] {processor.py:161} INFO - Started process (PID=792) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:38.908+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:14:38.911+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:14:38.910+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:38.936+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:14:38.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:14:38.971+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:14:39.021+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T05:15:09.644+0000] {processor.py:161} INFO - Started process (PID=801) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:09.646+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:15:09.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:15:09.648+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:09.673+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:09.705+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:15:09.705+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:15:09.748+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:15:40.316+0000] {processor.py:161} INFO - Started process (PID=810) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:40.317+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:15:40.319+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:15:40.319+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:40.342+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:15:40.373+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:15:40.373+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:15:40.416+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.104 seconds
[2025-06-04T05:16:11.065+0000] {processor.py:161} INFO - Started process (PID=813) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:11.067+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:16:11.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:16:11.069+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:11.095+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:11.127+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:16:11.127+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:16:11.171+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T05:16:41.655+0000] {processor.py:161} INFO - Started process (PID=822) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:41.658+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:16:41.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:16:41.660+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:41.684+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:16:41.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:16:41.717+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:16:41.767+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T05:17:12.312+0000] {processor.py:161} INFO - Started process (PID=831) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:12.314+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:17:12.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:17:12.316+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:12.342+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:12.373+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:17:12.373+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:17:12.417+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:17:43.074+0000] {processor.py:161} INFO - Started process (PID=839) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:43.075+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:17:43.077+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:17:43.077+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:43.105+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:17:43.136+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:17:43.136+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:17:43.181+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T05:18:13.768+0000] {processor.py:161} INFO - Started process (PID=848) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:13.770+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:18:13.773+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:18:13.772+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:13.798+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:13.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:18:13.832+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:18:13.877+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T05:18:44.462+0000] {processor.py:161} INFO - Started process (PID=856) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:44.464+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:18:44.467+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:18:44.466+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:44.495+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:18:44.526+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:18:44.526+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:18:44.572+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T05:19:15.370+0000] {processor.py:161} INFO - Started process (PID=865) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:15.372+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:19:15.378+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:19:15.377+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:15.412+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:15.462+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:19:15.461+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:19:15.494+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:19:15.494+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:19:15.518+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.153 seconds
[2025-06-04T05:19:46.216+0000] {processor.py:161} INFO - Started process (PID=874) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:46.217+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:19:46.219+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:19:46.219+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:46.245+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:19:46.283+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:19:46.283+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:19:46.331+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T05:20:16.787+0000] {processor.py:161} INFO - Started process (PID=883) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:16.789+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:20:16.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:20:16.791+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:16.824+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:16.876+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:20:16.876+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:20:16.929+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.149 seconds
[2025-06-04T05:20:47.498+0000] {processor.py:161} INFO - Started process (PID=892) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:47.499+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:20:47.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:20:47.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:47.523+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:20:47.554+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:20:47.554+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:20:47.601+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:21:18.175+0000] {processor.py:161} INFO - Started process (PID=901) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:18.176+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:21:18.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:21:18.178+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:18.205+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:18.239+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:21:18.239+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:21:18.282+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T05:21:48.615+0000] {processor.py:161} INFO - Started process (PID=910) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:48.617+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:21:48.619+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:21:48.619+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:48.642+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:21:48.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:21:48.674+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:21:48.719+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:22:19.391+0000] {processor.py:161} INFO - Started process (PID=919) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:19.392+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:22:19.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:22:19.396+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:19.421+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:19.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:22:19.453+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:22:19.496+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T05:22:50.148+0000] {processor.py:161} INFO - Started process (PID=928) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:50.149+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:22:50.152+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:22:50.152+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:50.177+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:22:50.208+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:22:50.208+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:22:50.348+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.204 seconds
[2025-06-04T05:23:20.768+0000] {processor.py:161} INFO - Started process (PID=937) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:20.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:23:20.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:23:20.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:20.797+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:20.828+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:23:20.828+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:23:20.884+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T05:23:51.449+0000] {processor.py:161} INFO - Started process (PID=946) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:51.452+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:23:51.454+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:23:51.454+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:51.492+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:23:51.530+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:23:51.530+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:23:51.574+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T05:24:22.234+0000] {processor.py:161} INFO - Started process (PID=955) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:22.235+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:24:22.238+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:24:22.237+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:22.263+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:22.293+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:24:22.293+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:24:22.333+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.103 seconds
[2025-06-04T05:24:52.895+0000] {processor.py:161} INFO - Started process (PID=964) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:52.897+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:24:52.900+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:24:52.900+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:52.933+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:24:52.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:24:52.991+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:24:53.074+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.184 seconds
[2025-06-04T05:25:23.688+0000] {processor.py:161} INFO - Started process (PID=973) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:23.690+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:25:23.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:23.693+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:23.721+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:23.755+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:23.755+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:25:23.778+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:23.778+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:25:23.798+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T05:25:54.519+0000] {processor.py:161} INFO - Started process (PID=976) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:54.532+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:25:54.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:54.535+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:54.589+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:25:54.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:54.675+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:25:54.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:25:54.752+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:25:54.787+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.278 seconds
[2025-06-04T05:26:25.187+0000] {processor.py:161} INFO - Started process (PID=986) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:25.188+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:26:25.191+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:26:25.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:25.214+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:25.247+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:26:25.246+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:26:25.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:26:25.267+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:26:25.289+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.108 seconds
[2025-06-04T05:26:55.994+0000] {processor.py:161} INFO - Started process (PID=995) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:55.996+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:26:56.005+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:26:56.004+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:56.063+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:26:56.181+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:26:56.181+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:26:56.317+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.333 seconds
[2025-06-04T05:27:27.155+0000] {processor.py:161} INFO - Started process (PID=1004) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:27.158+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:27:27.161+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:27:27.160+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:27.183+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:27.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:27:27.214+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:27:27.255+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.103 seconds
[2025-06-04T05:27:57.782+0000] {processor.py:161} INFO - Started process (PID=1013) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:57.784+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:27:57.786+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:27:57.786+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:57.821+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:27:57.877+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:27:57.877+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:27:57.943+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.166 seconds
[2025-06-04T05:28:28.464+0000] {processor.py:161} INFO - Started process (PID=1022) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:28.467+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:28:28.469+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:28:28.469+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:28.493+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:28.531+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:28:28.531+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:28:28.594+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.135 seconds
[2025-06-04T05:28:59.208+0000] {processor.py:161} INFO - Started process (PID=1031) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:59.210+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:28:59.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:28:59.212+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:59.235+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:28:59.265+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:28:59.265+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:28:59.308+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.104 seconds
[2025-06-04T05:29:29.950+0000] {processor.py:161} INFO - Started process (PID=1041) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:29:29.951+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:29:29.953+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:29:29.953+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:29:29.978+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:29:30.013+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:29:30.012+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:29:30.055+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T05:30:00.677+0000] {processor.py:161} INFO - Started process (PID=1050) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:00.679+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:30:00.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:30:00.682+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:00.705+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:00.740+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:30:00.739+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:30:00.785+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T05:30:31.358+0000] {processor.py:161} INFO - Started process (PID=1059) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:31.359+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:30:31.362+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:30:31.361+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:31.385+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:30:31.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:30:31.419+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:30:31.466+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T05:31:02.012+0000] {processor.py:161} INFO - Started process (PID=1068) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:02.013+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:31:02.016+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:31:02.015+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:02.039+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:02.070+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:31:02.070+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:31:02.119+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T05:31:32.699+0000] {processor.py:161} INFO - Started process (PID=1077) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:32.701+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:31:32.703+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:31:32.703+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:32.732+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:31:32.764+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:31:32.763+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:31:32.807+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T05:32:04.534+0000] {processor.py:161} INFO - Started process (PID=1086) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:04.536+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:32:04.538+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:32:04.538+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:04.564+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:04.603+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:32:04.602+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:32:04.669+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.139 seconds
[2025-06-04T05:32:36.248+0000] {processor.py:161} INFO - Started process (PID=1095) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:36.250+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:32:36.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:32:36.252+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:36.276+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:32:36.308+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:32:36.308+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:32:36.331+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:32:36.331+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:32:36.353+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:33:06.610+0000] {processor.py:161} INFO - Started process (PID=1104) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:06.611+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:33:06.614+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:06.614+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:06.634+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:06.667+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:06.667+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:33:06.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:06.693+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:33:06.714+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:33:37.547+0000] {processor.py:161} INFO - Started process (PID=1113) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:37.549+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:33:37.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:37.552+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:37.582+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:33:37.613+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:37.613+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:33:37.637+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:33:37.636+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:33:37.659+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T05:34:08.486+0000] {processor.py:161} INFO - Started process (PID=1122) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:08.487+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:34:08.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:08.489+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:08.532+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:08.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:08.592+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:34:08.635+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:08.635+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:34:08.667+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.192 seconds
[2025-06-04T05:34:39.214+0000] {processor.py:161} INFO - Started process (PID=1131) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:39.215+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:34:39.218+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:39.218+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:39.246+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:34:39.279+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:39.279+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:34:39.300+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:34:39.300+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:34:39.323+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T05:35:10.040+0000] {processor.py:161} INFO - Started process (PID=1140) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:10.042+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:35:10.044+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:10.044+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:10.075+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:10.105+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:10.104+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:35:10.128+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:10.128+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:35:10.150+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.114 seconds
[2025-06-04T05:35:40.694+0000] {processor.py:161} INFO - Started process (PID=1149) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:40.695+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:35:40.698+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:40.698+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:40.723+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:35:40.755+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:40.755+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:35:40.776+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:35:40.776+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:35:40.797+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:36:11.397+0000] {processor.py:161} INFO - Started process (PID=1158) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:11.398+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:36:11.402+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:11.401+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:11.424+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:11.455+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:11.454+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:36:11.477+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:11.476+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:36:11.499+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T05:36:42.153+0000] {processor.py:161} INFO - Started process (PID=1167) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:42.155+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:36:42.157+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:42.157+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:42.180+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:36:42.220+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:42.220+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:36:42.250+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:36:42.249+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:36:42.269+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T05:37:12.817+0000] {processor.py:161} INFO - Started process (PID=1170) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:12.819+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:37:12.824+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:12.823+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:12.854+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:12.900+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:12.900+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:37:12.929+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:12.929+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:37:12.948+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.136 seconds
[2025-06-04T05:37:43.520+0000] {processor.py:161} INFO - Started process (PID=1179) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:43.522+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:37:43.524+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:43.524+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:43.549+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:37:43.580+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:43.579+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:37:43.601+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:37:43.601+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:37:43.623+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:38:14.199+0000] {processor.py:161} INFO - Started process (PID=1188) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:14.201+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:38:14.203+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:14.203+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:14.226+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:14.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:14.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:38:14.279+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:14.279+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:38:14.299+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.104 seconds
[2025-06-04T05:38:44.987+0000] {processor.py:161} INFO - Started process (PID=1197) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:44.988+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:38:44.991+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:44.991+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:45.012+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:38:45.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:45.041+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:38:45.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:38:45.062+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:38:45.083+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.099 seconds
[2025-06-04T05:39:15.630+0000] {processor.py:161} INFO - Started process (PID=1206) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:15.632+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:39:15.634+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:15.634+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:15.656+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:15.686+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:15.686+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:39:15.710+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:15.709+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:39:15.731+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.105 seconds
[2025-06-04T05:39:46.310+0000] {processor.py:161} INFO - Started process (PID=1215) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:46.311+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:39:46.313+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:46.313+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:46.336+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:39:46.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:46.366+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:39:46.388+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:39:46.387+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:39:46.409+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.103 seconds
[2025-06-04T05:40:17.071+0000] {processor.py:161} INFO - Started process (PID=1225) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:17.072+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:40:17.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:17.075+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:17.105+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:17.147+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:17.146+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:40:17.176+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:17.176+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:40:17.203+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.137 seconds
[2025-06-04T05:40:47.802+0000] {processor.py:161} INFO - Started process (PID=1234) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:47.804+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:40:47.806+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:47.806+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:47.830+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:40:47.860+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:47.860+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:40:47.884+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:40:47.883+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:40:47.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.107 seconds
[2025-06-04T05:41:18.564+0000] {processor.py:161} INFO - Started process (PID=1243) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:18.565+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:41:18.568+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:18.568+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:18.590+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:18.621+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:18.621+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:41:18.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:18.642+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:41:18.664+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.105 seconds
[2025-06-04T05:41:49.204+0000] {processor.py:161} INFO - Started process (PID=1252) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:49.206+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:41:49.208+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:49.208+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:49.232+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:41:49.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:49.262+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:41:49.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:41:49.284+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:41:49.306+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T05:42:20.041+0000] {processor.py:161} INFO - Started process (PID=1261) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:20.042+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:42:20.045+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:20.044+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:20.068+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:20.097+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:20.097+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:42:20.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:20.121+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:42:20.141+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.105 seconds
[2025-06-04T05:42:50.693+0000] {processor.py:161} INFO - Started process (PID=1270) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:50.695+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:42:50.697+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:50.697+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:50.717+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:42:50.745+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:50.745+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:42:50.766+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:42:50.766+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:42:50.787+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.099 seconds
[2025-06-04T05:43:20.960+0000] {processor.py:161} INFO - Started process (PID=1278) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:20.962+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:43:20.964+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:20.964+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:20.996+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:21.030+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:21.030+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:43:21.054+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:21.053+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:43:21.076+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T05:43:51.723+0000] {processor.py:161} INFO - Started process (PID=1287) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:51.725+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:43:51.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:51.727+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:51.751+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:43:51.785+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:51.785+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:43:51.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:43:51.810+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:43:51.835+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T05:44:22.210+0000] {processor.py:161} INFO - Started process (PID=1296) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:22.212+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:44:22.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:22.214+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:22.235+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:22.266+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:22.265+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:44:22.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:22.289+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:44:22.328+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.129 seconds
[2025-06-04T05:44:52.885+0000] {processor.py:161} INFO - Started process (PID=1305) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:52.886+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:44:52.889+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:52.889+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:52.916+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:44:52.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:52.968+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:44:53.038+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:44:53.038+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:44:53.080+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.200 seconds
[2025-06-04T05:45:23.775+0000] {processor.py:161} INFO - Started process (PID=1315) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:23.776+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:45:23.780+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:23.780+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:23.822+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:23.858+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:23.858+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:45:23.884+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:23.883+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:45:23.908+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.138 seconds
[2025-06-04T05:45:54.356+0000] {processor.py:161} INFO - Started process (PID=1324) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:54.358+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:45:54.361+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:54.360+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:54.385+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:45:54.429+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:54.429+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:45:54.462+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:45:54.462+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:45:54.498+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.147 seconds
[2025-06-04T05:46:25.017+0000] {processor.py:161} INFO - Started process (PID=1333) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:25.019+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:46:25.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:25.022+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:25.052+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:25.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:25.087+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:46:25.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:25.112+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:46:25.133+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T05:46:55.760+0000] {processor.py:161} INFO - Started process (PID=1336) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:55.761+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:46:55.764+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:55.764+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:55.801+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:46:55.836+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:55.836+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:46:55.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:46:55.865+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:46:55.887+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.132 seconds
[2025-06-04T05:47:26.548+0000] {processor.py:161} INFO - Started process (PID=1346) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:26.561+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:47:26.569+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:26.567+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:26.617+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:26.679+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:26.676+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:47:26.720+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:26.719+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:47:26.765+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.227 seconds
[2025-06-04T05:47:57.355+0000] {processor.py:161} INFO - Started process (PID=1355) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:57.357+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:47:57.359+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:57.359+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:57.418+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:47:57.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:57.502+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:47:57.531+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:47:57.531+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:47:57.726+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.377 seconds
[2025-06-04T05:48:28.199+0000] {processor.py:161} INFO - Started process (PID=1364) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:28.200+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:48:28.205+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:28.205+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:28.232+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:28.265+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:28.265+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:48:28.289+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:28.288+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:48:28.310+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T05:48:58.879+0000] {processor.py:161} INFO - Started process (PID=1373) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:58.881+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:48:58.883+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:58.883+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:58.906+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:48:58.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:58.937+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:48:58.960+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:48:58.960+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:48:58.984+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.109 seconds
[2025-06-04T05:49:29.571+0000] {processor.py:161} INFO - Started process (PID=1382) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:49:29.572+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:49:29.576+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:49:29.575+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:49:29.606+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:49:29.638+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:49:29.638+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:49:29.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:49:29.659+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:49:29.684+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T05:50:00.250+0000] {processor.py:161} INFO - Started process (PID=1391) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:00.251+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:50:00.254+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:00.254+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:00.281+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:00.311+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:00.311+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:50:00.335+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:00.334+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:50:00.355+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T05:50:30.856+0000] {processor.py:161} INFO - Started process (PID=1400) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:30.858+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:50:30.860+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:30.860+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:30.883+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:50:30.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:30.914+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:50:30.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:50:30.937+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:50:30.959+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.106 seconds
[2025-06-04T05:51:01.464+0000] {processor.py:161} INFO - Started process (PID=1409) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:01.465+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:51:01.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:01.468+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:01.492+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:01.523+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:01.523+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:51:01.548+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:01.547+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:51:01.570+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.111 seconds
[2025-06-04T05:51:32.138+0000] {processor.py:161} INFO - Started process (PID=1417) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:32.139+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:51:32.143+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:32.142+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:32.172+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:51:32.204+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:32.204+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:51:32.229+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:51:32.229+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:51:32.255+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T05:52:02.867+0000] {processor.py:161} INFO - Started process (PID=1426) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:02.869+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:52:02.871+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:02.871+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:02.900+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:02.950+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:02.950+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:52:02.988+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:02.987+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:52:03.022+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.159 seconds
[2025-06-04T05:52:33.581+0000] {processor.py:161} INFO - Started process (PID=1435) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:33.583+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:52:33.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:33.586+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:33.611+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:52:33.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:33.643+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:52:33.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:52:33.666+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:52:33.689+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.112 seconds
[2025-06-04T05:53:04.173+0000] {processor.py:161} INFO - Started process (PID=1445) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:04.176+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:53:04.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:04.178+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:04.210+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:04.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:04.241+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:53:04.266+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:04.266+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:53:04.291+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T05:53:35.834+0000] {processor.py:161} INFO - Started process (PID=1453) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:35.836+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:53:35.839+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:35.838+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:35.871+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:53:35.903+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:35.903+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:53:35.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:53:35.925+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:53:35.946+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T05:54:06.118+0000] {processor.py:161} INFO - Started process (PID=1462) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:06.120+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:54:06.122+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:06.122+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:06.152+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:06.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:06.194+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:54:06.222+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:06.222+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:54:06.247+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
[2025-06-04T05:54:36.896+0000] {processor.py:161} INFO - Started process (PID=1471) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:36.898+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:54:36.900+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:36.900+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:36.929+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:54:36.966+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:36.966+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:54:36.989+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:54:36.988+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:54:37.011+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T05:55:07.652+0000] {processor.py:161} INFO - Started process (PID=1480) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:07.653+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:55:07.656+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:07.655+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:07.692+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:07.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:07.736+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:55:07.767+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:07.767+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:55:07.789+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.141 seconds
[2025-06-04T05:55:38.355+0000] {processor.py:161} INFO - Started process (PID=1489) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:38.357+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:55:38.360+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:38.359+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:38.388+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:55:38.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:38.423+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:55:38.447+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:55:38.447+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:55:38.471+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T05:56:09.002+0000] {processor.py:161} INFO - Started process (PID=1498) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:09.004+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:56:09.008+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:09.007+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:09.033+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:09.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:09.071+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:56:09.094+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:09.094+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:56:09.114+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T05:56:39.456+0000] {processor.py:161} INFO - Started process (PID=1501) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:39.458+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:56:39.461+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:39.461+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:39.485+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:56:39.518+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:39.517+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:56:39.541+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:56:39.541+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:56:39.565+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.113 seconds
[2025-06-04T05:57:10.417+0000] {processor.py:161} INFO - Started process (PID=1510) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:10.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:57:10.421+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:10.420+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:10.443+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:10.474+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:10.474+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:57:10.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:10.496+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:57:10.516+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.103 seconds
[2025-06-04T05:57:41.088+0000] {processor.py:161} INFO - Started process (PID=1520) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:41.089+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:57:41.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:41.092+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:41.127+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:57:41.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:41.161+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:57:41.186+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:57:41.186+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:57:41.206+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.122 seconds
[2025-06-04T05:58:11.813+0000] {processor.py:161} INFO - Started process (PID=1529) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:11.815+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:58:11.819+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:11.818+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:11.931+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:11.981+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:11.981+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:58:12.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:12.022+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:58:12.049+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.242 seconds
[2025-06-04T05:58:42.655+0000] {processor.py:161} INFO - Started process (PID=1538) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:42.657+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:58:42.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:42.660+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:42.691+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:58:42.726+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:42.725+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:58:42.750+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:58:42.750+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:58:42.771+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T05:59:13.405+0000] {processor.py:161} INFO - Started process (PID=1547) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:13.406+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:59:13.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:13.409+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:13.442+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:13.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:13.474+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:59:13.499+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:13.499+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:59:13.523+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T05:59:44.095+0000] {processor.py:161} INFO - Started process (PID=1556) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:44.097+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T05:59:44.099+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:44.099+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:44.130+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T05:59:44.176+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:44.176+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:59:44.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:59:44.199+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T05:59:44.222+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T06:00:14.788+0000] {processor.py:161} INFO - Started process (PID=1565) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:14.790+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:00:14.793+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:14.793+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:14.827+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:14.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:14.878+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:00:14.917+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:14.916+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:00:14.957+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.173 seconds
[2025-06-04T06:00:45.497+0000] {processor.py:161} INFO - Started process (PID=1574) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:45.499+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:00:45.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:45.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:45.529+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:00:45.561+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:45.561+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:00:45.585+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:00:45.585+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:00:45.619+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.126 seconds
[2025-06-04T06:01:16.203+0000] {processor.py:161} INFO - Started process (PID=1583) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:16.205+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:01:16.208+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:16.207+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:16.238+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:16.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:16.270+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:01:16.294+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:16.294+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:01:16.316+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T06:01:46.909+0000] {processor.py:161} INFO - Started process (PID=1592) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:46.911+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:01:46.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:46.915+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:46.947+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:01:46.984+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:46.983+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:01:47.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:01:47.006+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:01:47.028+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T06:02:17.581+0000] {processor.py:161} INFO - Started process (PID=1601) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:17.583+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:02:17.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:17.586+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:17.610+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:17.646+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:17.645+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:02:17.670+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:17.670+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:02:17.697+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T06:02:48.237+0000] {processor.py:161} INFO - Started process (PID=1610) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:48.239+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:02:48.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:48.241+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:48.269+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:02:48.303+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:48.302+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:02:48.333+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:02:48.333+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:02:48.358+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T06:03:18.694+0000] {processor.py:161} INFO - Started process (PID=1619) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:18.697+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:03:18.703+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:18.701+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:18.856+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:18.900+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:18.900+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:03:18.936+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:18.935+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:03:18.960+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.275 seconds
[2025-06-04T06:03:49.730+0000] {processor.py:161} INFO - Started process (PID=1628) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:49.732+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:03:49.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:49.734+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:49.756+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:03:49.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:49.792+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:03:49.821+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:03:49.821+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:03:49.846+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T06:04:20.058+0000] {processor.py:161} INFO - Started process (PID=1637) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:20.060+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:04:20.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:20.062+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:20.088+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:20.143+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:20.142+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:04:21.545+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:21.545+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:04:21.576+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.153 seconds
[2025-06-04T06:04:54.082+0000] {processor.py:161} INFO - Started process (PID=1646) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:54.085+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:04:54.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:54.088+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:54.112+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:04:54.145+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:54.144+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:04:54.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:04:54.169+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:04:54.192+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.115 seconds
[2025-06-04T06:05:24.549+0000] {processor.py:161} INFO - Started process (PID=1655) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:24.551+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:05:24.555+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:24.554+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:24.584+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:24.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:24.616+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:05:24.640+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:24.639+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:05:24.663+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T06:05:55.284+0000] {processor.py:161} INFO - Started process (PID=1664) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:55.285+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:05:55.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:55.289+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:55.315+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:05:55.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:55.348+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:05:55.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:05:55.372+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:05:55.398+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.119 seconds
[2025-06-04T06:06:25.925+0000] {processor.py:161} INFO - Started process (PID=1673) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:25.927+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:06:25.929+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:25.929+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:25.955+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:25.990+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:25.990+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:06:26.015+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:26.015+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:06:26.039+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T06:06:56.609+0000] {processor.py:161} INFO - Started process (PID=1682) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:56.611+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:06:56.614+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:56.614+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:56.644+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:06:56.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:56.682+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:06:56.710+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:06:56.710+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:06:56.735+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T06:07:27.254+0000] {processor.py:161} INFO - Started process (PID=1691) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:27.256+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:07:27.258+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:27.258+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:27.366+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:27.418+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:27.418+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:07:27.452+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:27.452+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:07:27.476+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.229 seconds
[2025-06-04T06:07:58.126+0000] {processor.py:161} INFO - Started process (PID=1694) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:58.127+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:07:58.130+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:58.130+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:58.158+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:07:58.192+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:58.191+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:07:58.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:07:58.214+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:07:58.239+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.118 seconds
[2025-06-04T06:08:28.836+0000] {processor.py:161} INFO - Started process (PID=1703) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:28.838+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:08:28.840+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:28.840+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:28.861+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:28.892+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:28.892+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:08:28.919+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:28.917+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:08:28.941+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T06:08:59.520+0000] {processor.py:161} INFO - Started process (PID=1712) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:59.522+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:08:59.526+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:59.526+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:59.556+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:08:59.590+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:59.590+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:08:59.614+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:08:59.614+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:08:59.650+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.134 seconds
[2025-06-04T06:09:30.109+0000] {processor.py:161} INFO - Started process (PID=1721) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:09:30.110+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:09:30.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:09:30.113+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:09:30.141+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:09:30.175+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:09:30.175+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:09:30.200+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:09:30.200+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:09:30.222+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T06:10:00.776+0000] {processor.py:161} INFO - Started process (PID=1730) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:00.777+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:10:00.780+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:00.780+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:00.807+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:00.841+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:00.840+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:10:00.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:00.866+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:10:00.891+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T06:10:31.441+0000] {processor.py:161} INFO - Started process (PID=1739) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:31.442+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:10:31.445+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:31.445+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:31.469+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:10:31.500+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:31.500+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:10:31.525+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:10:31.524+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:10:31.547+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.110 seconds
[2025-06-04T06:11:02.147+0000] {processor.py:161} INFO - Started process (PID=1748) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:02.152+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:11:02.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:02.156+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:02.209+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:02.272+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:02.272+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:11:02.333+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:02.333+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:11:02.403+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.271 seconds
[2025-06-04T06:11:33.124+0000] {processor.py:161} INFO - Started process (PID=1758) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:33.126+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:11:33.128+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:33.128+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:33.182+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:11:33.293+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:33.293+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:11:33.354+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:11:33.354+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:11:33.386+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.266 seconds
[2025-06-04T06:12:04.055+0000] {processor.py:161} INFO - Started process (PID=1767) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:04.059+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:12:04.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:04.062+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:04.107+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:04.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:04.162+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:12:04.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:04.187+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:12:04.212+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.161 seconds
[2025-06-04T06:12:34.867+0000] {processor.py:161} INFO - Started process (PID=1777) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:34.869+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:12:34.873+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:34.873+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:34.901+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:12:34.945+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:34.944+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:12:34.972+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:12:34.972+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:12:34.993+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.130 seconds
[2025-06-04T06:13:05.446+0000] {processor.py:161} INFO - Started process (PID=1786) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:05.447+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:13:05.450+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:05.449+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:05.480+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:05.513+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:05.513+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:13:05.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:05.540+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:13:05.568+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.127 seconds
[2025-06-04T06:13:36.156+0000] {processor.py:161} INFO - Started process (PID=1795) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:36.158+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:13:36.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:36.161+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:36.191+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:13:36.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:36.222+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:13:36.247+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:13:36.246+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:13:36.272+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.120 seconds
[2025-06-04T06:14:06.846+0000] {processor.py:161} INFO - Started process (PID=1804) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:06.849+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:14:06.852+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:06.851+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:06.883+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:06.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:06.915+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:14:06.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:06.939+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:14:06.963+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.121 seconds
[2025-06-04T06:14:37.532+0000] {processor.py:161} INFO - Started process (PID=1813) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:37.535+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:14:37.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:37.539+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:37.570+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:14:37.605+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:37.605+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:14:37.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:14:37.631+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:14:37.654+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T06:15:08.245+0000] {processor.py:161} INFO - Started process (PID=1822) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:08.246+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:15:08.249+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:08.249+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:08.273+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:08.312+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:08.312+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:15:08.337+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:08.337+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:15:08.363+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.123 seconds
[2025-06-04T06:15:39.903+0000] {processor.py:161} INFO - Started process (PID=1831) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:39.905+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:15:39.908+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:39.908+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:39.938+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:15:39.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:39.973+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:15:39.999+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:15:39.999+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:15:40.023+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T06:16:10.417+0000] {processor.py:161} INFO - Started process (PID=1840) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:10.422+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:16:10.428+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:10.428+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:10.487+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:10.563+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:10.563+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:16:12.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:12.023+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:16:12.083+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.308 seconds
[2025-06-04T06:16:44.924+0000] {processor.py:161} INFO - Started process (PID=1849) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:44.925+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:16:44.928+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:44.927+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:44.955+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:16:44.989+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:44.989+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:16:45.013+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:16:45.013+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:16:45.035+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.116 seconds
[2025-06-04T06:17:15.269+0000] {processor.py:161} INFO - Started process (PID=1858) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:15.271+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:17:15.274+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:15.273+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:15.301+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:15.333+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:15.333+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:17:15.362+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:15.361+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:17:15.390+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.125 seconds
[2025-06-04T06:17:45.828+0000] {processor.py:161} INFO - Started process (PID=1867) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:45.829+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:17:45.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:45.832+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:45.863+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:17:45.927+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:45.927+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:17:45.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:17:45.963+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:17:45.997+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.173 seconds
[2025-06-04T06:18:16.851+0000] {processor.py:161} INFO - Started process (PID=1875) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:16.853+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:18:16.857+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:16.856+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:16.883+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:16.916+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:16.916+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:18:16.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:16.939+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:18:16.965+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.117 seconds
[2025-06-04T06:18:47.594+0000] {processor.py:161} INFO - Started process (PID=1883) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:47.596+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:18:47.599+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:47.598+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:47.635+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:18:47.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:47.675+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:18:47.710+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:18:47.709+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:18:47.758+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.168 seconds
[2025-06-04T06:19:18.452+0000] {processor.py:161} INFO - Started process (PID=1892) to work on /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:19:18.453+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/simple_etl_dag.py for tasks to queue
[2025-06-04T06:19:18.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:19:18.456+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:19:18.480+0000] {processor.py:840} INFO - DAG(s) 'simple_etl_pipeline' retrieved from /opt/airflow/dags/simple_etl_dag.py
[2025-06-04T06:19:18.525+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:19:18.525+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T06:19:18.551+0000] {logging_mixin.py:188} INFO - [2025-06-04T06:19:18.550+0000] {dag.py:3823} INFO - Setting next_dagrun for simple_etl_pipeline to 2025-06-04 00:00:00+00:00, run_after=2025-06-05 00:00:00+00:00
[2025-06-04T06:19:18.581+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/simple_etl_dag.py took 0.133 seconds
