{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/catalog/v1.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T02:52:28.515935Z", "invocation_id": "3a5ca569-63a5-45f4-bcf7-436afe4ec72d", "env": {}}, "nodes": {"test.live_c360.source_not_null_live_external_ext_live_orders_id.43cabbf964": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_ORDERS_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_id.43cabbf964"}, "test.live_c360.source_not_null_live_external_ext_live_orders_generated_at.a66c929306": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_ORDERS_GENERATED_AT", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_generated_at.a66c929306"}, "model.live_c360.query_history_health": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "QUERY_HISTORY_HEALTH", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"EXECUTION_DATE": {"type": "DATE", "index": 1, "name": "EXECUTION_DATE", "comment": null}, "QUERY_CATEGORY": {"type": "TEXT", "index": 2, "name": "QUERY_CATEGORY", "comment": null}, "TIME_SEGMENT": {"type": "TEXT", "index": 3, "name": "TIME_SEGMENT", "comment": null}, "WAREHOUSE_NAME": {"type": "TEXT", "index": 4, "name": "WAREHOUSE_NAME", "comment": null}, "TOTAL_QUERIES": {"type": "NUMBER", "index": 5, "name": "TOTAL_QUERIES", "comment": null}, "FAILED_QUERIES": {"type": "NUMBER", "index": 6, "name": "FAILED_QUERIES", "comment": null}, "LONG_RUNNING_QUERIES": {"type": "NUMBER", "index": 7, "name": "LONG_RUNNING_QUERIES", "comment": null}, "QUERIES_WITH_SPILL": {"type": "NUMBER", "index": 8, "name": "QUERIES_WITH_SPILL", "comment": null}, "LOW_CACHE_QUERIES": {"type": "NUMBER", "index": 9, "name": "LOW_CACHE_QUERIES", "comment": null}, "AVG_ELAPSED_TIME_MS": {"type": "NUMBER", "index": 10, "name": "AVG_ELAPSED_TIME_MS", "comment": null}, "MAX_ELAPSED_TIME_MS": {"type": "NUMBER", "index": 11, "name": "MAX_ELAPSED_TIME_MS", "comment": null}, "P95_ELAPSED_TIME_MS": {"type": "NUMBER", "index": 12, "name": "P95_ELAPSED_TIME_MS", "comment": null}, "TOTAL_BYTES_SCANNED": {"type": "NUMBER", "index": 13, "name": "TOTAL_BYTES_SCANNED", "comment": null}, "TOTAL_ROWS_PRODUCED": {"type": "NUMBER", "index": 14, "name": "TOTAL_ROWS_PRODUCED", "comment": null}, "AVG_CACHE_HIT_RATE": {"type": "FLOAT", "index": 15, "name": "AVG_CACHE_HIT_RATE", "comment": null}, "TOTAL_CREDITS_USED": {"type": "FLOAT", "index": 16, "name": "TOTAL_CREDITS_USED", "comment": null}, "LATEST_EXECUTION_TIME": {"type": "TIMESTAMP_LTZ", "index": 17, "name": "LATEST_EXECUTION_TIME", "comment": null}, "RELIABILITY_SCORE": {"type": "NUMBER", "index": 18, "name": "RELIABILITY_SCORE", "comment": null}, "PERFORMANCE_SCORE": {"type": "NUMBER", "index": 19, "name": "PERFORMANCE_SCORE", "comment": null}, "EFFICIENCY_SCORE": {"type": "NUMBER", "index": 20, "name": "EFFICIENCY_SCORE", "comment": null}, "OVERALL_HEALTH_SCORE": {"type": "NUMBER", "index": 21, "name": "OVERALL_HEALTH_SCORE", "comment": null}, "HEALTH_CHECK_TIMESTAMP": {"type": "TIMESTAMP_LTZ", "index": 22, "name": "HEALTH_CHECK_TIMESTAMP", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 21, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 54272, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 21:47UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.query_history_health"}, "model.live_c360.stg_users": {"metadata": {"type": "VIEW", "schema": "LIVE_DATA", "name": "STG_USERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"USER_ID": {"type": "TEXT", "index": 1, "name": "USER_ID", "comment": null}, "EMAIL_HASH": {"type": "TEXT", "index": 2, "name": "EMAIL_HASH", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 5, "name": "ADDRESS", "comment": null}, "ACQUISITION_CHANNEL": {"type": "TEXT", "index": 6, "name": "ACQUISITION_CHANNEL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 7, "name": "COUNTRY", "comment": null}, "CREATION_DATE": {"type": "TIMESTAMP_NTZ", "index": 8, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TIMESTAMP_NTZ", "index": 9, "name": "LAST_ACTIVITY_DATE", "comment": null}, "GENDER": {"type": "TEXT", "index": 10, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 11, "name": "AGE_GROUP", "comment": null}, "IS_CHURNED": {"type": "BOOLEAN", "index": 12, "name": "IS_CHURNED", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 13, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TIMESTAMP_NTZ", "index": 14, "name": "GENERATED_AT", "comment": null}, "PROCESSED_AT": {"type": "TIMESTAMP_LTZ", "index": 15, "name": "PROCESSED_AT", "comment": null}, "DAYS_SINCE_CREATION": {"type": "NUMBER", "index": 16, "name": "DAYS_SINCE_CREATION", "comment": null}, "DAYS_SINCE_LAST_ACTIVITY": {"type": "NUMBER", "index": 17, "name": "DAYS_SINCE_LAST_ACTIVITY", "comment": null}, "HAS_FUTURE_CREATION_DATE": {"type": "BOOLEAN", "index": 18, "name": "HAS_FUTURE_CREATION_DATE", "comment": null}, "HAS_INVALID_ACTIVITY_DATE": {"type": "BOOLEAN", "index": 19, "name": "HAS_INVALID_ACTIVITY_DATE", "comment": null}, "ACTIVITY_SEGMENT": {"type": "TEXT", "index": 20, "name": "ACTIVITY_SEGMENT", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.stg_users"}, "test.live_c360.source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_.ba15555d86": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_RELATIONSHIPS_LIVE_EXTE_F8DCAB25F08D3D0CECC66ACC19C0F320", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"FROM_FIELD": {"type": "TEXT", "index": 1, "name": "FROM_FIELD", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 396000, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": ********, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_.ba15555d86"}, "test.live_c360.source_unique_live_external_ext_live_events_event_id.eeb702f8fc": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_UNIQUE_LIVE_EXTERNAL_EXT_LIVE_EVENTS_EVENT_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"UNIQUE_FIELD": {"type": "TEXT", "index": 1, "name": "UNIQUE_FIELD", "comment": null}, "N_RECORDS": {"type": "NUMBER", "index": 2, "name": "N_RECORDS", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_unique_live_external_ext_live_events_event_id.eeb702f8fc"}, "test.live_c360.source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_.8d45efa758": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_RELATIONSHIPS_LIVE_EXTE_3905F58A8F4F802F3D4269E076A3F201", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"FROM_FIELD": {"type": "TEXT", "index": 1, "name": "FROM_FIELD", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 72000, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 2408448, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_.8d45efa758"}, "test.live_c360.source_not_null_live_external_ext_live_users_generated_at.60675c4e9c": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_USERS_GENERATED_AT", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "EMAIL": {"type": "TEXT", "index": 5, "name": "EMAIL", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 6, "name": "ADDRESS", "comment": null}, "CANAL": {"type": "TEXT", "index": 7, "name": "CANAL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 8, "name": "COUNTRY", "comment": null}, "CREATION_DATE": {"type": "TEXT", "index": 9, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TEXT", "index": 10, "name": "LAST_ACTIVITY_DATE", "comment": null}, "GENDER": {"type": "NUMBER", "index": 11, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 12, "name": "AGE_GROUP", "comment": null}, "CHURN": {"type": "BOOLEAN", "index": 13, "name": "CHURN", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 14, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 15, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_generated_at.60675c4e9c"}, "model.live_c360.stg_orders": {"metadata": {"type": "VIEW", "schema": "LIVE_DATA", "name": "STG_ORDERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"ORDER_ID": {"type": "TEXT", "index": 1, "name": "ORDER_ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TIMESTAMP_NTZ", "index": 3, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 4, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 5, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 6, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TIMESTAMP_NTZ", "index": 7, "name": "GENERATED_AT", "comment": null}, "PROCESSED_AT": {"type": "TIMESTAMP_LTZ", "index": 8, "name": "PROCESSED_AT", "comment": null}, "AVG_ITEM_PRICE": {"type": "NUMBER", "index": 9, "name": "AVG_ITEM_PRICE", "comment": null}, "TRANSACTION_DATE_ONLY": {"type": "DATE", "index": 10, "name": "TRANSACTION_DATE_ONLY", "comment": null}, "TRANSACTION_YEAR": {"type": "NUMBER", "index": 11, "name": "TRANSACTION_YEAR", "comment": null}, "TRANSACTION_MONTH": {"type": "NUMBER", "index": 12, "name": "TRANSACTION_MONTH", "comment": null}, "TRANSACTION_DAY": {"type": "NUMBER", "index": 13, "name": "TRANSACTION_DAY", "comment": null}, "TRANSACTION_HOUR": {"type": "NUMBER", "index": 14, "name": "TRANSACTION_HOUR", "comment": null}, "TRANSACTION_DAY_NAME": {"type": "TEXT", "index": 15, "name": "TRANSACTION_DAY_NAME", "comment": null}, "TIME_OF_DAY_SEGMENT": {"type": "TEXT", "index": 16, "name": "TIME_OF_DAY_SEGMENT", "comment": null}, "ORDER_SIZE_SEGMENT": {"type": "TEXT", "index": 17, "name": "ORDER_SIZE_SEGMENT", "comment": null}, "QUANTITY_SEGMENT": {"type": "TEXT", "index": 18, "name": "QUANTITY_SEGMENT", "comment": null}, "HAS_FUTURE_TRANSACTION_DATE": {"type": "BOOLEAN", "index": 19, "name": "HAS_FUTURE_TRANSACTION_DATE", "comment": null}, "HAS_HIGH_ITEM_PRICE": {"type": "BOOLEAN", "index": 20, "name": "HAS_HIGH_ITEM_PRICE", "comment": null}, "DAYS_SINCE_TRANSACTION": {"type": "NUMBER", "index": 21, "name": "DAYS_SINCE_TRANSACTION", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.stg_orders"}, "test.live_c360.source_not_null_live_external_ext_live_orders_amount.679c5c05e3": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_ORDERS_AMOUNT", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_amount.679c5c05e3"}, "test.live_c360.source_not_null_live_external_ext_live_orders_item_count.7dac40310e": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_ORDERS_ITEM_COUNT", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_item_count.7dac40310e"}, "model.live_c360.pipeline_runtime_health": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "PIPELINE_RUNTIME_HEALTH", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"EXECUTION_DATE": {"type": "DATE", "index": 1, "name": "EXECUTION_DATE", "comment": null}, "EXECUTION_HOUR": {"type": "NUMBER", "index": 2, "name": "EXECUTION_HOUR", "comment": null}, "MODEL_TYPE": {"type": "TEXT", "index": 3, "name": "MODEL_TYPE", "comment": null}, "WAREHOUSE_NAME": {"type": "TEXT", "index": 4, "name": "WAREHOUSE_NAME", "comment": null}, "MODELS_EXECUTED": {"type": "NUMBER", "index": 5, "name": "MODELS_EXECUTED", "comment": null}, "SUCCESSFUL_MODELS": {"type": "NUMBER", "index": 6, "name": "SUCCESSFUL_MODELS", "comment": null}, "FAILED_MODELS": {"type": "NUMBER", "index": 7, "name": "FAILED_MODELS", "comment": null}, "PIPELINE_SUCCESS_RATE": {"type": "FLOAT", "index": 8, "name": "PIPELINE_SUCCESS_RATE", "comment": null}, "TOTAL_PIPELINE_TIME_MS": {"type": "NUMBER", "index": 9, "name": "TOTAL_PIPELINE_TIME_MS", "comment": null}, "AVG_MODEL_TIME_MS": {"type": "NUMBER", "index": 10, "name": "AVG_MODEL_TIME_MS", "comment": null}, "MAX_MODEL_TIME_MS": {"type": "NUMBER", "index": 11, "name": "MAX_MODEL_TIME_MS", "comment": null}, "PIPELINE_DURATION_MS": {"type": "NUMBER", "index": 12, "name": "PIPELINE_DURATION_MS", "comment": null}, "PIPELINE_START_TIME": {"type": "TIMESTAMP_LTZ", "index": 13, "name": "PIPELINE_START_TIME", "comment": null}, "PIPELINE_END_TIME": {"type": "TIMESTAMP_LTZ", "index": 14, "name": "PIPELINE_END_TIME", "comment": null}, "PERFORMANCE_SCORE": {"type": "NUMBER", "index": 15, "name": "PERFORMANCE_SCORE", "comment": null}, "RELIABILITY_SCORE": {"type": "NUMBER", "index": 16, "name": "RELIABILITY_SCORE", "comment": null}, "DURATION_SCORE": {"type": "NUMBER", "index": 17, "name": "DURATION_SCORE", "comment": null}, "OVERALL_PIPELINE_HEALTH_SCORE": {"type": "NUMBER", "index": 18, "name": "OVERALL_PIPELINE_HEALTH_SCORE", "comment": null}, "PIPELINE_STATUS": {"type": "TEXT", "index": 19, "name": "PIPELINE_STATUS", "comment": null}, "ERROR_MESSAGES": {"type": "TEXT", "index": 20, "name": "ERROR_MESSAGES", "comment": null}, "HEALTH_CHECK_TIMESTAMP": {"type": "TIMESTAMP_LTZ", "index": 21, "name": "HEALTH_CHECK_TIMESTAMP", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 14, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 52736, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 21:47UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.pipeline_runtime_health"}, "test.live_c360.source_unique_live_external_ext_live_users_id.4af1be4e1f": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_UNIQUE_LIVE_EXTERNAL_EXT_LIVE_USERS_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"UNIQUE_FIELD": {"type": "TEXT", "index": 1, "name": "UNIQUE_FIELD", "comment": null}, "N_RECORDS": {"type": "NUMBER", "index": 2, "name": "N_RECORDS", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:51UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_unique_live_external_ext_live_users_id.4af1be4e1f"}, "test.live_c360.source_not_null_live_external_ext_live_events_generated_at.96d76fb529": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_EVENTS_GENERATED_AT", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "EVENT_ID": {"type": "TEXT", "index": 3, "name": "EVENT_ID", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 4, "name": "PLATFORM", "comment": null}, "DATE": {"type": "TEXT", "index": 5, "name": "DATE", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 7, "name": "SESSION_ID", "comment": null}, "URL": {"type": "TEXT", "index": 8, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 9, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 10, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_generated_at.96d76fb529"}, "model.live_c360.etl_health_dashboard": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "ETL_HEALTH_DASHBOARD", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"HEALTH_CATEGORY": {"type": "TEXT", "index": 1, "name": "HEALTH_CATEGORY", "comment": null}, "AVG_HEALTH_SCORE": {"type": "FLOAT", "index": 2, "name": "AVG_HEALTH_SCORE", "comment": null}, "MIN_HEALTH_SCORE": {"type": "FLOAT", "index": 3, "name": "MIN_HEALTH_SCORE", "comment": null}, "MAX_HEALTH_SCORE": {"type": "FLOAT", "index": 4, "name": "MAX_HEALTH_SCORE", "comment": null}, "LATEST_CHECK_TIME": {"type": "TIMESTAMP_LTZ", "index": 5, "name": "LATEST_CHECK_TIME", "comment": null}, "TOTAL_ITEMS": {"type": "NUMBER", "index": 6, "name": "TOTAL_ITEMS", "comment": null}, "TOTAL_ISSUES": {"type": "NUMBER", "index": 7, "name": "TOTAL_ISSUES", "comment": null}, "HEALTH_STATUS": {"type": "TEXT", "index": 8, "name": "HEALTH_STATUS", "comment": null}, "TREND_INDICATOR": {"type": "TEXT", "index": 9, "name": "TREND_INDICATOR", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 2, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 6144, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 21:47UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.etl_health_dashboard"}, "test.live_c360.source_not_null_live_external_ext_live_users_email.d823bafd9b": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_USERS_EMAIL", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "EMAIL": {"type": "TEXT", "index": 5, "name": "EMAIL", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 6, "name": "ADDRESS", "comment": null}, "CANAL": {"type": "TEXT", "index": 7, "name": "CANAL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 8, "name": "COUNTRY", "comment": null}, "CREATION_DATE": {"type": "TEXT", "index": 9, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TEXT", "index": 10, "name": "LAST_ACTIVITY_DATE", "comment": null}, "GENDER": {"type": "NUMBER", "index": 11, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 12, "name": "AGE_GROUP", "comment": null}, "CHURN": {"type": "BOOLEAN", "index": 13, "name": "CHURN", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 14, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 15, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_email.d823bafd9b"}, "test.live_c360.source_unique_live_external_ext_live_orders_id.82585e5b4d": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_UNIQUE_LIVE_EXTERNAL_EXT_LIVE_ORDERS_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"UNIQUE_FIELD": {"type": "TEXT", "index": 1, "name": "UNIQUE_FIELD", "comment": null}, "N_RECORDS": {"type": "NUMBER", "index": 2, "name": "N_RECORDS", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_unique_live_external_ext_live_orders_id.82585e5b4d"}, "test.live_c360.source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None.553832ca75": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_ACCEPTED_VALUES_LIVE_EX_255D50E75759BEBAE188BA25E47211E0", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE_FIELD": {"type": "TEXT", "index": 1, "name": "VALUE_FIELD", "comment": null}, "N_RECORDS": {"type": "NUMBER", "index": 2, "name": "N_RECORDS", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None.553832ca75"}, "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0.50380c3cc8": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "DBT_UTILS_SOURCE_ACCEPTED_RANG_038429B5CA81DF79A58A497617B75C52", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0.50380c3cc8"}, "model.live_c360.data_quality_health": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "DATA_QUALITY_HEALTH", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"TABLE_NAME": {"type": "TEXT", "index": 1, "name": "TABLE_NAME", "comment": null}, "LAYER": {"type": "TEXT", "index": 2, "name": "LAYER", "comment": null}, "TOTAL_ROWS": {"type": "NUMBER", "index": 3, "name": "TOTAL_ROWS", "comment": null}, "UNIQUE_KEYS": {"type": "NUMBER", "index": 4, "name": "UNIQUE_KEYS", "comment": null}, "NULL_PRIMARY_KEYS": {"type": "NUMBER", "index": 5, "name": "NULL_PRIMARY_KEYS", "comment": null}, "NULL_CRITICAL_FIELDS": {"type": "NUMBER", "index": 6, "name": "NULL_CRITICAL_FIELDS", "comment": null}, "DATA_QUALITY_ISSUES": {"type": "NUMBER", "index": 7, "name": "DATA_QUALITY_ISSUES", "comment": null}, "LATEST_DATA_TIMESTAMP": {"type": "TIMESTAMP_NTZ", "index": 8, "name": "LATEST_DATA_TIMESTAMP", "comment": null}, "UNIQUENESS_SCORE": {"type": "FLOAT", "index": 9, "name": "UNIQUENESS_SCORE", "comment": null}, "COMPLETENESS_SCORE": {"type": "FLOAT", "index": 10, "name": "COMPLETENESS_SCORE", "comment": null}, "VALIDITY_SCORE": {"type": "FLOAT", "index": 11, "name": "VALIDITY_SCORE", "comment": null}, "FRESHNESS_SCORE": {"type": "NUMBER", "index": 12, "name": "FRESHNESS_SCORE", "comment": null}, "OVERALL_QUALITY_SCORE": {"type": "FLOAT", "index": 13, "name": "OVERALL_QUALITY_SCORE", "comment": null}, "VOLUME_CHANGE_PCT": {"type": "FLOAT", "index": 14, "name": "VOLUME_CHANGE_PCT", "comment": null}, "QUALITY_STATUS": {"type": "TEXT", "index": 15, "name": "QUALITY_STATUS", "comment": null}, "VOLUME_STATUS": {"type": "TEXT", "index": 16, "name": "VOLUME_STATUS", "comment": null}, "FRESHNESS_STATUS": {"type": "TEXT", "index": 17, "name": "FRESHNESS_STATUS", "comment": null}, "CHECK_TIMESTAMP": {"type": "TIMESTAMP_LTZ", "index": 18, "name": "CHECK_TIMESTAMP", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 5, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 16384, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 21:47UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.data_quality_health"}, "model.live_c360.dbt_test_health": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "DBT_TEST_HEALTH", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"TEST_DATE": {"type": "DATE", "index": 1, "name": "TEST_DATE", "comment": null}, "TEST_TYPE": {"type": "TEXT", "index": 2, "name": "TEST_TYPE", "comment": null}, "MODEL_NAME": {"type": "TEXT", "index": 3, "name": "MODEL_NAME", "comment": null}, "TEST_CATEGORY": {"type": "TEXT", "index": 4, "name": "TEST_CATEGORY", "comment": null}, "TOTAL_TESTS": {"type": "NUMBER", "index": 5, "name": "TOTAL_TESTS", "comment": null}, "PASSED_TESTS": {"type": "NUMBER", "index": 6, "name": "PASSED_TESTS", "comment": null}, "FAILED_TESTS": {"type": "NUMBER", "index": 7, "name": "FAILED_TESTS", "comment": null}, "WARNING_TESTS": {"type": "NUMBER", "index": 8, "name": "WARNING_TESTS", "comment": null}, "SKIPPED_TESTS": {"type": "NUMBER", "index": 9, "name": "SKIPPED_TESTS", "comment": null}, "TOTAL_FAILURES": {"type": "NUMBER", "index": 10, "name": "TOTAL_FAILURES", "comment": null}, "PASS_RATE": {"type": "FLOAT", "index": 11, "name": "PASS_RATE", "comment": null}, "TEST_TYPES_COVERED": {"type": "NUMBER", "index": 12, "name": "TEST_TYPES_COVERED", "comment": null}, "TOTAL_TESTS_ON_MODEL": {"type": "NUMBER", "index": 13, "name": "TOTAL_TESTS_ON_MODEL", "comment": null}, "HAS_NOT_NULL_TESTS": {"type": "NUMBER", "index": 14, "name": "HAS_NOT_NULL_TESTS", "comment": null}, "HAS_UNIQUE_TESTS": {"type": "NUMBER", "index": 15, "name": "HAS_UNIQUE_TESTS", "comment": null}, "HAS_RELATIONSHIP_TESTS": {"type": "NUMBER", "index": 16, "name": "HAS_RELATIONSHIP_TESTS", "comment": null}, "HAS_ACCEPTED_VALUES_TESTS": {"type": "NUMBER", "index": 17, "name": "HAS_ACCEPTED_VALUES_TESTS", "comment": null}, "RELIABILITY_SCORE": {"type": "NUMBER", "index": 18, "name": "RELIABILITY_SCORE", "comment": null}, "COVERAGE_SCORE": {"type": "NUMBER", "index": 19, "name": "COVERAGE_SCORE", "comment": null}, "OVERALL_TEST_HEALTH_SCORE": {"type": "NUMBER", "index": 20, "name": "OVERALL_TEST_HEALTH_SCORE", "comment": null}, "TEST_HEALTH_STATUS": {"type": "TEXT", "index": 21, "name": "TEST_HEALTH_STATUS", "comment": null}, "LATEST_EXECUTION": {"type": "TIMESTAMP_LTZ", "index": 22, "name": "LATEST_EXECUTION", "comment": null}, "HEALTH_CHECK_TIMESTAMP": {"type": "TIMESTAMP_LTZ", "index": 23, "name": "HEALTH_CHECK_TIMESTAMP", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 5, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 7168, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 21:47UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.dbt_test_health"}, "test.live_c360.source_not_null_live_external_ext_live_events_event_id.ff0aeb9e92": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_EVENTS_EVENT_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "EVENT_ID": {"type": "TEXT", "index": 3, "name": "EVENT_ID", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 4, "name": "PLATFORM", "comment": null}, "DATE": {"type": "TEXT", "index": 5, "name": "DATE", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 7, "name": "SESSION_ID", "comment": null}, "URL": {"type": "TEXT", "index": 8, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 9, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 10, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_event_id.ff0aeb9e92"}, "test.live_c360.source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase.6a0240ce21": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_ACCEPTED_VALUES_LIVE_EX_E2A83A0F133A781A89C6730F46BEEFED", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE_FIELD": {"type": "TEXT", "index": 1, "name": "VALUE_FIELD", "comment": null}, "N_RECORDS": {"type": "NUMBER", "index": 2, "name": "N_RECORDS", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase.6a0240ce21"}, "model.live_c360.fact_orders": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "FACT_ORDERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"ORDER_ID": {"type": "TEXT", "index": 1, "name": "ORDER_ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TIMESTAMP_NTZ", "index": 3, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 4, "name": "ITEM_COUNT", "comment": null}, "ORDER_AMOUNT": {"type": "NUMBER", "index": 5, "name": "ORDER_AMOUNT", "comment": null}, "USER_ORDER_SEQUENCE": {"type": "NUMBER", "index": 6, "name": "USER_ORDER_SEQUENCE", "comment": null}, "DAYS_SINCE_PREVIOUS_ORDER": {"type": "NUMBER", "index": 7, "name": "DAYS_SINCE_PREVIOUS_ORDER", "comment": null}, "RUNNING_TOTAL_AMOUNT": {"type": "NUMBER", "index": 8, "name": "RUNNING_TOTAL_AMOUNT", "comment": null}, "RUNNING_ORDER_COUNT": {"type": "NUMBER", "index": 9, "name": "RUNNING_ORDER_COUNT", "comment": null}, "CUSTOMER_TYPE": {"type": "TEXT", "index": 10, "name": "CUSTOMER_TYPE", "comment": null}, "IS_HIGH_VALUE_ORDER": {"type": "BOOLEAN", "index": 11, "name": "IS_HIGH_VALUE_ORDER", "comment": null}, "IS_LARGE_ORDER": {"type": "BOOLEAN", "index": 12, "name": "IS_LARGE_ORDER", "comment": null}, "TIME_OF_DAY": {"type": "TEXT", "index": 13, "name": "TIME_OF_DAY", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 90000, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 7083520, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:49UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.fact_orders"}, "model.live_c360.stg_events": {"metadata": {"type": "VIEW", "schema": "LIVE_DATA", "name": "STG_EVENTS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"EVENT_ID": {"type": "TEXT", "index": 1, "name": "EVENT_ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 3, "name": "SESSION_ID", "comment": null}, "EVENT_TIMESTAMP": {"type": "TIMESTAMP_NTZ", "index": 4, "name": "EVENT_TIMESTAMP", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 5, "name": "PLATFORM", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "URL": {"type": "TEXT", "index": 7, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 8, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TIMESTAMP_NTZ", "index": 9, "name": "GENERATED_AT", "comment": null}, "PROCESSED_AT": {"type": "TIMESTAMP_LTZ", "index": 10, "name": "PROCESSED_AT", "comment": null}, "PAGE_TYPE": {"type": "TEXT", "index": 11, "name": "PAGE_TYPE", "comment": null}, "URL_PATH": {"type": "TEXT", "index": 12, "name": "URL_PATH", "comment": null}, "EVENT_DATE": {"type": "DATE", "index": 13, "name": "EVENT_DATE", "comment": null}, "EVENT_YEAR": {"type": "NUMBER", "index": 14, "name": "EVENT_YEAR", "comment": null}, "EVENT_MONTH": {"type": "NUMBER", "index": 15, "name": "EVENT_MONTH", "comment": null}, "EVENT_DAY": {"type": "NUMBER", "index": 16, "name": "EVENT_DAY", "comment": null}, "EVENT_HOUR": {"type": "NUMBER", "index": 17, "name": "EVENT_HOUR", "comment": null}, "EVENT_DAY_NAME": {"type": "TEXT", "index": 18, "name": "EVENT_DAY_NAME", "comment": null}, "TIME_OF_DAY_SEGMENT": {"type": "TEXT", "index": 19, "name": "TIME_OF_DAY_SEGMENT", "comment": null}, "DEVICE_CATEGORY": {"type": "TEXT", "index": 20, "name": "DEVICE_CATEGORY", "comment": null}, "ACTION_CATEGORY": {"type": "TEXT", "index": 21, "name": "ACTION_CATEGORY", "comment": null}, "FUNNEL_STAGE": {"type": "TEXT", "index": 22, "name": "FUNNEL_STAGE", "comment": null}, "HAS_FUTURE_EVENT_TIMESTAMP": {"type": "BOOLEAN", "index": 23, "name": "HAS_FUTURE_EVENT_TIMESTAMP", "comment": null}, "MINUTES_SINCE_EVENT": {"type": "NUMBER", "index": 24, "name": "MINUTES_SINCE_EVENT", "comment": null}, "HOURS_SINCE_EVENT": {"type": "NUMBER", "index": 25, "name": "HOURS_SINCE_EVENT", "comment": null}, "DAYS_SINCE_EVENT": {"type": "NUMBER", "index": 26, "name": "DAYS_SINCE_EVENT", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.stg_events"}, "test.live_c360.source_not_null_live_external_ext_live_events_user_id.f112ba0802": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_EVENTS_USER_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "EVENT_ID": {"type": "TEXT", "index": 3, "name": "EVENT_ID", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 4, "name": "PLATFORM", "comment": null}, "DATE": {"type": "TEXT", "index": 5, "name": "DATE", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 7, "name": "SESSION_ID", "comment": null}, "URL": {"type": "TEXT", "index": 8, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 9, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 10, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_user_id.f112ba0802"}, "model.live_c360.dim_users": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "DIM_USERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"USER_ID": {"type": "TEXT", "index": 1, "name": "USER_ID", "comment": null}, "EMAIL_HASH": {"type": "TEXT", "index": 2, "name": "EMAIL_HASH", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 5, "name": "ADDRESS", "comment": null}, "ACQUISITION_CHANNEL": {"type": "TEXT", "index": 6, "name": "ACQUISITION_CHANNEL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 7, "name": "COUNTRY", "comment": null}, "GENDER": {"type": "TEXT", "index": 8, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 9, "name": "AGE_GROUP", "comment": null}, "CREATION_DATE": {"type": "TIMESTAMP_NTZ", "index": 10, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TIMESTAMP_NTZ", "index": 11, "name": "LAST_ACTIVITY_DATE", "comment": null}, "IS_CHURNED": {"type": "BOOLEAN", "index": 12, "name": "IS_CHURNED", "comment": null}, "ACTIVITY_SEGMENT": {"type": "TEXT", "index": 13, "name": "ACTIVITY_SEGMENT", "comment": null}, "DAYS_SINCE_CREATION": {"type": "NUMBER", "index": 14, "name": "DAYS_SINCE_CREATION", "comment": null}, "DAYS_SINCE_LAST_ACTIVITY": {"type": "NUMBER", "index": 15, "name": "DAYS_SINCE_LAST_ACTIVITY", "comment": null}, "TOTAL_ORDERS": {"type": "NUMBER", "index": 16, "name": "TOTAL_ORDERS", "comment": null}, "TOTAL_SPENT": {"type": "NUMBER", "index": 17, "name": "TOTAL_SPENT", "comment": null}, "AVG_ORDER_VALUE": {"type": "NUMBER", "index": 18, "name": "AVG_ORDER_VALUE", "comment": null}, "FIRST_ORDER_DATE": {"type": "TIMESTAMP_NTZ", "index": 19, "name": "FIRST_ORDER_DATE", "comment": null}, "LAST_ORDER_DATE": {"type": "TIMESTAMP_NTZ", "index": 20, "name": "LAST_ORDER_DATE", "comment": null}, "TOTAL_ITEMS_PURCHASED": {"type": "NUMBER", "index": 21, "name": "TOTAL_ITEMS_PURCHASED", "comment": null}, "TOTAL_EVENTS": {"type": "NUMBER", "index": 22, "name": "TOTAL_EVENTS", "comment": null}, "TOTAL_SESSIONS": {"type": "NUMBER", "index": 23, "name": "TOTAL_SESSIONS", "comment": null}, "ACTIVE_DAYS": {"type": "NUMBER", "index": 24, "name": "ACTIVE_DAYS", "comment": null}, "FIRST_EVENT_DATE": {"type": "TIMESTAMP_NTZ", "index": 25, "name": "FIRST_EVENT_DATE", "comment": null}, "LAST_EVENT_DATE": {"type": "TIMESTAMP_NTZ", "index": 26, "name": "LAST_EVENT_DATE", "comment": null}, "VIEW_EVENTS": {"type": "NUMBER", "index": 27, "name": "VIEW_EVENTS", "comment": null}, "CLICK_EVENTS": {"type": "NUMBER", "index": 28, "name": "CLICK_EVENTS", "comment": null}, "LOGIN_EVENTS": {"type": "NUMBER", "index": 29, "name": "LOGIN_EVENTS", "comment": null}, "PURCHASE_EVENTS": {"type": "NUMBER", "index": 30, "name": "PURCHASE_EVENTS", "comment": null}, "MOBILE_EVENTS": {"type": "NUMBER", "index": 31, "name": "MOBILE_EVENTS", "comment": null}, "WEB_EVENTS": {"type": "NUMBER", "index": 32, "name": "WEB_EVENTS", "comment": null}, "EVENTS_PER_ORDER": {"type": "FLOAT", "index": 33, "name": "EVENTS_PER_ORDER", "comment": null}, "EVENTS_PER_SESSION": {"type": "FLOAT", "index": 34, "name": "EVENTS_PER_SESSION", "comment": null}, "AVG_DAYS_BETWEEN_ORDERS": {"type": "FLOAT", "index": 35, "name": "AVG_DAYS_BETWEEN_ORDERS", "comment": null}, "PURCHASE_SEGMENT": {"type": "TEXT", "index": 36, "name": "PURCHASE_SEGMENT", "comment": null}, "VALUE_SEGMENT": {"type": "TEXT", "index": 37, "name": "VALUE_SEGMENT", "comment": null}, "PLATFORM_PREFERENCE": {"type": "TEXT", "index": 38, "name": "PLATFORM_PREFERENCE", "comment": null}, "HAS_FUTURE_CREATION_DATE": {"type": "BOOLEAN", "index": 39, "name": "HAS_FUTURE_CREATION_DATE", "comment": null}, "HAS_INVALID_ACTIVITY_DATE": {"type": "BOOLEAN", "index": 40, "name": "HAS_INVALID_ACTIVITY_DATE", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 41, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TIMESTAMP_NTZ", "index": 42, "name": "GENERATED_AT", "comment": null}, "PROCESSED_AT": {"type": "TIMESTAMP_LTZ", "index": 43, "name": "PROCESSED_AT", "comment": null}, "MART_CREATED_AT": {"type": "TIMESTAMP_LTZ", "index": 44, "name": "MART_CREATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 30000, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 4357120, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:49UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.dim_users"}, "model.live_c360.mv_fact_orders": {"metadata": {"type": "VIEW", "schema": "LIVE_DATA_ANALYTICS", "name": "MV_FACT_ORDERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"ORDER_ID": {"type": "TEXT", "index": 1, "name": "ORDER_ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TIMESTAMP_NTZ", "index": 3, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 4, "name": "ITEM_COUNT", "comment": null}, "ORDER_AMOUNT": {"type": "NUMBER", "index": 5, "name": "ORDER_AMOUNT", "comment": null}, "USER_ORDER_SEQUENCE": {"type": "NUMBER", "index": 6, "name": "USER_ORDER_SEQUENCE", "comment": null}, "DAYS_SINCE_PREVIOUS_ORDER": {"type": "NUMBER", "index": 7, "name": "DAYS_SINCE_PREVIOUS_ORDER", "comment": null}, "RUNNING_TOTAL_AMOUNT": {"type": "NUMBER", "index": 8, "name": "RUNNING_TOTAL_AMOUNT", "comment": null}, "RUNNING_ORDER_COUNT": {"type": "NUMBER", "index": 9, "name": "RUNNING_ORDER_COUNT", "comment": null}, "GENERATED_AT": {"type": "TIMESTAMP_NTZ", "index": 10, "name": "GENERATED_AT", "comment": null}, "CUSTOMER_TYPE": {"type": "TEXT", "index": 11, "name": "CUSTOMER_TYPE", "comment": null}, "IS_HIGH_VALUE_ORDER": {"type": "BOOLEAN", "index": 12, "name": "IS_HIGH_VALUE_ORDER", "comment": null}, "IS_LARGE_ORDER": {"type": "BOOLEAN", "index": 13, "name": "IS_LARGE_ORDER", "comment": null}, "TIME_OF_DAY": {"type": "TEXT", "index": 14, "name": "TIME_OF_DAY", "comment": null}, "DBT_UPDATED_AT": {"type": "TIMESTAMP_LTZ", "index": 15, "name": "DBT_UPDATED_AT", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.mv_fact_orders"}, "model.live_c360.dim_users_scd2": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA", "name": "DIM_USERS_SCD2", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"USER_SK": {"type": "TEXT", "index": 1, "name": "USER_SK", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "EMAIL": {"type": "TEXT", "index": 5, "name": "EMAIL", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 6, "name": "ADDRESS", "comment": null}, "ACQUISITION_CHANNEL": {"type": "TEXT", "index": 7, "name": "ACQUISITION_CHANNEL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 8, "name": "COUNTRY", "comment": null}, "GENDER": {"type": "TEXT", "index": 9, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 10, "name": "AGE_GROUP", "comment": null}, "CREATION_DATE": {"type": "TIMESTAMP_NTZ", "index": 11, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TIMESTAMP_NTZ", "index": 12, "name": "LAST_ACTIVITY_DATE", "comment": null}, "CHURN": {"type": "BOOLEAN", "index": 13, "name": "CHURN", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 14, "name": "BATCH_ID", "comment": null}, "DBT_VALID_FROM": {"type": "TIMESTAMP_LTZ", "index": 15, "name": "DBT_VALID_FROM", "comment": null}, "DBT_VALID_TO": {"type": "TIMESTAMP_NTZ", "index": 16, "name": "DBT_VALID_TO", "comment": null}, "IS_CURRENT_VERSION": {"type": "BOOLEAN", "index": 17, "name": "IS_CURRENT_VERSION", "comment": null}, "DBT_CHANGE_TYPE": {"type": "TEXT", "index": 18, "name": "DBT_CHANGE_TYPE", "comment": null}, "PROCESSED_AT": {"type": "TIMESTAMP_LTZ", "index": 19, "name": "PROCESSED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 30000, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 4472832, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:49UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.live_c360.dim_users_scd2"}, "test.live_c360.source_not_null_live_external_ext_live_events_session_id.342957c8ec": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_EVENTS_SESSION_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "EVENT_ID": {"type": "TEXT", "index": 3, "name": "EVENT_ID", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 4, "name": "PLATFORM", "comment": null}, "DATE": {"type": "TEXT", "index": 5, "name": "DATE", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 7, "name": "SESSION_ID", "comment": null}, "URL": {"type": "TEXT", "index": 8, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 9, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 10, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_session_id.342957c8ec"}, "test.live_c360.source_not_null_live_external_ext_live_users_id.b0694be37c": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_USERS_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "EMAIL": {"type": "TEXT", "index": 5, "name": "EMAIL", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 6, "name": "ADDRESS", "comment": null}, "CANAL": {"type": "TEXT", "index": 7, "name": "CANAL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 8, "name": "COUNTRY", "comment": null}, "CREATION_DATE": {"type": "TEXT", "index": 9, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TEXT", "index": 10, "name": "LAST_ACTIVITY_DATE", "comment": null}, "GENDER": {"type": "NUMBER", "index": 11, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 12, "name": "AGE_GROUP", "comment": null}, "CHURN": {"type": "BOOLEAN", "index": 13, "name": "CHURN", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 14, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 15, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_id.b0694be37c"}, "test.live_c360.source_not_null_live_external_ext_live_orders_user_id.129a7fc228": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "SOURCE_NOT_NULL_LIVE_EXTERNAL_EXT_LIVE_ORDERS_USER_ID", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_user_id.129a7fc228"}, "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1.93502d99cd": {"metadata": {"type": "BASE TABLE", "schema": "LIVE_DATA_DBT_TEST__AUDIT", "name": "DBT_UTILS_SOURCE_ACCEPTED_RANG_2FA5C5EAA1E5C8F251141E01FD7779E8", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": null}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"row_count": {"id": "row_count", "label": "Row Count", "value": 0, "include": true, "description": "An approximate count of rows in this table"}, "bytes": {"id": "bytes", "label": "Approximate Size", "value": 0, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "last_modified": {"id": "last_modified", "label": "Last Modified", "value": "2025-06-03 20:50UTC", "include": true, "description": "The timestamp for last update/change"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1.93502d99cd"}}, "sources": {"source.live_c360.live_external.ext_live_orders": {"metadata": {"type": "EXTERNAL TABLE", "schema": "LIVE_DATA", "name": "EXT_LIVE_ORDERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": "The value of this row"}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "USER_ID": {"type": "TEXT", "index": 3, "name": "USER_ID", "comment": null}, "TRANSACTION_DATE": {"type": "TEXT", "index": 4, "name": "TRANSACTION_DATE", "comment": null}, "ITEM_COUNT": {"type": "NUMBER", "index": 5, "name": "ITEM_COUNT", "comment": null}, "AMOUNT": {"type": "NUMBER", "index": 6, "name": "AMOUNT", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 7, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 8, "name": "GENERATED_AT", "comment": null}}, "stats": {"bytes": {"id": "bytes", "label": "Approximate Size", "value": ********, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "source.live_c360.live_external.ext_live_orders"}, "source.live_c360.live_external.ext_live_users": {"metadata": {"type": "EXTERNAL TABLE", "schema": "LIVE_DATA", "name": "EXT_LIVE_USERS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": "The value of this row"}, "ID": {"type": "TEXT", "index": 2, "name": "ID", "comment": null}, "FIRSTNAME": {"type": "TEXT", "index": 3, "name": "FIRSTNAME", "comment": null}, "LASTNAME": {"type": "TEXT", "index": 4, "name": "LASTNAME", "comment": null}, "EMAIL": {"type": "TEXT", "index": 5, "name": "EMAIL", "comment": null}, "ADDRESS": {"type": "TEXT", "index": 6, "name": "ADDRESS", "comment": null}, "CANAL": {"type": "TEXT", "index": 7, "name": "CANAL", "comment": null}, "COUNTRY": {"type": "TEXT", "index": 8, "name": "COUNTRY", "comment": null}, "CREATION_DATE": {"type": "TEXT", "index": 9, "name": "CREATION_DATE", "comment": null}, "LAST_ACTIVITY_DATE": {"type": "TEXT", "index": 10, "name": "LAST_ACTIVITY_DATE", "comment": null}, "GENDER": {"type": "NUMBER", "index": 11, "name": "GENDER", "comment": null}, "AGE_GROUP": {"type": "NUMBER", "index": 12, "name": "AGE_GROUP", "comment": null}, "CHURN": {"type": "BOOLEAN", "index": 13, "name": "CHURN", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 14, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 15, "name": "GENERATED_AT", "comment": null}}, "stats": {"bytes": {"id": "bytes", "label": "Approximate Size", "value": 6716340, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "source.live_c360.live_external.ext_live_users"}, "source.live_c360.live_external.ext_live_events": {"metadata": {"type": "EXTERNAL TABLE", "schema": "LIVE_DATA", "name": "EXT_LIVE_EVENTS", "database": "MYDB", "comment": null, "owner": "ACCOUNTADMIN"}, "columns": {"VALUE": {"type": "VARIANT", "index": 1, "name": "VALUE", "comment": "The value of this row"}, "USER_ID": {"type": "TEXT", "index": 2, "name": "USER_ID", "comment": null}, "EVENT_ID": {"type": "TEXT", "index": 3, "name": "EVENT_ID", "comment": null}, "PLATFORM": {"type": "TEXT", "index": 4, "name": "PLATFORM", "comment": null}, "DATE": {"type": "TEXT", "index": 5, "name": "DATE", "comment": null}, "ACTION": {"type": "TEXT", "index": 6, "name": "ACTION", "comment": null}, "SESSION_ID": {"type": "TEXT", "index": 7, "name": "SESSION_ID", "comment": null}, "URL": {"type": "TEXT", "index": 8, "name": "URL", "comment": null}, "BATCH_ID": {"type": "TEXT", "index": 9, "name": "BATCH_ID", "comment": null}, "GENERATED_AT": {"type": "TEXT", "index": 10, "name": "GENERATED_AT", "comment": null}}, "stats": {"bytes": {"id": "bytes", "label": "Approximate Size", "value": ********, "include": true, "description": "Approximate size of the table as reported by Snowflake"}, "has_stats": {"id": "has_stats", "label": "Has Stats?", "value": true, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "source.live_c360.live_external.ext_live_events"}}, "errors": null}