[0m05:19:31.992803 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723271f2a860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723270e1bdf0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723270e18880>]}


============================== 05:19:31.998669 | 25b77bf6-2e9e-436e-97ea-98bdf0292428 ==============================
[0m05:19:31.998669 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:19:31.999735 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'debug': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'log_format': 'default', 'static_parser': 'True', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'target_path': 'None', 'invocation_command': 'dbt deps', 'send_anonymous_usage_stats': 'True'}
[0m05:19:32.172520 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '25b77bf6-2e9e-436e-97ea-98bdf0292428', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7232732f1330>]}
[0m05:19:32.203949 [debug] [MainThread]: Set downloads directory='/tmp/dbt-downloads-ocenpgek'
[0m05:19:32.208627 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m05:19:32.363329 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m05:19:32.365765 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m05:19:32.462786 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m05:19:32.470298 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m05:19:38.001873 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c4c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cb3f040>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cb3f100>]}


============================== 05:19:38.009949 | a5095a95-5c1c-43d9-8ca4-41756f50438c ==============================
[0m05:19:38.009949 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:19:38.012013 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m05:19:41.346909 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'a5095a95-5c1c-43d9-8ca4-41756f50438c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d72d120>]}
[0m05:19:41.430012 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'a5095a95-5c1c-43d9-8ca4-41756f50438c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d0951e0>]}
[0m05:19:41.431470 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:19:41.859206 [error] [MainThread]: Encountered an error:
Compilation Error
  dbt found more than one package with the name "dbt_utils" included in this project. Package names must be unique in a project. Please rename one of these packages.
[0m05:19:41.860899 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 1.7083069, "process_in_blocks": "0", "process_kernel_time": 0.635038, "process_mem_max_rss": "291712", "process_out_blocks": "0", "process_user_time": 3.453564}
[0m05:19:41.862321 [debug] [MainThread]: Command `cli run` failed at 05:19:41.862127 after 1.71 seconds
[0m05:19:41.863281 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c4c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81cd2560>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81cd10f0>]}
[0m05:19:41.864276 [debug] [MainThread]: Flushing usage events
[0m05:19:42.790230 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:19:45.669173 [info ] [MainThread]: Installed from version 1.1.1
[0m05:19:45.670196 [info ] [MainThread]: Updated version available: 1.3.0
[0m05:19:45.671160 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': '25b77bf6-2e9e-436e-97ea-98bdf0292428', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723271b991b0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723270c8b9a0>]}
[0m05:19:45.672124 [info ] [MainThread]: 
[0m05:19:45.673002 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m05:19:45.674746 [debug] [MainThread]: Resource report: {"command_name": "deps", "command_success": true, "command_wall_clock_time": 11.520399, "process_in_blocks": "0", "process_kernel_time": 1.002515, "process_mem_max_rss": "94780", "process_out_blocks": "200", "process_user_time": 2.660883}
[0m05:19:45.676158 [debug] [MainThread]: Command `dbt deps` succeeded at 05:19:45.675983 after 11.52 seconds
[0m05:19:45.677114 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723271f2a860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7232732f1330>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x723271ba6560>]}
[0m05:19:45.678027 [debug] [MainThread]: Flushing usage events
[0m05:19:46.626106 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:19:48.724847 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6c101327d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6c0f076a10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6c0f075960>]}


============================== 05:19:48.732833 | 77a8374d-d846-4c5d-bc61-6c59698afcd8 ==============================
[0m05:19:48.732833 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:19:48.734357 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'debug': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'version_check': 'True', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'introspect': 'True', 'log_format': 'default', 'target_path': 'None', 'invocation_command': 'dbt parse', 'send_anonymous_usage_stats': 'True'}
[0m05:19:49.596160 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '77a8374d-d846-4c5d-bc61-6c59698afcd8', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bfc524d00>]}
[0m05:19:49.714616 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '77a8374d-d846-4c5d-bc61-6c59698afcd8', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bfc524b80>]}
[0m05:19:49.716595 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:19:49.912309 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m05:19:49.919945 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m05:19:49.921050 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '77a8374d-d846-4c5d-bc61-6c59698afcd8', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bfc22aef0>]}
[0m05:19:53.159288 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m05:19:53.183644 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '77a8374d-d846-4c5d-bc61-6c59698afcd8', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bf741e440>]}
[0m05:19:53.192898 [info ] [MainThread]: Performance info: /opt/airflow/workspace/dbt_live/target/perf_info.json
[0m05:19:53.387924 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:19:53.392980 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:19:53.394704 [debug] [MainThread]: Resource report: {"command_name": "parse", "command_success": true, "command_wall_clock_time": 4.78898, "process_in_blocks": "0", "process_kernel_time": 0.531478, "process_mem_max_rss": "193072", "process_out_blocks": "0", "process_user_time": 5.595645}
[0m05:19:53.395998 [debug] [MainThread]: Command `dbt parse` succeeded at 05:19:53.395862 after 4.79 seconds
[0m05:19:53.398936 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6c101327d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bf7d13880>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e6bf7d11bd0>]}
[0m05:19:53.400179 [debug] [MainThread]: Flushing usage events
[0m05:19:54.297978 [debug] [MainThread]: An error was encountered while trying to flush usage events
