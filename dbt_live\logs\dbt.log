[0m05:26:38.011560 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96eb9a800>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96da9bb20>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96da9afb0>]}


============================== 05:26:38.024692 | d7d79d17-c1cd-434a-8460-ef91375204c6 ==============================
[0m05:26:38.024692 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:26:38.037076 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'static_parser': 'True', 'introspect': 'True', 'invocation_command': 'dbt deps', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'send_anonymous_usage_stats': 'True'}
[0m05:26:39.132571 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'd7d79d17-c1cd-434a-8460-ef91375204c6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96ff679d0>]}
[0m05:26:39.157984 [debug] [MainThread]: Set downloads directory='/tmp/dbt-downloads-r6evka1z'
[0m05:26:39.159172 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m05:26:40.660169 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m05:26:40.663440 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m05:26:40.756130 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m05:26:40.766253 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m05:26:45.985891 [info ] [MainThread]: Installed from version 1.1.1
[0m05:26:45.986955 [info ] [MainThread]: Updated version available: 1.3.0
[0m05:26:45.988888 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': 'd7d79d17-c1cd-434a-8460-ef91375204c6', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96e802350>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96d9139a0>]}
[0m05:26:45.990126 [info ] [MainThread]: 
[0m05:26:45.991321 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m05:26:45.993451 [debug] [MainThread]: Resource report: {"command_name": "deps", "command_success": true, "command_wall_clock_time": 5.8307056, "process_in_blocks": "0", "process_kernel_time": 0.591989, "process_mem_max_rss": "94900", "process_out_blocks": "200", "process_user_time": 2.310525}
[0m05:26:45.994848 [debug] [MainThread]: Command `dbt deps` succeeded at 05:26:45.994677 after 5.83 seconds
[0m05:26:45.995773 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96eb9a800>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96ff679d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x73f96e813700>]}
[0m05:26:45.996689 [debug] [MainThread]: Flushing usage events
[0m05:26:46.320672 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c4c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cb3f040>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cb3f100>]}


============================== 05:26:46.328891 | 570794da-3d22-451a-81c0-8ceef189434f ==============================
[0m05:26:46.328891 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:26:46.331208 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m05:26:46.910854 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:26:47.362132 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d72d120>]}
[0m05:26:47.449380 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d0951e0>]}
[0m05:26:47.451130 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:26:47.833112 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m05:26:47.840175 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m05:26:47.841700 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81cd0df0>]}
[0m05:26:49.138508 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e329f84e830>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e329e73c580>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e329e73f8e0>]}


============================== 05:26:49.144977 | 50062a97-7b60-4f00-bbde-25d539257fd6 ==============================
[0m05:26:49.144977 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:26:49.147264 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt parse', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m05:26:50.086774 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '50062a97-7b60-4f00-bbde-25d539257fd6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e3287ba9090>]}
[0m05:26:50.169320 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '50062a97-7b60-4f00-bbde-25d539257fd6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e3287b1ca30>]}
[0m05:26:50.170770 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:26:50.348874 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m05:26:50.353798 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m05:26:50.354805 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '50062a97-7b60-4f00-bbde-25d539257fd6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e3287afb010>]}
[0m05:26:51.002369 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m05:26:51.029003 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81221090>]}
[0m05:26:51.261629 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:26:51.271615 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:26:51.533466 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8132b160>]}
[0m05:26:51.535784 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m05:26:51.538905 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8132b250>]}
[0m05:26:51.543882 [info ] [MainThread]: 
[0m05:26:51.545764 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m05:26:51.547404 [info ] [MainThread]: 
[0m05:26:51.549556 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m05:26:51.557680 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m05:26:51.558867 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m05:26:51.610075 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m05:26:51.610775 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m05:26:51.611590 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m05:26:51.612424 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m05:26:51.613435 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:26:51.614420 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:26:52.143755 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.530 seconds
[0m05:26:52.240634 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.621 seconds
[0m05:26:52.263376 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m05:26:52.264372 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m05:26:52.265493 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m05:26:52.291914 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:26:52.295091 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:26:52.300438 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:26:52.301582 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:26:52.302521 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:26:52.303429 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:26:52.308270 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:26:52.356032 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.050 seconds
[0m05:26:52.361841 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:26:52.364372 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m05:26:52.368174 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.064 seconds
[0m05:26:52.391974 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:26:52.412593 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m05:26:52.475780 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.106 seconds
[0m05:26:52.485044 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:26:52.487068 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:26:52.522655 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.098 seconds
[0m05:26:52.525319 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:26:52.528100 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:26:52.570222 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.081 seconds
[0m05:26:52.587733 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.059 seconds
[0m05:26:52.788379 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.480 seconds
[0m05:26:52.791638 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:26:52.793053 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m05:26:52.868500 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.074 seconds
[0m05:26:52.871373 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:26:52.872400 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:26:52.935327 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.062 seconds
[0m05:26:52.942521 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8132b8b0>]}
[0m05:26:52.948241 [debug] [Thread-1 (]: Began running node model.live_c360.dbt_test_health
[0m05:26:52.948922 [debug] [Thread-2 (]: Began running node model.live_c360.pipeline_runtime_health
[0m05:26:52.949894 [debug] [Thread-3 (]: Began running node model.live_c360.query_history_health
[0m05:26:52.952453 [debug] [Thread-4 (]: Began running node model.live_c360.stg_events
[0m05:26:52.953580 [info ] [Thread-1 (]: 1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[0m05:26:52.964598 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dbt_test_health)
[0m05:26:52.965732 [debug] [Thread-1 (]: Began compiling node model.live_c360.dbt_test_health
[0m05:26:52.962952 [info ] [Thread-4 (]: 4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[0m05:26:52.955364 [info ] [Thread-2 (]: 2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[0m05:26:52.983872 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.pipeline_runtime_health)
[0m05:26:52.985132 [debug] [Thread-2 (]: Began compiling node model.live_c360.pipeline_runtime_health
[0m05:26:52.980440 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.stg_events'
[0m05:26:52.961345 [info ] [Thread-3 (]: 3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[0m05:26:52.978579 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dbt_test_health"
[0m05:26:52.990443 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.pipeline_runtime_health"
[0m05:26:52.993800 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_events
[0m05:26:52.995462 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.query_history_health)
[0m05:26:53.006335 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m05:26:53.007602 [debug] [Thread-3 (]: Began compiling node model.live_c360.query_history_health
[0m05:26:53.015139 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.query_history_health"
[0m05:26:53.022960 [debug] [Thread-2 (]: Began executing node model.live_c360.pipeline_runtime_health
[0m05:26:53.024171 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_events
[0m05:26:53.030662 [debug] [Thread-1 (]: Began executing node model.live_c360.dbt_test_health
[0m05:26:53.036997 [debug] [Thread-3 (]: Began executing node model.live_c360.query_history_health
[0m05:26:53.125007 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m05:26:53.130255 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.pipeline_runtime_health"
[0m05:26:53.142018 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dbt_test_health"
[0m05:26:53.147242 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.query_history_health"
[0m05:26:53.175499 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dbt_test_health"
[0m05:26:53.177150 [debug] [Thread-1 (]: On model.live_c360.dbt_test_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dbt_test_health"} */
create or replace transient table MYDB.LIVE_DATA.dbt_test_health
    

    
    as (

-- ETL Health Check: dbt Test Results Monitoring
-- Tracks dbt test execution and results

with test_metadata as (
    -- This would typically come from dbt artifacts or a custom logging solution
    -- For now, we'll create a mock structure based on our known tests
    select 'source_not_null_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_unique_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'unique' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_not_null_live_external_ext_live_orders_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'user_id' as column_name,
           'relationships' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,
           'source' as test_type,
           'ext_live_events' as model_name,
           'action' as column_name,
           'accepted_values' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
),

test_summary as (
    select
        date(execution_time) as test_date,
        test_type,
        model_name,
        test_category,
        
        -- Test counts
        count(*) as total_tests,
        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,
        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,
        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,
        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,
        
        -- Failure details
        sum(failures) as total_failures,
        max(execution_time) as latest_execution,
        
        -- Calculate pass rate
        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate
        
    from test_metadata
    group by date(execution_time), test_type, model_name, test_category
),

model_coverage as (
    -- Calculate test coverage per model
    select
        model_name,
        count(distinct test_category) as test_types_covered,
        count(*) as total_tests_on_model,
        
        -- Check for essential test coverage
        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,
        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,
        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,
        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests
        
    from test_metadata
    group by model_name
),

health_scores as (
    select
        ts.*,
        mc.test_types_covered,
        mc.total_tests_on_model,
        mc.has_not_null_tests,
        mc.has_unique_tests,
        mc.has_relationship_tests,
        mc.has_accepted_values_tests,
        
        -- Test reliability score
        case 
            when pass_rate = 100 then 100
            when pass_rate >= 95 then 90
            when pass_rate >= 90 then 80
            when pass_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Test coverage score
        case 
            when test_types_covered >= 4 then 100
            when test_types_covered >= 3 then 80
            when test_types_covered >= 2 then 60
            when test_types_covered >= 1 then 40
            else 20
        end as coverage_score
        
    from test_summary ts
    left join model_coverage mc on ts.model_name = mc.model_name
)

select
    test_date,
    test_type,
    model_name,
    test_category,
    total_tests,
    passed_tests,
    failed_tests,
    warning_tests,
    skipped_tests,
    total_failures,
    pass_rate,
    test_types_covered,
    total_tests_on_model,
    has_not_null_tests,
    has_unique_tests,
    has_relationship_tests,
    has_accepted_values_tests,
    reliability_score,
    coverage_score,
    
    -- Overall test health score
    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,
    
    -- Test status
    case 
        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'
        when pass_rate >= 95 and coverage_score >= 60 then 'Good'
        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'
        when pass_rate >= 80 then 'Poor'
        else 'Critical'
    end as test_health_status,
    
    latest_execution,
    current_timestamp() as health_check_timestamp
    
from health_scores
order by test_date desc, model_name, test_category
    )
;
[0m05:26:53.187375 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_events"
[0m05:26:53.192530 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.query_history_health"
[0m05:26:53.195530 [debug] [Thread-4 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m05:26:53.197797 [debug] [Thread-3 (]: On model.live_c360.query_history_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.query_history_health"} */
create or replace transient table MYDB.LIVE_DATA.query_history_health
    

    
    as (

-- ETL Health Check: Query History Monitoring
-- Tracks query performance, failures, and runtime metrics

with query_history as (
    select
        query_id,
        query_text,
        database_name,
        schema_name,
        query_type,
        session_id,
        user_name,
        role_name,
        warehouse_name,
        warehouse_size,
        warehouse_type,
        cluster_number,
        query_tag,
        execution_status,
        error_code,
        error_message,
        start_time,
        end_time,
        total_elapsed_time,
        bytes_scanned,
        percentage_scanned_from_cache,
        bytes_written,
        bytes_written_to_result,
        bytes_read_from_result,
        rows_produced,
        rows_inserted,
        rows_updated,
        rows_deleted,
        rows_unloaded,
        bytes_deleted,
        partitions_scanned,
        partitions_total,
        bytes_spilled_to_local_storage,
        bytes_spilled_to_remote_storage,
        bytes_sent_over_the_network,
        compilation_time,
        execution_time,
        queued_provisioning_time,
        queued_repair_time,
        queued_overload_time,
        transaction_blocked_time,
        outbound_data_transfer_cloud,
        outbound_data_transfer_region,
        outbound_data_transfer_bytes,
        inbound_data_transfer_cloud,
        inbound_data_transfer_region,
        inbound_data_transfer_bytes,
        list_external_files_time,
        credits_used_cloud_services
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7  -- Last 7 days
      and database_name = 'MYDB'  -- Focus on our database
),

etl_queries as (
    select
        *,
        -- Categorize queries
        case 
            when query_text ilike '%dbt%' then 'dbt'
            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'
            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'
            when query_text ilike '%copy into%' then 'Data_Load'
            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'
            else 'Other'
        end as query_category,
        
        -- Performance flags
        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes
        case when execution_status = 'FAIL' then true else false end as is_failed,
        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,
        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,
        
        -- Time segments
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour,
        case 
            when extract(hour from start_time) between 6 and 11 then 'Morning'
            when extract(hour from start_time) between 12 and 17 then 'Afternoon'
            when extract(hour from start_time) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_segment
        
    from query_history
),

health_metrics as (
    select
        execution_date,
        query_category,
        time_segment,
        warehouse_name,
        
        -- Count metrics
        count(*) as total_queries,
        sum(case when is_failed then 1 else 0 end) as failed_queries,
        sum(case when is_long_running then 1 else 0 end) as long_running_queries,
        sum(case when has_spill then 1 else 0 end) as queries_with_spill,
        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,
        
        -- Performance metrics
        avg(total_elapsed_time) as avg_elapsed_time_ms,
        max(total_elapsed_time) as max_elapsed_time_ms,
        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,
        
        -- Data metrics
        sum(bytes_scanned) as total_bytes_scanned,
        sum(rows_produced) as total_rows_produced,
        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,
        
        -- Cost metrics
        sum(credits_used_cloud_services) as total_credits_used,
        
        -- Latest execution
        max(start_time) as latest_execution_time
        
    from etl_queries
    group by execution_date, query_category, time_segment, warehouse_name
)

select
    *,
    -- Health scores (0-100)
    case 
        when failed_queries = 0 then 100
        when failed_queries::float / total_queries <= 0.01 then 95
        when failed_queries::float / total_queries <= 0.05 then 80
        when failed_queries::float / total_queries <= 0.10 then 60
        else 30
    end as reliability_score,
    
    case 
        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds
        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes
        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes
        else 30
    end as performance_score,
    
    case 
        when avg_cache_hit_rate >= 80 then 100
        when avg_cache_hit_rate >= 60 then 80
        when avg_cache_hit_rate >= 40 then 60
        else 30
    end as efficiency_score,
    
    -- Overall health score
    round((
        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +
        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +
        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3
    ), 0) as overall_health_score,
    
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, query_category, warehouse_name
    )
;
[0m05:26:53.204906 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.pipeline_runtime_health"
[0m05:26:53.206060 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m05:26:53.211159 [debug] [Thread-2 (]: On model.live_c360.pipeline_runtime_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.pipeline_runtime_health"} */
create or replace transient table MYDB.LIVE_DATA.pipeline_runtime_health
    

    
    as (

-- ETL Health Check: Pipeline Runtime Monitoring
-- Tracks pipeline execution times, success rates, and performance trends

with dbt_run_history as (
    -- Extract dbt-specific queries from query history
    select
        query_id,
        query_text,
        start_time,
        end_time,
        total_elapsed_time,
        execution_status,
        error_message,
        warehouse_name,
        user_name,
        
        -- Extract model name from dbt queries
        case 
            when query_text ilike '%create or replace%view%stg_%' then 
                regexp_substr(query_text, 'view\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%dim_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%fact_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%merge into%' then 
                regexp_substr(query_text, 'merge\\s+into\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            else 'unknown'
        end as model_name,
        
        -- Categorize model types
        case 
            when query_text ilike '%stg_%' then 'staging'
            when query_text ilike '%dim_%' then 'dimension'
            when query_text ilike '%fact_%' then 'fact'
            when query_text ilike '%mv_%' then 'materialized_view'
            else 'other'
        end as model_type,
        
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour
        
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7
      and database_name = 'MYDB'
      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')
      and query_text not ilike '%information_schema%'
      and query_text not ilike '%show%'
),

pipeline_runs as (
    -- Group queries into pipeline runs (by date and hour)
    select
        execution_date,
        execution_hour,
        model_type,
        warehouse_name,
        
        -- Run metrics
        count(*) as models_executed,
        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,
        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,
        
        -- Timing metrics
        sum(total_elapsed_time) as total_pipeline_time_ms,
        avg(total_elapsed_time) as avg_model_time_ms,
        max(total_elapsed_time) as max_model_time_ms,
        min(start_time) as pipeline_start_time,
        max(end_time) as pipeline_end_time,
        
        -- Calculate actual pipeline duration
        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,
        
        -- Error details
        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages
        
    from dbt_run_history
    where model_name != 'unknown'
    group by execution_date, execution_hour, model_type, warehouse_name
),

model_performance as (
    -- Individual model performance tracking
    select
        model_name,
        model_type,
        execution_date,
        
        -- Performance metrics
        count(*) as execution_count,
        avg(total_elapsed_time) as avg_execution_time_ms,
        max(total_elapsed_time) as max_execution_time_ms,
        min(total_elapsed_time) as min_execution_time_ms,
        stddev(total_elapsed_time) as stddev_execution_time_ms,
        
        -- Success rate
        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,
        
        -- Latest execution
        max(start_time) as latest_execution_time,
        
        -- Performance trend (compare to previous day)
        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time
        
    from dbt_run_history
    where model_name != 'unknown'
    group by model_name, model_type, execution_date
),

health_metrics as (
    select
        pr.*,
        
        -- Success rate
        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,
        
        -- Performance scores
        case 
            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds
            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes
            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes
            else 30
        end as performance_score,
        
        case 
            when pipeline_success_rate = 100 then 100
            when pipeline_success_rate >= 95 then 90
            when pipeline_success_rate >= 90 then 80
            when pipeline_success_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Duration score (based on total pipeline time)
        case 
            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes
            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes
            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes
            else 30
        end as duration_score
        
    from pipeline_runs pr
)

select
    execution_date,
    execution_hour,
    model_type,
    warehouse_name,
    models_executed,
    successful_models,
    failed_models,
    pipeline_success_rate,
    total_pipeline_time_ms,
    avg_model_time_ms,
    max_model_time_ms,
    pipeline_duration_ms,
    pipeline_start_time,
    pipeline_end_time,
    performance_score,
    reliability_score,
    duration_score,
    
    -- Overall pipeline health score
    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,
    
    -- Pipeline status
    case 
        when failed_models = 0 and performance_score >= 80 then 'Healthy'
        when failed_models = 0 and performance_score >= 60 then 'Good'
        when failed_models <= 1 and performance_score >= 60 then 'Fair'
        when failed_models <= 2 then 'Poor'
        else 'Critical'
    end as pipeline_status,
    
    error_messages,
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, execution_hour desc, model_type
    )
;
[0m05:26:53.741839 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc26-3204-802b-0002-4ad60006809e
[0m05:26:53.743229 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc26-3204-802b-0002-4ad60006809e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m05:26:53.745441 [debug] [Thread-4 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc26-3204-802b-0002-4ad60006809e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m05:26:53.748157 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81c625c0>]}
[0m05:26:53.749640 [error] [Thread-4 (]: 4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [[31mERROR[0m in 0.77s]
[0m05:26:53.751249 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_events
[0m05:26:53.752192 [debug] [Thread-4 (]: Began running node model.live_c360.stg_orders
[0m05:26:53.752892 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc26-3204-802b-0002-4ad60006809e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m05:26:53.753869 [info ] [Thread-4 (]: 5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[0m05:26:53.757594 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_events, now model.live_c360.stg_orders)
[0m05:26:53.758558 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_orders
[0m05:26:53.763336 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m05:26:53.768416 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_orders
[0m05:26:53.775432 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m05:26:53.783327 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m05:26:53.784595 [debug] [Thread-4 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m05:26:53.900904 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc26-3204-7ee0-0002-4ad60005c13e
[0m05:26:53.904682 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc26-3204-7ee0-0002-4ad60005c13e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m05:26:53.907641 [debug] [Thread-4 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc26-3204-7ee0-0002-4ad60005c13e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m05:26:53.922474 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8da5fdf0>]}
[0m05:26:53.941873 [debug] [Thread-1 (]: SQL status: SUCCESS 1 in 0.760 seconds
[0m05:26:53.937365 [error] [Thread-4 (]: 5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [[31mERROR[0m in 0.16s]
[0m05:26:53.981251 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_orders
[0m05:26:53.992417 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8e36c760>]}
[0m05:26:53.993164 [debug] [Thread-4 (]: Began running node model.live_c360.stg_users
[0m05:26:53.993860 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc26-3204-7ee0-0002-4ad60005c13e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m05:26:53.995437 [info ] [Thread-1 (]: 1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [[32mSUCCESS 1[0m in 1.03s]
[0m05:26:53.998127 [info ] [Thread-4 (]: 6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[0m05:26:54.002183 [debug] [Thread-1 (]: Finished running node model.live_c360.dbt_test_health
[0m05:26:54.003386 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_orders, now model.live_c360.stg_users)
[0m05:26:54.005026 [debug] [Thread-1 (]: Began running node model.live_c360.fact_orders
[0m05:26:54.006695 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_users
[0m05:26:54.008137 [info ] [Thread-1 (]: 7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [[33mSKIP[0m]
[0m05:26:54.016548 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m05:26:54.017673 [debug] [Thread-1 (]: Finished running node model.live_c360.fact_orders
[0m05:26:54.019252 [debug] [Thread-1 (]: Began running node model.live_c360.mv_fact_orders
[0m05:26:54.020317 [info ] [Thread-1 (]: 8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [[33mSKIP[0m]
[0m05:26:54.022070 [debug] [Thread-1 (]: Finished running node model.live_c360.mv_fact_orders
[0m05:26:54.024438 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_users
[0m05:26:54.030283 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m05:26:54.038276 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_users"
[0m05:26:54.039413 [debug] [Thread-4 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m05:26:54.154374 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc26-3204-7f69-0002-4ad60005d192
[0m05:26:54.156421 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc26-3204-7f69-0002-4ad60005d192: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m05:26:54.158639 [debug] [Thread-4 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc26-3204-7f69-0002-4ad60005d192: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m05:26:54.160535 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8126b8b0>]}
[0m05:26:54.164891 [error] [Thread-4 (]: 6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [[31mERROR[0m in 0.16s]
[0m05:26:54.178796 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_users
[0m05:26:54.186772 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc26-3204-7f69-0002-4ad60005d192: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m05:26:54.190391 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m05:26:54.191538 [debug] [Thread-4 (]: Began running node model.live_c360.dim_users_scd2
[0m05:26:54.193002 [info ] [Thread-1 (]: 9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [[33mSKIP[0m]
[0m05:26:54.194650 [info ] [Thread-4 (]: 10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [[33mSKIP[0m]
[0m05:26:54.198484 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m05:26:54.199629 [debug] [Thread-4 (]: Finished running node model.live_c360.dim_users_scd2
[0m05:26:54.201155 [debug] [Thread-1 (]: Began running node model.live_c360.data_quality_health
[0m05:26:54.202598 [info ] [Thread-1 (]: 11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [[33mSKIP[0m]
[0m05:26:54.204168 [debug] [Thread-1 (]: Finished running node model.live_c360.data_quality_health
[0m05:26:54.205606 [debug] [Thread-4 (]: Began running node model.live_c360.etl_health_dashboard
[0m05:26:54.206701 [info ] [Thread-4 (]: 12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [[33mSKIP[0m]
[0m05:26:54.212948 [debug] [Thread-4 (]: Finished running node model.live_c360.etl_health_dashboard
[0m05:26:54.240265 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m05:26:54.262291 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '50062a97-7b60-4f00-bbde-25d539257fd6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e3286ca9d50>]}
[0m05:26:54.270948 [info ] [MainThread]: Performance info: /opt/airflow/workspace/dbt_live/target/perf_info.json
[0m05:26:54.487113 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:26:54.492127 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:26:54.493771 [debug] [MainThread]: Resource report: {"command_name": "parse", "command_success": true, "command_wall_clock_time": 5.435545, "process_in_blocks": "0", "process_kernel_time": 0.647727, "process_mem_max_rss": "193208", "process_out_blocks": "0", "process_user_time": 6.335309}
[0m05:26:54.494880 [debug] [MainThread]: Command `dbt parse` succeeded at 05:26:54.494746 after 5.44 seconds
[0m05:26:54.495753 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e329f84e830>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e32876bbd90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7e32876bb6d0>]}
[0m05:26:54.496699 [debug] [MainThread]: Flushing usage events
[0m05:26:55.373747 [debug] [Thread-2 (]: SQL status: SUCCESS 1 in 2.158 seconds
[0m05:26:55.377897 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81906470>]}
[0m05:26:55.379743 [info ] [Thread-2 (]: 2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [[32mSUCCESS 1[0m in 2.39s]
[0m05:26:55.382082 [debug] [Thread-2 (]: Finished running node model.live_c360.pipeline_runtime_health
[0m05:26:55.412096 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:26:55.490849 [debug] [Thread-3 (]: SQL status: SUCCESS 1 in 2.283 seconds
[0m05:26:55.510288 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '570794da-3d22-451a-81c0-8ceef189434f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8132ad40>]}
[0m05:26:55.512422 [info ] [Thread-3 (]: 3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [[32mSUCCESS 1[0m in 2.51s]
[0m05:26:55.514772 [debug] [Thread-3 (]: Finished running node model.live_c360.query_history_health
[0m05:26:55.522296 [debug] [MainThread]: Connection 'master' was properly closed.
[0m05:26:55.526290 [debug] [MainThread]: Connection 'model.live_c360.query_history_health' was left open.
[0m05:26:55.527751 [debug] [MainThread]: On model.live_c360.query_history_health: Close
[0m05:26:55.598588 [debug] [MainThread]: Connection 'model.live_c360.pipeline_runtime_health' was left open.
[0m05:26:55.601390 [debug] [MainThread]: On model.live_c360.pipeline_runtime_health: Close
[0m05:26:55.664696 [debug] [MainThread]: Connection 'model.live_c360.dbt_test_health' was left open.
[0m05:26:55.683775 [debug] [MainThread]: On model.live_c360.dbt_test_health: Close
[0m05:26:55.753975 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m05:26:55.757126 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m05:26:55.833777 [info ] [MainThread]: 
[0m05:26:55.836611 [info ] [MainThread]: Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.28 seconds (4.28s).
[0m05:26:55.839514 [debug] [MainThread]: Command end result
[0m05:26:55.920812 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:26:55.930927 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:26:55.949079 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m05:26:55.959826 [info ] [MainThread]: 
[0m05:26:55.966967 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m05:26:55.981737 [info ] [MainThread]: 
[0m05:26:55.988757 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc26-3204-802b-0002-4ad60006809e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m05:26:55.990983 [info ] [MainThread]: 
[0m05:26:55.996943 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc26-3204-7ee0-0002-4ad60005c13e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m05:26:56.007282 [info ] [MainThread]: 
[0m05:26:56.011522 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc26-3204-7f69-0002-4ad60005d192: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m05:26:56.018604 [info ] [MainThread]: 
[0m05:26:56.027205 [info ] [MainThread]: Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[0m05:26:56.039731 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 9.802602, "process_in_blocks": "0", "process_kernel_time": 0.97481, "process_mem_max_rss": "325904", "process_out_blocks": "16", "process_user_time": 7.145707}
[0m05:26:56.055219 [debug] [MainThread]: Command `cli run` failed at 05:26:56.054960 after 9.82 seconds
[0m05:26:56.059976 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c4c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d72d120>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8126b550>]}
[0m05:26:56.074786 [debug] [MainThread]: Flushing usage events
[0m05:26:57.227161 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:32:00.050846 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a41f0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca9690>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8ccaa710>]}


============================== 05:32:00.057519 | f6f63e44-1b6c-400b-8d04-e6aed961363f ==============================
[0m05:32:00.057519 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:32:00.059649 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m05:32:00.878620 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81fb4d60>]}
[0m05:32:00.947024 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81fb7790>]}
[0m05:32:00.948925 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:32:02.238365 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m05:32:04.733648 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m05:32:04.734794 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m05:32:04.748841 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m05:32:04.811459 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8168a7d0>]}
[0m05:32:04.982470 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:32:04.988050 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:32:05.013472 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8182ff10>]}
[0m05:32:05.015002 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m05:32:05.016923 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed818d7640>]}
[0m05:32:05.020223 [info ] [MainThread]: 
[0m05:32:05.022227 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m05:32:05.024003 [info ] [MainThread]: 
[0m05:32:05.027448 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m05:32:05.034954 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m05:32:05.035955 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m05:32:05.070975 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m05:32:05.071720 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m05:32:05.074168 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m05:32:05.075533 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m05:32:05.076722 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:32:05.077962 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:32:05.774856 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.698 seconds
[0m05:32:05.776224 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.698 seconds
[0m05:32:05.785923 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m05:32:05.786854 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m05:32:05.787732 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m05:32:05.801665 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:32:05.805129 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:32:05.809974 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:32:05.811540 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:32:05.812722 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:32:05.814298 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m05:32:05.820623 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m05:32:05.901456 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.086 seconds
[0m05:32:05.903527 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.086 seconds
[0m05:32:05.913298 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:32:05.916870 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:32:05.919019 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m05:32:05.920662 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m05:32:06.030526 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.109 seconds
[0m05:32:06.033943 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m05:32:06.035337 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:32:06.070211 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.146 seconds
[0m05:32:06.074579 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m05:32:06.076227 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:32:06.103000 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.066 seconds
[0m05:32:06.181338 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.104 seconds
[0m05:32:06.310922 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.490 seconds
[0m05:32:06.314213 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:32:06.315594 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m05:32:06.423290 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.106 seconds
[0m05:32:06.427052 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m05:32:06.428545 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m05:32:06.501331 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.072 seconds
[0m05:32:06.508032 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8e478970>]}
[0m05:32:06.515582 [debug] [Thread-1 (]: Began running node model.live_c360.dbt_test_health
[0m05:32:06.516188 [debug] [Thread-2 (]: Began running node model.live_c360.pipeline_runtime_health
[0m05:32:06.516809 [debug] [Thread-3 (]: Began running node model.live_c360.query_history_health
[0m05:32:06.517564 [debug] [Thread-4 (]: Began running node model.live_c360.stg_events
[0m05:32:06.518505 [info ] [Thread-1 (]: 1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[0m05:32:06.520075 [info ] [Thread-2 (]: 2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[0m05:32:06.522223 [info ] [Thread-3 (]: 3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[0m05:32:06.524497 [info ] [Thread-4 (]: 4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[0m05:32:06.527723 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dbt_test_health)
[0m05:32:06.529212 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.pipeline_runtime_health)
[0m05:32:06.530615 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.query_history_health)
[0m05:32:06.533231 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.stg_events'
[0m05:32:06.534536 [debug] [Thread-1 (]: Began compiling node model.live_c360.dbt_test_health
[0m05:32:06.535685 [debug] [Thread-2 (]: Began compiling node model.live_c360.pipeline_runtime_health
[0m05:32:06.537051 [debug] [Thread-3 (]: Began compiling node model.live_c360.query_history_health
[0m05:32:06.538266 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_events
[0m05:32:06.548550 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dbt_test_health"
[0m05:32:06.553656 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.pipeline_runtime_health"
[0m05:32:06.560112 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.query_history_health"
[0m05:32:06.565223 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m05:32:06.574957 [debug] [Thread-3 (]: Began executing node model.live_c360.query_history_health
[0m05:32:06.576433 [debug] [Thread-2 (]: Began executing node model.live_c360.pipeline_runtime_health
[0m05:32:06.582502 [debug] [Thread-1 (]: Began executing node model.live_c360.dbt_test_health
[0m05:32:06.592821 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_events
[0m05:32:06.623233 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.query_history_health"
[0m05:32:06.629576 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.pipeline_runtime_health"
[0m05:32:06.634103 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dbt_test_health"
[0m05:32:06.660556 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m05:32:06.673556 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.query_history_health"
[0m05:32:06.678087 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dbt_test_health"
[0m05:32:06.679831 [debug] [Thread-3 (]: On model.live_c360.query_history_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.query_history_health"} */
create or replace transient table MYDB.LIVE_DATA.query_history_health
    

    
    as (

-- ETL Health Check: Query History Monitoring
-- Tracks query performance, failures, and runtime metrics

with query_history as (
    select
        query_id,
        query_text,
        database_name,
        schema_name,
        query_type,
        session_id,
        user_name,
        role_name,
        warehouse_name,
        warehouse_size,
        warehouse_type,
        cluster_number,
        query_tag,
        execution_status,
        error_code,
        error_message,
        start_time,
        end_time,
        total_elapsed_time,
        bytes_scanned,
        percentage_scanned_from_cache,
        bytes_written,
        bytes_written_to_result,
        bytes_read_from_result,
        rows_produced,
        rows_inserted,
        rows_updated,
        rows_deleted,
        rows_unloaded,
        bytes_deleted,
        partitions_scanned,
        partitions_total,
        bytes_spilled_to_local_storage,
        bytes_spilled_to_remote_storage,
        bytes_sent_over_the_network,
        compilation_time,
        execution_time,
        queued_provisioning_time,
        queued_repair_time,
        queued_overload_time,
        transaction_blocked_time,
        outbound_data_transfer_cloud,
        outbound_data_transfer_region,
        outbound_data_transfer_bytes,
        inbound_data_transfer_cloud,
        inbound_data_transfer_region,
        inbound_data_transfer_bytes,
        list_external_files_time,
        credits_used_cloud_services
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7  -- Last 7 days
      and database_name = 'MYDB'  -- Focus on our database
),

etl_queries as (
    select
        *,
        -- Categorize queries
        case 
            when query_text ilike '%dbt%' then 'dbt'
            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'
            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'
            when query_text ilike '%copy into%' then 'Data_Load'
            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'
            else 'Other'
        end as query_category,
        
        -- Performance flags
        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes
        case when execution_status = 'FAIL' then true else false end as is_failed,
        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,
        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,
        
        -- Time segments
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour,
        case 
            when extract(hour from start_time) between 6 and 11 then 'Morning'
            when extract(hour from start_time) between 12 and 17 then 'Afternoon'
            when extract(hour from start_time) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_segment
        
    from query_history
),

health_metrics as (
    select
        execution_date,
        query_category,
        time_segment,
        warehouse_name,
        
        -- Count metrics
        count(*) as total_queries,
        sum(case when is_failed then 1 else 0 end) as failed_queries,
        sum(case when is_long_running then 1 else 0 end) as long_running_queries,
        sum(case when has_spill then 1 else 0 end) as queries_with_spill,
        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,
        
        -- Performance metrics
        avg(total_elapsed_time) as avg_elapsed_time_ms,
        max(total_elapsed_time) as max_elapsed_time_ms,
        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,
        
        -- Data metrics
        sum(bytes_scanned) as total_bytes_scanned,
        sum(rows_produced) as total_rows_produced,
        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,
        
        -- Cost metrics
        sum(credits_used_cloud_services) as total_credits_used,
        
        -- Latest execution
        max(start_time) as latest_execution_time
        
    from etl_queries
    group by execution_date, query_category, time_segment, warehouse_name
)

select
    *,
    -- Health scores (0-100)
    case 
        when failed_queries = 0 then 100
        when failed_queries::float / total_queries <= 0.01 then 95
        when failed_queries::float / total_queries <= 0.05 then 80
        when failed_queries::float / total_queries <= 0.10 then 60
        else 30
    end as reliability_score,
    
    case 
        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds
        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes
        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes
        else 30
    end as performance_score,
    
    case 
        when avg_cache_hit_rate >= 80 then 100
        when avg_cache_hit_rate >= 60 then 80
        when avg_cache_hit_rate >= 40 then 60
        else 30
    end as efficiency_score,
    
    -- Overall health score
    round((
        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +
        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +
        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3
    ), 0) as overall_health_score,
    
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, query_category, warehouse_name
    )
;
[0m05:32:06.688492 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_events"
[0m05:32:06.685679 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.pipeline_runtime_health"
[0m05:32:06.690056 [debug] [Thread-1 (]: On model.live_c360.dbt_test_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dbt_test_health"} */
create or replace transient table MYDB.LIVE_DATA.dbt_test_health
    

    
    as (

-- ETL Health Check: dbt Test Results Monitoring
-- Tracks dbt test execution and results

with test_metadata as (
    -- This would typically come from dbt artifacts or a custom logging solution
    -- For now, we'll create a mock structure based on our known tests
    select 'source_not_null_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_unique_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'unique' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_not_null_live_external_ext_live_orders_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'user_id' as column_name,
           'relationships' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,
           'source' as test_type,
           'ext_live_events' as model_name,
           'action' as column_name,
           'accepted_values' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
),

test_summary as (
    select
        date(execution_time) as test_date,
        test_type,
        model_name,
        test_category,
        
        -- Test counts
        count(*) as total_tests,
        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,
        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,
        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,
        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,
        
        -- Failure details
        sum(failures) as total_failures,
        max(execution_time) as latest_execution,
        
        -- Calculate pass rate
        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate
        
    from test_metadata
    group by date(execution_time), test_type, model_name, test_category
),

model_coverage as (
    -- Calculate test coverage per model
    select
        model_name,
        count(distinct test_category) as test_types_covered,
        count(*) as total_tests_on_model,
        
        -- Check for essential test coverage
        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,
        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,
        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,
        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests
        
    from test_metadata
    group by model_name
),

health_scores as (
    select
        ts.*,
        mc.test_types_covered,
        mc.total_tests_on_model,
        mc.has_not_null_tests,
        mc.has_unique_tests,
        mc.has_relationship_tests,
        mc.has_accepted_values_tests,
        
        -- Test reliability score
        case 
            when pass_rate = 100 then 100
            when pass_rate >= 95 then 90
            when pass_rate >= 90 then 80
            when pass_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Test coverage score
        case 
            when test_types_covered >= 4 then 100
            when test_types_covered >= 3 then 80
            when test_types_covered >= 2 then 60
            when test_types_covered >= 1 then 40
            else 20
        end as coverage_score
        
    from test_summary ts
    left join model_coverage mc on ts.model_name = mc.model_name
)

select
    test_date,
    test_type,
    model_name,
    test_category,
    total_tests,
    passed_tests,
    failed_tests,
    warning_tests,
    skipped_tests,
    total_failures,
    pass_rate,
    test_types_covered,
    total_tests_on_model,
    has_not_null_tests,
    has_unique_tests,
    has_relationship_tests,
    has_accepted_values_tests,
    reliability_score,
    coverage_score,
    
    -- Overall test health score
    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,
    
    -- Test status
    case 
        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'
        when pass_rate >= 95 and coverage_score >= 60 then 'Good'
        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'
        when pass_rate >= 80 then 'Poor'
        else 'Critical'
    end as test_health_status,
    
    latest_execution,
    current_timestamp() as health_check_timestamp
    
from health_scores
order by test_date desc, model_name, test_category
    )
;
[0m05:32:06.694140 [debug] [Thread-4 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m05:32:06.696574 [debug] [Thread-2 (]: On model.live_c360.pipeline_runtime_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.pipeline_runtime_health"} */
create or replace transient table MYDB.LIVE_DATA.pipeline_runtime_health
    

    
    as (

-- ETL Health Check: Pipeline Runtime Monitoring
-- Tracks pipeline execution times, success rates, and performance trends

with dbt_run_history as (
    -- Extract dbt-specific queries from query history
    select
        query_id,
        query_text,
        start_time,
        end_time,
        total_elapsed_time,
        execution_status,
        error_message,
        warehouse_name,
        user_name,
        
        -- Extract model name from dbt queries
        case 
            when query_text ilike '%create or replace%view%stg_%' then 
                regexp_substr(query_text, 'view\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%dim_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%fact_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%merge into%' then 
                regexp_substr(query_text, 'merge\\s+into\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            else 'unknown'
        end as model_name,
        
        -- Categorize model types
        case 
            when query_text ilike '%stg_%' then 'staging'
            when query_text ilike '%dim_%' then 'dimension'
            when query_text ilike '%fact_%' then 'fact'
            when query_text ilike '%mv_%' then 'materialized_view'
            else 'other'
        end as model_type,
        
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour
        
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7
      and database_name = 'MYDB'
      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')
      and query_text not ilike '%information_schema%'
      and query_text not ilike '%show%'
),

pipeline_runs as (
    -- Group queries into pipeline runs (by date and hour)
    select
        execution_date,
        execution_hour,
        model_type,
        warehouse_name,
        
        -- Run metrics
        count(*) as models_executed,
        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,
        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,
        
        -- Timing metrics
        sum(total_elapsed_time) as total_pipeline_time_ms,
        avg(total_elapsed_time) as avg_model_time_ms,
        max(total_elapsed_time) as max_model_time_ms,
        min(start_time) as pipeline_start_time,
        max(end_time) as pipeline_end_time,
        
        -- Calculate actual pipeline duration
        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,
        
        -- Error details
        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages
        
    from dbt_run_history
    where model_name != 'unknown'
    group by execution_date, execution_hour, model_type, warehouse_name
),

model_performance as (
    -- Individual model performance tracking
    select
        model_name,
        model_type,
        execution_date,
        
        -- Performance metrics
        count(*) as execution_count,
        avg(total_elapsed_time) as avg_execution_time_ms,
        max(total_elapsed_time) as max_execution_time_ms,
        min(total_elapsed_time) as min_execution_time_ms,
        stddev(total_elapsed_time) as stddev_execution_time_ms,
        
        -- Success rate
        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,
        
        -- Latest execution
        max(start_time) as latest_execution_time,
        
        -- Performance trend (compare to previous day)
        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time
        
    from dbt_run_history
    where model_name != 'unknown'
    group by model_name, model_type, execution_date
),

health_metrics as (
    select
        pr.*,
        
        -- Success rate
        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,
        
        -- Performance scores
        case 
            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds
            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes
            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes
            else 30
        end as performance_score,
        
        case 
            when pipeline_success_rate = 100 then 100
            when pipeline_success_rate >= 95 then 90
            when pipeline_success_rate >= 90 then 80
            when pipeline_success_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Duration score (based on total pipeline time)
        case 
            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes
            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes
            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes
            else 30
        end as duration_score
        
    from pipeline_runs pr
)

select
    execution_date,
    execution_hour,
    model_type,
    warehouse_name,
    models_executed,
    successful_models,
    failed_models,
    pipeline_success_rate,
    total_pipeline_time_ms,
    avg_model_time_ms,
    max_model_time_ms,
    pipeline_duration_ms,
    pipeline_start_time,
    pipeline_end_time,
    performance_score,
    reliability_score,
    duration_score,
    
    -- Overall pipeline health score
    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,
    
    -- Pipeline status
    case 
        when failed_models = 0 and performance_score >= 80 then 'Healthy'
        when failed_models = 0 and performance_score >= 60 then 'Good'
        when failed_models <= 1 and performance_score >= 60 then 'Fair'
        when failed_models <= 2 then 'Poor'
        else 'Critical'
    end as pipeline_status,
    
    error_messages,
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, execution_hour desc, model_type
    )
;
[0m05:32:06.703224 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m05:32:07.265056 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc2c-3204-7f69-0002-4ad60005d196
[0m05:32:07.268182 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m05:32:07.276698 [debug] [Thread-4 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m05:32:07.281013 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed807a6470>]}
[0m05:32:07.283053 [error] [Thread-4 (]: 4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [[31mERROR[0m in 0.75s]
[0m05:32:07.285919 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_events
[0m05:32:07.291937 [debug] [Thread-4 (]: Began running node model.live_c360.stg_orders
[0m05:32:07.292923 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m05:32:07.294242 [info ] [Thread-4 (]: 5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[0m05:32:07.297548 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_events, now model.live_c360.stg_orders)
[0m05:32:07.300071 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_orders
[0m05:32:07.308162 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m05:32:07.314326 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_orders
[0m05:32:07.319571 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m05:32:07.330957 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m05:32:07.332642 [debug] [Thread-4 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m05:32:07.500439 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc2c-3204-7fc5-0002-4ad600063182
[0m05:32:07.501814 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m05:32:07.504142 [debug] [Thread-4 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m05:32:07.507157 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed807e3850>]}
[0m05:32:07.509241 [error] [Thread-4 (]: 5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [[31mERROR[0m in 0.21s]
[0m05:32:07.511210 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_orders
[0m05:32:07.512173 [debug] [Thread-4 (]: Began running node model.live_c360.stg_users
[0m05:32:07.512959 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m05:32:07.514996 [info ] [Thread-4 (]: 6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[0m05:32:07.518242 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_orders, now model.live_c360.stg_users)
[0m05:32:07.519855 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_users
[0m05:32:07.528393 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m05:32:07.540995 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_users
[0m05:32:07.546358 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m05:32:07.566666 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_users"
[0m05:32:07.577899 [debug] [Thread-4 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m05:32:07.634580 [debug] [Thread-1 (]: SQL status: SUCCESS 1 in 0.935 seconds
[0m05:32:07.732384 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8e3a3700>]}
[0m05:32:07.741154 [info ] [Thread-1 (]: 1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [[32mSUCCESS 1[0m in 1.20s]
[0m05:32:07.744295 [debug] [Thread-1 (]: Finished running node model.live_c360.dbt_test_health
[0m05:32:07.745440 [debug] [Thread-1 (]: Began running node model.live_c360.fact_orders
[0m05:32:07.746455 [info ] [Thread-1 (]: 7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [[33mSKIP[0m]
[0m05:32:07.748284 [debug] [Thread-1 (]: Finished running node model.live_c360.fact_orders
[0m05:32:07.749980 [debug] [Thread-1 (]: Began running node model.live_c360.mv_fact_orders
[0m05:32:07.751157 [info ] [Thread-1 (]: 8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [[33mSKIP[0m]
[0m05:32:07.752594 [debug] [Thread-1 (]: Finished running node model.live_c360.mv_fact_orders
[0m05:32:07.789743 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc2c-3204-7eab-0002-4ad6000611ce
[0m05:32:07.790787 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m05:32:07.792506 [debug] [Thread-4 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m05:32:07.793996 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed807a6470>]}
[0m05:32:07.795284 [error] [Thread-4 (]: 6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [[31mERROR[0m in 0.28s]
[0m05:32:07.796714 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_users
[0m05:32:07.797768 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m05:32:07.799023 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m05:32:07.799544 [debug] [Thread-4 (]: Began running node model.live_c360.dim_users_scd2
[0m05:32:07.800347 [info ] [Thread-1 (]: 9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [[33mSKIP[0m]
[0m05:32:07.802953 [info ] [Thread-4 (]: 10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [[33mSKIP[0m]
[0m05:32:07.804781 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m05:32:07.806009 [debug] [Thread-4 (]: Finished running node model.live_c360.dim_users_scd2
[0m05:32:07.807348 [debug] [Thread-1 (]: Began running node model.live_c360.data_quality_health
[0m05:32:07.808488 [info ] [Thread-1 (]: 11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [[33mSKIP[0m]
[0m05:32:07.809856 [debug] [Thread-1 (]: Finished running node model.live_c360.data_quality_health
[0m05:32:07.810981 [debug] [Thread-4 (]: Began running node model.live_c360.etl_health_dashboard
[0m05:32:07.811806 [info ] [Thread-4 (]: 12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [[33mSKIP[0m]
[0m05:32:07.813094 [debug] [Thread-4 (]: Finished running node model.live_c360.etl_health_dashboard
[0m05:32:09.096513 [debug] [Thread-3 (]: SQL status: SUCCESS 1 in 2.405 seconds
[0m05:32:09.102081 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed807e1b70>]}
[0m05:32:09.104167 [debug] [Thread-2 (]: SQL status: SUCCESS 1 in 2.399 seconds
[0m05:32:09.105666 [info ] [Thread-3 (]: 3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [[32mSUCCESS 1[0m in 2.57s]
[0m05:32:09.111701 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'f6f63e44-1b6c-400b-8d04-e6aed961363f', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed807945b0>]}
[0m05:32:09.113363 [debug] [Thread-3 (]: Finished running node model.live_c360.query_history_health
[0m05:32:09.115342 [info ] [Thread-2 (]: 2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [[32mSUCCESS 1[0m in 2.58s]
[0m05:32:09.118864 [debug] [Thread-2 (]: Finished running node model.live_c360.pipeline_runtime_health
[0m05:32:09.121863 [debug] [MainThread]: Connection 'master' was properly closed.
[0m05:32:09.124473 [debug] [MainThread]: Connection 'model.live_c360.pipeline_runtime_health' was left open.
[0m05:32:09.125927 [debug] [MainThread]: On model.live_c360.pipeline_runtime_health: Close
[0m05:32:09.210880 [debug] [MainThread]: Connection 'model.live_c360.query_history_health' was left open.
[0m05:32:09.212856 [debug] [MainThread]: On model.live_c360.query_history_health: Close
[0m05:32:09.323579 [debug] [MainThread]: Connection 'model.live_c360.dbt_test_health' was left open.
[0m05:32:09.329712 [debug] [MainThread]: On model.live_c360.dbt_test_health: Close
[0m05:32:09.413471 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m05:32:09.414668 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m05:32:09.483650 [info ] [MainThread]: 
[0m05:32:09.485993 [info ] [MainThread]: Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.46 seconds (4.46s).
[0m05:32:09.490104 [debug] [MainThread]: Command end result
[0m05:32:09.570697 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m05:32:09.578774 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m05:32:09.595246 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m05:32:09.596422 [info ] [MainThread]: 
[0m05:32:09.598313 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m05:32:09.600010 [info ] [MainThread]: 
[0m05:32:09.601819 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m05:32:09.603871 [info ] [MainThread]: 
[0m05:32:09.607155 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m05:32:09.608839 [info ] [MainThread]: 
[0m05:32:09.610621 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m05:32:09.612344 [info ] [MainThread]: 
[0m05:32:09.614051 [info ] [MainThread]: Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[0m05:32:09.616026 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 7.3760653, "process_in_blocks": "0", "process_kernel_time": 0.69787, "process_mem_max_rss": "320192", "process_out_blocks": "16", "process_user_time": 3.727648}
[0m05:32:09.617436 [debug] [MainThread]: Command `cli run` failed at 05:32:09.617254 after 7.38 seconds
[0m05:32:09.618623 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a41f0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8c7430>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed80760250>]}
[0m05:32:09.621954 [debug] [MainThread]: Flushing usage events
[0m05:32:10.805266 [debug] [MainThread]: An error was encountered while trying to flush usage events
