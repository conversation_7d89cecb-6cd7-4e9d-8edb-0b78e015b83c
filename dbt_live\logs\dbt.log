[0m14:43:58.843103 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B488290>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B488350>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B48A290>]}
[0m14:43:59.049816 [debug] [MainThread]: An error was encountered while trying to send an event


============================== 14:43:59.049816 | 7ac27c76-cb48-4dde-9a7f-19e9be1a52fc ==============================
[0m14:43:59.049816 [info ] [MainThread]: Running with dbt=1.9.6
[0m14:43:59.049816 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live', 'version_check': 'True', 'debug': 'False', 'log_path': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt deps', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'send_anonymous_usage_stats': 'True'}
[0m14:43:59.383161 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '7ac27c76-cb48-4dde-9a7f-19e9be1a52fc', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B4E59D0>]}
[0m14:43:59.383161 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:43:59.764774 [debug] [MainThread]: Set downloads directory='C:\Users\<USER>\AppData\Local\Temp\dbt-downloads-72cnlouk'
[0m14:43:59.766542 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m14:44:00.551150 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m14:44:00.561167 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m14:44:01.403679 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m14:44:01.403679 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m14:44:07.550185 [info ] [MainThread]: Installed from version 1.1.1
[0m14:44:07.550185 [info ] [MainThread]: Updated version available: 1.3.0
[0m14:44:07.560187 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': '7ac27c76-cb48-4dde-9a7f-19e9be1a52fc', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1A670550>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B5B5550>]}
[0m14:44:07.560187 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:07.560187 [info ] [MainThread]: 
[0m14:44:07.560187 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m14:44:07.560187 [debug] [MainThread]: Command `dbt deps` succeeded at 14:44:07.560187 after 9.21 seconds
[0m14:44:07.566869 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B156850>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF1B591CD0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002AF14CABB50>]}
[0m14:44:07.566869 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:07.566869 [debug] [MainThread]: Flushing usage events
[0m14:44:11.027157 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6563D890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6563E650>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6563CF10>]}
[0m14:44:11.037157 [debug] [MainThread]: An error was encountered while trying to send an event


============================== 14:44:11.037157 | 5ce89b78-0ddf-4d27-bffd-19fdebffd6d6 ==============================
[0m14:44:11.037157 [info ] [MainThread]: Running with dbt=1.9.6
[0m14:44:11.037157 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live', 'version_check': 'True', 'debug': 'False', 'log_path': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'log_format': 'default', 'invocation_command': 'dbt parse', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m14:44:25.041950 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '5ce89b78-0ddf-4d27-bffd-19fdebffd6d6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D65631CD0>]}
[0m14:44:25.041950 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:25.119536 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '5ce89b78-0ddf-4d27-bffd-19fdebffd6d6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D647D3690>]}
[0m14:44:25.119536 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:25.119536 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m14:44:26.054110 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m14:44:26.063238 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m14:44:26.063238 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '5ce89b78-0ddf-4d27-bffd-19fdebffd6d6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D69C23B10>]}
[0m14:44:26.063238 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:29.375800 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m14:44:29.404838 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '5ce89b78-0ddf-4d27-bffd-19fdebffd6d6', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6A0C1F50>]}
[0m14:44:29.405839 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:29.410681 [info ] [MainThread]: Performance info: G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\perf_info.json
[0m14:44:29.605736 [debug] [MainThread]: Wrote artifact WritableManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\manifest.json
[0m14:44:29.636853 [debug] [MainThread]: Wrote artifact SemanticManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\semantic_manifest.json
[0m14:44:29.638852 [debug] [MainThread]: Command `dbt parse` succeeded at 14:44:29.637856 after 18.72 seconds
[0m14:44:29.639727 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6568F050>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6568ED90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022D6568FE90>]}
[0m14:44:29.639727 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:44:29.640729 [debug] [MainThread]: Flushing usage events
[0m04:44:57.362859 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c310>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca0d60>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca2440>]}


============================== 04:44:57.370633 | b728db91-1a44-48e7-974e-1302e2a0db7a ==============================
[0m04:44:57.370633 [info ] [MainThread]: Running with dbt=1.9.6
[0m04:44:57.372718 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m04:44:58.283713 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'b728db91-1a44-48e7-974e-1302e2a0db7a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d081240>]}
[0m04:44:58.354324 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'b728db91-1a44-48e7-974e-1302e2a0db7a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81deaad0>]}
[0m04:44:58.356344 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m04:44:58.730129 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m04:44:59.837277 [debug] [MainThread]: Partial parsing enabled: 208 files deleted, 208 files added, 0 files changed.
[0m04:44:59.839113 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/equals.sql
[0m04:44:59.840427 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/relationships.sql
[0m04:44:59.841630 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_filtered_columns_in_relation.sql
[0m04:44:59.842602 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/datediff.sql
[0m04:44:59.844366 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/not_null.sql
[0m04:44:59.845974 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/drop.sql
[0m04:44:59.847000 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_constant.sql
[0m04:44:59.847857 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/columns.sql
[0m04:44:59.848663 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast_bool_to_text.sql
[0m04:44:59.849470 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/accepted_values.sql
[0m04:44:59.850374 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/indexes.sql
[0m04:44:59.851216 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/haversine_distance.sql
[0m04:44:59.852176 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/relationships_where.sql
[0m04:44:59.853068 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/refresh.sql
[0m04:44:59.853964 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename_intermediate.sql
[0m04:44:59.855228 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/any_value.sql
[0m04:44:59.856142 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop.sql
[0m04:44:59.857227 [debug] [MainThread]: Partial parsing: added file: live_c360://models/sources.yml
[0m04:44:59.858115 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/accepted_range.sql
[0m04:44:59.859185 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/escape_single_quotes.sql
[0m04:44:59.860733 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/groupby.sql
[0m04:44:59.861650 [debug] [MainThread]: Partial parsing: added file: dbt://macros/python_model/python.sql
[0m04:44:59.862485 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/describe.sql
[0m04:44:59.863274 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental.sql
[0m04:44:59.864287 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename.sql
[0m04:44:59.865410 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_schema.sql
[0m04:44:59.866446 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/table.sql
[0m04:44:59.867475 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/fact_orders.sql
[0m04:44:59.868368 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/generate_series.sql
[0m04:44:59.869250 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_concat.sql
[0m04:44:59.870140 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/create.sql
[0m04:44:59.871156 [debug] [MainThread]: Partial parsing: added file: dbt://docs/overview.md
[0m04:44:59.872020 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/alter.sql
[0m04:44:59.872884 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_database.sql
[0m04:44:59.873716 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_accepted_values.sql
[0m04:44:59.874592 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_parameter.sql
[0m04:44:59.876693 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/bool_or.sql
[0m04:44:59.877712 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/metadata.sql
[0m04:44:59.878634 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_alias.sql
[0m04:44:59.879430 [debug] [MainThread]: Partial parsing: added file: dbt://macros/unit_test_sql/get_fixture_sql.sql
[0m04:44:59.880212 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/apply_grants.sql
[0m04:44:59.881016 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/position.sql
[0m04:44:59.881810 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_column_values.sql
[0m04:44:59.882729 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_orders.sql
[0m04:44:59.883559 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/rename.sql
[0m04:44:59.884353 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/rename.sql
[0m04:44:59.885146 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/schema.sql
[0m04:44:59.886049 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/timestamps.sql
[0m04:44:59.886973 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/helpers.sql
[0m04:44:59.887819 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_backup.sql
[0m04:44:59.888688 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/dateadd.sql
[0m04:44:59.889610 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/view.sql
[0m04:44:59.891705 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/sequential_values.sql
[0m04:44:59.892625 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/drop.sql
[0m04:44:59.893446 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/create.sql
[0m04:44:59.894313 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata/list_relations_without_caching.sql
[0m04:44:59.895249 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/dbt_test_health.sql
[0m04:44:59.896224 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/strategies.sql
[0m04:44:59.897107 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/datetime.sql
[0m04:44:59.897900 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/escape_single_quotes.sql
[0m04:44:59.898739 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/column_helpers.sql
[0m04:44:59.899544 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/is_incremental.sql
[0m04:44:59.900334 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equal_rowcount.sql
[0m04:44:59.901115 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/right.sql
[0m04:44:59.901893 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_construct.sql
[0m04:44:59.902697 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/schema.sql
[0m04:44:59.903473 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/drop.sql
[0m04:44:59.904274 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/hooks.sql
[0m04:44:59.905875 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create.sql
[0m04:44:59.907552 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/query_history_health.sql
[0m04:44:59.908704 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/clone.sql
[0m04:44:59.909766 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/test.sql
[0m04:44:59.910660 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/fewer_rows_than.sql
[0m04:44:59.911753 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/last_day.sql
[0m04:44:59.912901 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast.sql
[0m04:44:59.914069 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/test.sql
[0m04:44:59.915112 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_subtract.sql
[0m04:44:59.916239 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date.sql
[0m04:44:59.917255 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/timestamps.sql
[0m04:44:59.918310 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/unit.sql
[0m04:44:59.919329 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users_scd2.sql
[0m04:44:59.920248 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/create.sql
[0m04:44:59.921154 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_pattern.sql
[0m04:44:59.923010 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/slugify.sql
[0m04:44:59.924222 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/replace.sql
[0m04:44:59.925443 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/replace.sql
[0m04:44:59.926577 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_single_value.sql
[0m04:44:59.927562 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/bool_or.sql
[0m04:44:59.928430 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/merge.sql
[0m04:44:59.929274 [debug] [MainThread]: Partial parsing: added file: dbt://tests/generic/builtin.sql
[0m04:44:59.930073 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create_backup.sql
[0m04:44:59.931016 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/replace.sql
[0m04:44:59.931885 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/rename.sql
[0m04:44:59.932699 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/dynamic_table.sql
[0m04:44:59.933674 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/right.sql
[0m04:44:59.934678 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/materialized_view.sql
[0m04:44:59.935542 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/can_clone_table.sql
[0m04:44:59.936371 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/expression_is_true.sql
[0m04:44:59.938113 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/statement.sql
[0m04:44:59.939365 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/replace.sql
[0m04:44:59.940462 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/replace.sql
[0m04:44:59.941410 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/where_subquery.sql
[0m04:44:59.943131 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/date_spine.sql
[0m04:44:59.944277 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_null_proportion.sql
[0m04:44:59.945149 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/clone.sql
[0m04:44:59.945947 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/listagg.sql
[0m04:44:59.946754 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/data_types.sql
[0m04:44:59.947578 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/catalog.sql
[0m04:44:59.948365 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/create.sql
[0m04:44:59.949145 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/column/columns_spec_ddl.sql
[0m04:44:59.949938 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/apply_grants.sql
[0m04:44:59.950908 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/cardinality_equality.sql
[0m04:44:59.951744 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/unique.sql
[0m04:44:59.952631 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_divide.sql
[0m04:44:59.955431 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename.sql
[0m04:44:59.956492 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/length.sql
[0m04:44:59.957476 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/at_least_one.sql
[0m04:44:59.958469 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/helpers.sql
[0m04:44:59.959385 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/on_schema_change.sql
[0m04:44:59.960270 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/pipeline_runtime_health.sql
[0m04:44:59.961135 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/freshness.sql
[0m04:44:59.962034 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/data_quality_health.sql
[0m04:44:59.963011 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/create_or_replace_clone.sql
[0m04:44:59.963898 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/literal.sql
[0m04:44:59.964798 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename_intermediate.sql
[0m04:44:59.965693 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_trunc.sql
[0m04:44:59.966627 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/strategies.sql
[0m04:44:59.969031 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/cast.sql
[0m04:44:59.970263 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot.sql
[0m04:44:59.971303 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/show.sql
[0m04:44:59.972178 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck.sql
[0m04:44:59.973048 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/union.sql
[0m04:44:59.973912 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/mutually_exclusive_ranges.sql
[0m04:44:59.974902 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/insert_overwrite.sql
[0m04:44:59.976127 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/split_part.sql
[0m04:44:59.977055 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/except.sql
[0m04:44:59.978003 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_query_results_as_dict.sql
[0m04:44:59.978893 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equality.sql
[0m04:44:59.979766 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/configs.sql
[0m04:44:59.980665 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/deduplicate.sql
[0m04:44:59.981579 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_path.sql
[0m04:44:59.983459 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users.sql
[0m04:44:59.985615 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/safe_cast.sql
[0m04:44:59.986602 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/log_info.sql
[0m04:44:59.987484 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_events.sql
[0m04:44:59.988340 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/array_construct.sql
[0m04:44:59.989244 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/concat.sql
[0m04:44:59.991759 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_prefix.sql
[0m04:44:59.992989 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/relation.sql
[0m04:44:59.993964 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot_merge.sql
[0m04:44:59.994962 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/hash.sql
[0m04:44:59.995906 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/drop.sql
[0m04:44:59.996812 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/incremental.sql
[0m04:44:59.997754 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/table.sql
[0m04:44:59.998693 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/helpers.sql
[0m04:45:00.001171 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/rename.sql
[0m04:45:00.002263 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/pivot.sql
[0m04:45:00.003227 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/etl_health_dashboard.sql
[0m04:45:00.004102 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/intersect.sql
[0m04:45:00.005010 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_empty_string.sql
[0m04:45:00.005948 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/replace.sql
[0m04:45:00.006988 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/validate_sql.sql
[0m04:45:00.007923 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_ephemeral.sql
[0m04:45:00.008825 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/unique_combination_of_columns.sql
[0m04:45:00.009963 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/adapters.sql
[0m04:45:00.011061 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_relation.sql
[0m04:45:00.012008 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/replace.sql
[0m04:45:00.012914 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_log_format.sql
[0m04:45:00.013830 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/width_bucket.sql
[0m04:45:00.014909 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_users.sql
[0m04:45:00.016574 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/drop.sql
[0m04:45:00.017671 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/snapshot.sql
[0m04:45:00.020112 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/refresh.sql
[0m04:45:00.025244 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_pattern_sql.sql
[0m04:45:00.026330 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/seed.sql
[0m04:45:00.027268 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/create.sql
[0m04:45:00.028195 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata.sql
[0m04:45:00.029024 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/alter.sql
[0m04:45:00.029818 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_prefix_sql.sql
[0m04:45:00.031660 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/mv_fact_orders.sql
[0m04:45:00.032791 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/drop.sql
[0m04:45:00.033660 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/replace.sql
[0m04:45:00.034485 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/replace.sql
[0m04:45:00.035330 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_intermediate.sql
[0m04:45:00.036142 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create.sql
[0m04:45:00.036922 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_host.sql
[0m04:45:00.037711 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop_backup.sql
[0m04:45:00.038520 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/recency.sql
[0m04:45:00.039331 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/surrogate_key.sql
[0m04:45:00.040115 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/unpivot.sql
[0m04:45:00.040890 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/rename.sql
[0m04:45:00.041752 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_time.sql
[0m04:45:00.043112 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/optional.sql
[0m04:45:00.044010 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/persist_docs.sql
[0m04:45:00.044842 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_series.sql
[0m04:45:00.045667 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/drop.sql
[0m04:45:00.047540 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_add.sql
[0m04:45:00.048644 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_table_types_sql.sql
[0m04:45:00.049630 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/merge.sql
[0m04:45:00.050639 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/star.sql
[0m04:45:00.051586 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_spine.sql
[0m04:45:00.052585 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/safe_cast.sql
[0m04:45:00.053477 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck_table.sql
[0m04:45:00.054353 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_surrogate_key.sql
[0m04:45:00.055184 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/view.sql
[0m04:45:00.056006 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/create.sql
[0m04:45:00.056801 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/seed.sql
[0m04:45:00.057785 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_append.sql
[0m04:45:00.059174 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\relation.sql
[0m04:45:00.059993 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\replace.sql
[0m04:45:00.060745 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_prefix.sql
[0m04:45:00.062639 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\rename.sql
[0m04:45:00.063704 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\clone.sql
[0m04:45:00.064664 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\cast.sql
[0m04:45:00.065422 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\is_incremental.sql
[0m04:45:00.066644 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\apply_grants.sql
[0m04:45:00.067682 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_surrogate_key.sql
[0m04:45:00.068509 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\date_spine.sql
[0m04:45:00.069257 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\etl_health_dashboard.sql
[0m04:45:00.070124 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\rename.sql
[0m04:45:00.070981 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\clone.sql
[0m04:45:00.071739 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\incremental.sql
[0m04:45:00.072486 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\unpivot.sql
[0m04:45:00.073231 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_construct.sql
[0m04:45:00.073993 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck.sql
[0m04:45:00.074813 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\describe.sql
[0m04:45:00.075578 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\unit.sql
[0m04:45:00.076477 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\deduplicate.sql
[0m04:45:00.078302 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot_merge.sql
[0m04:45:00.079314 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\refresh.sql
[0m04:45:00.080209 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\drop.sql
[0m04:45:00.081010 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\log_info.sql
[0m04:45:00.082136 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\create.sql
[0m04:45:00.083085 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\schema.sql
[0m04:45:00.083927 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_divide.sql
[0m04:45:00.084957 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop.sql
[0m04:45:00.086140 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\optional.sql
[0m04:45:00.087312 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\create.sql
[0m04:45:00.088284 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\data_types.sql
[0m04:45:00.089080 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_time.sql
[0m04:45:00.089823 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\test.sql
[0m04:45:00.090609 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental.sql
[0m04:45:00.091353 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\unique.sql
[0m04:45:00.092105 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\at_least_one.sql
[0m04:45:00.093834 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_backup.sql
[0m04:45:00.095499 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\table.sql
[0m04:45:00.096915 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\statement.sql
[0m04:45:00.097700 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equal_rowcount.sql
[0m04:45:00.098498 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\relationships.sql
[0m04:45:00.099330 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\merge.sql
[0m04:45:00.100093 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\not_null.sql
[0m04:45:00.100834 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_append.sql
[0m04:45:00.101584 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\length.sql
[0m04:45:00.102418 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\query_history_health.sql
[0m04:45:00.103354 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\create.sql
[0m04:45:00.104430 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\replace.sql
[0m04:45:00.105427 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_orders.sql
[0m04:45:00.106448 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\insert_overwrite.sql
[0m04:45:00.107406 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_schema.sql
[0m04:45:00.108382 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_concat.sql
[0m04:45:00.110308 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\hash.sql
[0m04:45:00.111455 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users.sql
[0m04:45:00.112477 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\helpers.sql
[0m04:45:00.113537 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\replace.sql
[0m04:45:00.114571 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\listagg.sql
[0m04:45:00.115574 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\catalog.sql
[0m04:45:00.117021 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\replace.sql
[0m04:45:00.117999 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create.sql
[0m04:45:00.118860 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\concat.sql
[0m04:45:00.119656 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\safe_cast.sql
[0m04:45:00.120400 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\fewer_rows_than.sql
[0m04:45:00.121182 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_series.sql
[0m04:45:00.122472 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\column\columns_spec_ddl.sql
[0m04:45:00.123305 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\literal.sql
[0m04:45:00.124945 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\validate_sql.sql
[0m04:45:00.125979 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata\list_relations_without_caching.sql
[0m04:45:00.126812 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\any_value.sql
[0m04:45:00.127568 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_subtract.sql
[0m04:45:00.128312 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\mv_fact_orders.sql
[0m04:45:00.129130 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\drop.sql
[0m04:45:00.129876 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\bool_or.sql
[0m04:45:00.131199 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\strategies.sql
[0m04:45:00.132225 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\drop.sql
[0m04:45:00.133133 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_trunc.sql
[0m04:45:00.133999 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\position.sql
[0m04:45:00.134843 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_relation.sql
[0m04:45:00.135688 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\hooks.sql
[0m04:45:00.136460 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\materialized_view.sql
[0m04:45:00.137211 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\relationships_where.sql
[0m04:45:00.137977 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\test.sql
[0m04:45:00.138764 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\accepted_range.sql
[0m04:45:00.140686 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\slugify.sql
[0m04:45:00.141831 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_spine.sql
[0m04:45:00.142706 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equality.sql
[0m04:45:00.143489 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_host.sql
[0m04:45:00.144269 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_empty_string.sql
[0m04:45:00.145091 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\create.sql
[0m04:45:00.145922 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\replace.sql
[0m04:45:00.146799 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename.sql
[0m04:45:00.147591 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date.sql
[0m04:45:00.148382 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata.sql
[0m04:45:00.149179 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\seed.sql
[0m04:45:00.150118 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\dynamic_table.sql
[0m04:45:00.151242 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\rename.sql
[0m04:45:00.152361 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast.sql
[0m04:45:00.153310 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename_intermediate.sql
[0m04:45:00.154130 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\create_or_replace_clone.sql
[0m04:45:00.154941 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast_bool_to_text.sql
[0m04:45:00.157282 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\create.sql
[0m04:45:00.158145 [debug] [MainThread]: Partial parsing: deleted file: dbt://tests\generic\builtin.sql
[0m04:45:00.159014 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\recency.sql
[0m04:45:00.159883 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_column_values.sql
[0m04:45:00.160659 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\refresh.sql
[0m04:45:00.161456 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\split_part.sql
[0m04:45:00.162287 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\star.sql
[0m04:45:00.163079 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\generate_series.sql
[0m04:45:00.163867 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\view.sql
[0m04:45:00.164762 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\timestamps.sql
[0m04:45:00.165639 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\column_helpers.sql
[0m04:45:00.166514 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\drop.sql
[0m04:45:00.167313 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\drop.sql
[0m04:45:00.168072 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_filtered_columns_in_relation.sql
[0m04:45:00.169095 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\fact_orders.sql
[0m04:45:00.169978 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\replace.sql
[0m04:45:00.172032 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_accepted_values.sql
[0m04:45:00.173044 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\merge.sql
[0m04:45:00.173875 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\haversine_distance.sql
[0m04:45:00.174724 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users_scd2.sql
[0m04:45:00.175522 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\snapshot.sql
[0m04:45:00.176312 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\view.sql
[0m04:45:00.177099 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\table.sql
[0m04:45:00.177917 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\data_quality_health.sql
[0m04:45:00.178787 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_users.sql
[0m04:45:00.179592 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\alter.sql
[0m04:45:00.180349 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_alias.sql
[0m04:45:00.181111 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\unique_combination_of_columns.sql
[0m04:45:00.181860 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\union.sql
[0m04:45:00.182679 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\strategies.sql
[0m04:45:00.183427 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\sequential_values.sql
[0m04:45:00.184171 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\width_bucket.sql
[0m04:45:00.184913 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\mutually_exclusive_ranges.sql
[0m04:45:00.185667 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\datetime.sql
[0m04:45:00.187998 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop_backup.sql
[0m04:45:00.188887 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\right.sql
[0m04:45:00.189679 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_path.sql
[0m04:45:00.190513 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\except.sql
[0m04:45:00.191297 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename_intermediate.sql
[0m04:45:00.192054 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create_backup.sql
[0m04:45:00.192814 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_database.sql
[0m04:45:00.193584 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\last_day.sql
[0m04:45:00.194394 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\escape_single_quotes.sql
[0m04:45:00.195168 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot.sql
[0m04:45:00.195937 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck_table.sql
[0m04:45:00.196749 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\apply_grants.sql
[0m04:45:00.197501 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\drop.sql
[0m04:45:00.198319 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\right.sql
[0m04:45:00.199502 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\columns.sql
[0m04:45:00.200262 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\accepted_values.sql
[0m04:45:00.201017 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\rename.sql
[0m04:45:00.201770 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\rename.sql
[0m04:45:00.203419 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\replace.sql
[0m04:45:00.204520 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_pattern_sql.sql
[0m04:45:00.205455 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_null_proportion.sql
[0m04:45:00.206352 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\datediff.sql
[0m04:45:00.207173 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\configs.sql
[0m04:45:00.207936 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\alter.sql
[0m04:45:00.208736 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\create.sql
[0m04:45:00.209566 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\equals.sql
[0m04:45:00.210388 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_prefix_sql.sql
[0m04:45:00.211400 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\adapters.sql
[0m04:45:00.212323 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\replace.sql
[0m04:45:00.213164 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\safe_cast.sql
[0m04:45:00.214003 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\persist_docs.sql
[0m04:45:00.214814 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_pattern.sql
[0m04:45:00.215588 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\array_construct.sql
[0m04:45:00.216385 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\on_schema_change.sql
[0m04:45:00.217181 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_table_types_sql.sql
[0m04:45:00.219512 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\bool_or.sql
[0m04:45:00.222432 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\replace.sql
[0m04:45:00.223479 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\schema.sql
[0m04:45:00.224452 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\dateadd.sql
[0m04:45:00.225349 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename.sql
[0m04:45:00.226268 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_events.sql
[0m04:45:00.229161 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\helpers.sql
[0m04:45:00.230259 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_query_results_as_dict.sql
[0m04:45:00.231086 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create.sql
[0m04:45:00.231848 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\freshness.sql
[0m04:45:00.232600 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\expression_is_true.sql
[0m04:45:00.234496 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\drop.sql
[0m04:45:00.235529 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\indexes.sql
[0m04:45:00.236384 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_ephemeral.sql
[0m04:45:00.237202 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_intermediate.sql
[0m04:45:00.238001 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\seed.sql
[0m04:45:00.238827 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_constant.sql
[0m04:45:00.239593 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\cardinality_equality.sql
[0m04:45:00.240337 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\dbt_test_health.sql
[0m04:45:00.241155 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\metadata.sql
[0m04:45:00.242214 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_parameter.sql
[0m04:45:00.243059 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\pipeline_runtime_health.sql
[0m04:45:00.243857 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\show.sql
[0m04:45:00.244711 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\timestamps.sql
[0m04:45:00.245491 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\unit_test_sql\get_fixture_sql.sql
[0m04:45:00.246285 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\escape_single_quotes.sql
[0m04:45:00.247079 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\intersect.sql
[0m04:45:00.247830 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_log_format.sql
[0m04:45:00.249505 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\where_subquery.sql
[0m04:45:00.250724 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\python_model\python.sql
[0m04:45:00.251554 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_single_value.sql
[0m04:45:00.252325 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_add.sql
[0m04:45:00.253226 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\helpers.sql
[0m04:45:00.253994 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\can_clone_table.sql
[0m04:45:00.254801 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\pivot.sql
[0m04:45:00.255575 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\surrogate_key.sql
[0m04:45:00.256419 [debug] [MainThread]: Partial parsing: deleted file: dbt://docs\overview.md
[0m04:45:00.257218 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\groupby.sql
[0m04:45:00.278978 [error] [MainThread]: Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[0m04:45:00.282344 [error] [MainThread]: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'

[0m04:45:00.284192 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 3.0032578, "process_in_blocks": "0", "process_kernel_time": 0.56666, "process_mem_max_rss": "297140", "process_out_blocks": "0", "process_user_time": 3.103354}
[0m04:45:00.285414 [debug] [MainThread]: Command `cli run` failed at 04:45:00.285294 after 3.00 seconds
[0m04:45:00.286375 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c310>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81da5090>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81fb4730>]}
[0m04:45:00.287471 [debug] [MainThread]: Flushing usage events
[0m04:45:01.217740 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m04:50:03.239489 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a0280>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8ccaab90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8ccaa200>]}


============================== 04:50:03.245873 | 36c9b7ea-0fb3-4110-9f86-5a6a20ddfa10 ==============================
[0m04:50:03.245873 [info ] [MainThread]: Running with dbt=1.9.6
[0m04:50:03.249303 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m04:50:04.144102 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '36c9b7ea-0fb3-4110-9f86-5a6a20ddfa10', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cceb880>]}
[0m04:50:04.232856 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '36c9b7ea-0fb3-4110-9f86-5a6a20ddfa10', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d7f1c60>]}
[0m04:50:04.234690 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m04:50:04.659143 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m04:50:07.932715 [debug] [MainThread]: Partial parsing enabled: 208 files deleted, 208 files added, 0 files changed.
[0m04:50:07.934200 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/equals.sql
[0m04:50:07.935281 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/relationships.sql
[0m04:50:07.936320 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_filtered_columns_in_relation.sql
[0m04:50:07.937456 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/datediff.sql
[0m04:50:07.938573 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/not_null.sql
[0m04:50:07.939562 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/drop.sql
[0m04:50:07.940602 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_constant.sql
[0m04:50:07.941751 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/columns.sql
[0m04:50:07.942730 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast_bool_to_text.sql
[0m04:50:07.945082 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/accepted_values.sql
[0m04:50:07.946124 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/indexes.sql
[0m04:50:07.947066 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/haversine_distance.sql
[0m04:50:07.947972 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/relationships_where.sql
[0m04:50:07.948862 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/refresh.sql
[0m04:50:07.949947 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename_intermediate.sql
[0m04:50:07.951107 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/any_value.sql
[0m04:50:07.952256 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop.sql
[0m04:50:07.953454 [debug] [MainThread]: Partial parsing: added file: live_c360://models/sources.yml
[0m04:50:07.954492 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/accepted_range.sql
[0m04:50:07.955606 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/escape_single_quotes.sql
[0m04:50:07.956678 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/groupby.sql
[0m04:50:07.957732 [debug] [MainThread]: Partial parsing: added file: dbt://macros/python_model/python.sql
[0m04:50:07.959771 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/describe.sql
[0m04:50:07.961376 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental.sql
[0m04:50:07.962316 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename.sql
[0m04:50:07.963341 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_schema.sql
[0m04:50:07.964305 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/table.sql
[0m04:50:07.965344 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/fact_orders.sql
[0m04:50:07.966332 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/generate_series.sql
[0m04:50:07.967267 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_concat.sql
[0m04:50:07.968226 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/create.sql
[0m04:50:07.969168 [debug] [MainThread]: Partial parsing: added file: dbt://docs/overview.md
[0m04:50:07.970066 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/alter.sql
[0m04:50:07.970997 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_database.sql
[0m04:50:07.971954 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_accepted_values.sql
[0m04:50:07.973228 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_parameter.sql
[0m04:50:07.974329 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/bool_or.sql
[0m04:50:07.976054 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/metadata.sql
[0m04:50:07.977274 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_alias.sql
[0m04:50:07.978374 [debug] [MainThread]: Partial parsing: added file: dbt://macros/unit_test_sql/get_fixture_sql.sql
[0m04:50:07.979390 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/apply_grants.sql
[0m04:50:07.980500 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/position.sql
[0m04:50:07.981616 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_column_values.sql
[0m04:50:07.982597 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_orders.sql
[0m04:50:07.983679 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/rename.sql
[0m04:50:07.985410 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/rename.sql
[0m04:50:07.986525 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/schema.sql
[0m04:50:07.987502 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/timestamps.sql
[0m04:50:07.988553 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/helpers.sql
[0m04:50:07.989552 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_backup.sql
[0m04:50:07.991492 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/dateadd.sql
[0m04:50:07.992675 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/view.sql
[0m04:50:07.993869 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/sequential_values.sql
[0m04:50:07.994965 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/drop.sql
[0m04:50:07.996045 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/create.sql
[0m04:50:07.997115 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata/list_relations_without_caching.sql
[0m04:50:07.998129 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/dbt_test_health.sql
[0m04:50:07.999465 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/strategies.sql
[0m04:50:08.000643 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/datetime.sql
[0m04:50:08.001841 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/escape_single_quotes.sql
[0m04:50:08.002899 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/column_helpers.sql
[0m04:50:08.004051 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/is_incremental.sql
[0m04:50:08.005174 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equal_rowcount.sql
[0m04:50:08.007524 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/right.sql
[0m04:50:08.008882 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_construct.sql
[0m04:50:08.010196 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/schema.sql
[0m04:50:08.011344 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/drop.sql
[0m04:50:08.012440 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/hooks.sql
[0m04:50:08.013543 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create.sql
[0m04:50:08.014611 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/query_history_health.sql
[0m04:50:08.015656 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/clone.sql
[0m04:50:08.016616 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/test.sql
[0m04:50:08.017604 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/fewer_rows_than.sql
[0m04:50:08.018680 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/last_day.sql
[0m04:50:08.019646 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast.sql
[0m04:50:08.020598 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/test.sql
[0m04:50:08.022703 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_subtract.sql
[0m04:50:08.023870 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date.sql
[0m04:50:08.025025 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/timestamps.sql
[0m04:50:08.026112 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/unit.sql
[0m04:50:08.027212 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users_scd2.sql
[0m04:50:08.028254 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/create.sql
[0m04:50:08.029330 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_pattern.sql
[0m04:50:08.030263 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/slugify.sql
[0m04:50:08.031617 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/replace.sql
[0m04:50:08.032909 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/replace.sql
[0m04:50:08.034228 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_single_value.sql
[0m04:50:08.035512 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/bool_or.sql
[0m04:50:08.036710 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/merge.sql
[0m04:50:08.041818 [debug] [MainThread]: Partial parsing: added file: dbt://tests/generic/builtin.sql
[0m04:50:08.049133 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create_backup.sql
[0m04:50:08.051250 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/replace.sql
[0m04:50:08.054891 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/rename.sql
[0m04:50:08.058286 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/dynamic_table.sql
[0m04:50:08.060734 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/right.sql
[0m04:50:08.062532 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/materialized_view.sql
[0m04:50:08.063954 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/can_clone_table.sql
[0m04:50:08.065377 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/expression_is_true.sql
[0m04:50:08.068162 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/statement.sql
[0m04:50:08.071501 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/replace.sql
[0m04:50:08.073032 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/replace.sql
[0m04:50:08.074627 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/where_subquery.sql
[0m04:50:08.076096 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/date_spine.sql
[0m04:50:08.077461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_null_proportion.sql
[0m04:50:08.078892 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/clone.sql
[0m04:50:08.080369 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/listagg.sql
[0m04:50:08.081822 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/data_types.sql
[0m04:50:08.083123 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/catalog.sql
[0m04:50:08.085652 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/create.sql
[0m04:50:08.087162 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/column/columns_spec_ddl.sql
[0m04:50:08.088607 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/apply_grants.sql
[0m04:50:08.091411 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/cardinality_equality.sql
[0m04:50:08.093289 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/unique.sql
[0m04:50:08.095016 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_divide.sql
[0m04:50:08.096847 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename.sql
[0m04:50:08.097861 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/length.sql
[0m04:50:08.098921 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/at_least_one.sql
[0m04:50:08.100724 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/helpers.sql
[0m04:50:08.102299 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/on_schema_change.sql
[0m04:50:08.104196 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/pipeline_runtime_health.sql
[0m04:50:08.105383 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/freshness.sql
[0m04:50:08.106660 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/data_quality_health.sql
[0m04:50:08.107939 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/create_or_replace_clone.sql
[0m04:50:08.109156 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/literal.sql
[0m04:50:08.110307 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename_intermediate.sql
[0m04:50:08.111491 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_trunc.sql
[0m04:50:08.112609 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/strategies.sql
[0m04:50:08.113747 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/cast.sql
[0m04:50:08.114984 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot.sql
[0m04:50:08.116852 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/show.sql
[0m04:50:08.118107 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck.sql
[0m04:50:08.119568 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/union.sql
[0m04:50:08.121068 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/mutually_exclusive_ranges.sql
[0m04:50:08.122396 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/insert_overwrite.sql
[0m04:50:08.123912 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/split_part.sql
[0m04:50:08.125037 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/except.sql
[0m04:50:08.125973 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_query_results_as_dict.sql
[0m04:50:08.127066 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equality.sql
[0m04:50:08.128009 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/configs.sql
[0m04:50:08.129348 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/deduplicate.sql
[0m04:50:08.130458 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_path.sql
[0m04:50:08.132428 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users.sql
[0m04:50:08.133811 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/safe_cast.sql
[0m04:50:08.135153 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/log_info.sql
[0m04:50:08.136224 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_events.sql
[0m04:50:08.137469 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/array_construct.sql
[0m04:50:08.138497 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/concat.sql
[0m04:50:08.139507 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_prefix.sql
[0m04:50:08.141907 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/relation.sql
[0m04:50:08.143416 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot_merge.sql
[0m04:50:08.144584 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/hash.sql
[0m04:50:08.145775 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/drop.sql
[0m04:50:08.148244 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/incremental.sql
[0m04:50:08.149458 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/table.sql
[0m04:50:08.150565 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/helpers.sql
[0m04:50:08.151682 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/rename.sql
[0m04:50:08.152769 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/pivot.sql
[0m04:50:08.153916 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/etl_health_dashboard.sql
[0m04:50:08.155235 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/intersect.sql
[0m04:50:08.156295 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_empty_string.sql
[0m04:50:08.157417 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/replace.sql
[0m04:50:08.158477 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/validate_sql.sql
[0m04:50:08.159633 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_ephemeral.sql
[0m04:50:08.160642 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/unique_combination_of_columns.sql
[0m04:50:08.162750 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/adapters.sql
[0m04:50:08.163971 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_relation.sql
[0m04:50:08.165019 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/replace.sql
[0m04:50:08.166003 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_log_format.sql
[0m04:50:08.166939 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/width_bucket.sql
[0m04:50:08.167898 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_users.sql
[0m04:50:08.168836 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/drop.sql
[0m04:50:08.169931 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/snapshot.sql
[0m04:50:08.171026 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/refresh.sql
[0m04:50:08.171945 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_pattern_sql.sql
[0m04:50:08.172933 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/seed.sql
[0m04:50:08.174003 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/create.sql
[0m04:50:08.174951 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata.sql
[0m04:50:08.175897 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/alter.sql
[0m04:50:08.176977 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_prefix_sql.sql
[0m04:50:08.179073 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/mv_fact_orders.sql
[0m04:50:08.180226 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/drop.sql
[0m04:50:08.181373 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/replace.sql
[0m04:50:08.182497 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/replace.sql
[0m04:50:08.183616 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_intermediate.sql
[0m04:50:08.184836 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create.sql
[0m04:50:08.185887 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_host.sql
[0m04:50:08.186821 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop_backup.sql
[0m04:50:08.187754 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/recency.sql
[0m04:50:08.188661 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/surrogate_key.sql
[0m04:50:08.189758 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/unpivot.sql
[0m04:50:08.190706 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/rename.sql
[0m04:50:08.191647 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_time.sql
[0m04:50:08.192561 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/optional.sql
[0m04:50:08.194746 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/persist_docs.sql
[0m04:50:08.196429 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_series.sql
[0m04:50:08.198038 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/drop.sql
[0m04:50:08.199291 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_add.sql
[0m04:50:08.200380 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_table_types_sql.sql
[0m04:50:08.201570 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/merge.sql
[0m04:50:08.202678 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/star.sql
[0m04:50:08.203815 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_spine.sql
[0m04:50:08.204879 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/safe_cast.sql
[0m04:50:08.206189 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck_table.sql
[0m04:50:08.207444 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_surrogate_key.sql
[0m04:50:08.208838 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/view.sql
[0m04:50:08.210551 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/create.sql
[0m04:50:08.211823 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/seed.sql
[0m04:50:08.212858 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_append.sql
[0m04:50:08.214444 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\relation.sql
[0m04:50:08.215555 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\replace.sql
[0m04:50:08.216648 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_prefix.sql
[0m04:50:08.217694 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\rename.sql
[0m04:50:08.218761 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\clone.sql
[0m04:50:08.219902 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\cast.sql
[0m04:50:08.221278 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\is_incremental.sql
[0m04:50:08.222910 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\apply_grants.sql
[0m04:50:08.223972 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_surrogate_key.sql
[0m04:50:08.225923 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\date_spine.sql
[0m04:50:08.227201 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\etl_health_dashboard.sql
[0m04:50:08.228585 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\rename.sql
[0m04:50:08.229684 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\clone.sql
[0m04:50:08.230784 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\incremental.sql
[0m04:50:08.231775 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\unpivot.sql
[0m04:50:08.232978 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_construct.sql
[0m04:50:08.234086 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck.sql
[0m04:50:08.235095 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\describe.sql
[0m04:50:08.236325 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\unit.sql
[0m04:50:08.237487 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\deduplicate.sql
[0m04:50:08.238538 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot_merge.sql
[0m04:50:08.239662 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\refresh.sql
[0m04:50:08.241804 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\drop.sql
[0m04:50:08.243062 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\log_info.sql
[0m04:50:08.244426 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\create.sql
[0m04:50:08.245560 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\schema.sql
[0m04:50:08.246579 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_divide.sql
[0m04:50:08.247693 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop.sql
[0m04:50:08.248758 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\optional.sql
[0m04:50:08.250078 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\create.sql
[0m04:50:08.251200 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\data_types.sql
[0m04:50:08.252269 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_time.sql
[0m04:50:08.253126 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\test.sql
[0m04:50:08.254081 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental.sql
[0m04:50:08.254951 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\unique.sql
[0m04:50:08.256847 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\at_least_one.sql
[0m04:50:08.257898 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_backup.sql
[0m04:50:08.259118 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\table.sql
[0m04:50:08.260697 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\statement.sql
[0m04:50:08.261909 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equal_rowcount.sql
[0m04:50:08.263042 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\relationships.sql
[0m04:50:08.264207 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\merge.sql
[0m04:50:08.265314 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\not_null.sql
[0m04:50:08.266387 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_append.sql
[0m04:50:08.267405 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\length.sql
[0m04:50:08.268472 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\query_history_health.sql
[0m04:50:08.269757 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\create.sql
[0m04:50:08.270739 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\replace.sql
[0m04:50:08.272863 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_orders.sql
[0m04:50:08.274006 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\insert_overwrite.sql
[0m04:50:08.275074 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_schema.sql
[0m04:50:08.276102 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_concat.sql
[0m04:50:08.277151 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\hash.sql
[0m04:50:08.278275 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users.sql
[0m04:50:08.279449 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\helpers.sql
[0m04:50:08.280562 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\replace.sql
[0m04:50:08.281616 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\listagg.sql
[0m04:50:08.282720 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\catalog.sql
[0m04:50:08.283841 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\replace.sql
[0m04:50:08.285025 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create.sql
[0m04:50:08.286094 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\concat.sql
[0m04:50:08.288273 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\safe_cast.sql
[0m04:50:08.289910 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\fewer_rows_than.sql
[0m04:50:08.291396 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_series.sql
[0m04:50:08.293268 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\column\columns_spec_ddl.sql
[0m04:50:08.294449 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\literal.sql
[0m04:50:08.295535 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\validate_sql.sql
[0m04:50:08.296409 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata\list_relations_without_caching.sql
[0m04:50:08.297321 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\any_value.sql
[0m04:50:08.298323 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_subtract.sql
[0m04:50:08.299286 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\mv_fact_orders.sql
[0m04:50:08.300204 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\drop.sql
[0m04:50:08.301132 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\bool_or.sql
[0m04:50:08.302144 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\strategies.sql
[0m04:50:08.304200 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\drop.sql
[0m04:50:08.305534 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_trunc.sql
[0m04:50:08.306733 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\position.sql
[0m04:50:08.308347 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_relation.sql
[0m04:50:08.309568 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\hooks.sql
[0m04:50:08.310638 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\materialized_view.sql
[0m04:50:08.311719 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\relationships_where.sql
[0m04:50:08.312717 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\test.sql
[0m04:50:08.313738 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\accepted_range.sql
[0m04:50:08.314602 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\slugify.sql
[0m04:50:08.315544 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_spine.sql
[0m04:50:08.316388 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equality.sql
[0m04:50:08.317491 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_host.sql
[0m04:50:08.319514 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_empty_string.sql
[0m04:50:08.320728 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\create.sql
[0m04:50:08.321660 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\replace.sql
[0m04:50:08.322663 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename.sql
[0m04:50:08.323505 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date.sql
[0m04:50:08.324460 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata.sql
[0m04:50:08.325494 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\seed.sql
[0m04:50:08.326497 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\dynamic_table.sql
[0m04:50:08.327672 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\rename.sql
[0m04:50:08.328737 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast.sql
[0m04:50:08.329801 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename_intermediate.sql
[0m04:50:08.330705 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\create_or_replace_clone.sql
[0m04:50:08.331607 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast_bool_to_text.sql
[0m04:50:08.332591 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\create.sql
[0m04:50:08.333642 [debug] [MainThread]: Partial parsing: deleted file: dbt://tests\generic\builtin.sql
[0m04:50:08.335297 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\recency.sql
[0m04:50:08.336314 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_column_values.sql
[0m04:50:08.337258 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\refresh.sql
[0m04:50:08.338315 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\split_part.sql
[0m04:50:08.339333 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\star.sql
[0m04:50:08.340283 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\generate_series.sql
[0m04:50:08.341499 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\view.sql
[0m04:50:08.342677 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\timestamps.sql
[0m04:50:08.343740 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\column_helpers.sql
[0m04:50:08.344737 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\drop.sql
[0m04:50:08.345872 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\drop.sql
[0m04:50:08.346758 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_filtered_columns_in_relation.sql
[0m04:50:08.347681 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\fact_orders.sql
[0m04:50:08.348520 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\replace.sql
[0m04:50:08.350726 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_accepted_values.sql
[0m04:50:08.351978 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\merge.sql
[0m04:50:08.353052 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\haversine_distance.sql
[0m04:50:08.354115 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users_scd2.sql
[0m04:50:08.355143 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\snapshot.sql
[0m04:50:08.356098 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\view.sql
[0m04:50:08.357054 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\table.sql
[0m04:50:08.357969 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\data_quality_health.sql
[0m04:50:08.359383 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_users.sql
[0m04:50:08.360406 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\alter.sql
[0m04:50:08.361338 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_alias.sql
[0m04:50:08.362279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\unique_combination_of_columns.sql
[0m04:50:08.363207 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\union.sql
[0m04:50:08.364316 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\strategies.sql
[0m04:50:08.366202 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\sequential_values.sql
[0m04:50:08.367269 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\width_bucket.sql
[0m04:50:08.368123 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\mutually_exclusive_ranges.sql
[0m04:50:08.369088 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\datetime.sql
[0m04:50:08.369921 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop_backup.sql
[0m04:50:08.370843 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\right.sql
[0m04:50:08.371675 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_path.sql
[0m04:50:08.372595 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\except.sql
[0m04:50:08.373581 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename_intermediate.sql
[0m04:50:08.374625 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create_backup.sql
[0m04:50:08.375495 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_database.sql
[0m04:50:08.376408 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\last_day.sql
[0m04:50:08.377658 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\escape_single_quotes.sql
[0m04:50:08.378618 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot.sql
[0m04:50:08.379563 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck_table.sql
[0m04:50:08.381448 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\apply_grants.sql
[0m04:50:08.382817 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\drop.sql
[0m04:50:08.383845 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\right.sql
[0m04:50:08.385347 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\columns.sql
[0m04:50:08.386526 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\accepted_values.sql
[0m04:50:08.387591 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\rename.sql
[0m04:50:08.388740 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\rename.sql
[0m04:50:08.389807 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\replace.sql
[0m04:50:08.390961 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_pattern_sql.sql
[0m04:50:08.392055 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_null_proportion.sql
[0m04:50:08.393183 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\datediff.sql
[0m04:50:08.394275 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\configs.sql
[0m04:50:08.395416 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\alter.sql
[0m04:50:08.398335 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\create.sql
[0m04:50:08.399494 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\equals.sql
[0m04:50:08.400502 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_prefix_sql.sql
[0m04:50:08.401858 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\adapters.sql
[0m04:50:08.402877 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\replace.sql
[0m04:50:08.403904 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\safe_cast.sql
[0m04:50:08.405130 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\persist_docs.sql
[0m04:50:08.406199 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_pattern.sql
[0m04:50:08.407179 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\array_construct.sql
[0m04:50:08.408604 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\on_schema_change.sql
[0m04:50:08.409806 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_table_types_sql.sql
[0m04:50:08.410948 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\bool_or.sql
[0m04:50:08.413093 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\replace.sql
[0m04:50:08.414296 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\schema.sql
[0m04:50:08.415253 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\dateadd.sql
[0m04:50:08.416200 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename.sql
[0m04:50:08.417183 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_events.sql
[0m04:50:08.418489 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\helpers.sql
[0m04:50:08.419543 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_query_results_as_dict.sql
[0m04:50:08.420616 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create.sql
[0m04:50:08.421624 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\freshness.sql
[0m04:50:08.422665 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\expression_is_true.sql
[0m04:50:08.423653 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\drop.sql
[0m04:50:08.424786 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\indexes.sql
[0m04:50:08.425740 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_ephemeral.sql
[0m04:50:08.426733 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_intermediate.sql
[0m04:50:08.428720 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\seed.sql
[0m04:50:08.430019 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_constant.sql
[0m04:50:08.431107 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\cardinality_equality.sql
[0m04:50:08.432203 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\dbt_test_health.sql
[0m04:50:08.433255 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\metadata.sql
[0m04:50:08.434231 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_parameter.sql
[0m04:50:08.435070 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\pipeline_runtime_health.sql
[0m04:50:08.436029 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\show.sql
[0m04:50:08.437030 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\timestamps.sql
[0m04:50:08.438008 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\unit_test_sql\get_fixture_sql.sql
[0m04:50:08.438873 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\escape_single_quotes.sql
[0m04:50:08.439788 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\intersect.sql
[0m04:50:08.440809 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_log_format.sql
[0m04:50:08.441836 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\where_subquery.sql
[0m04:50:08.442854 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\python_model\python.sql
[0m04:50:08.444683 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_single_value.sql
[0m04:50:08.445812 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_add.sql
[0m04:50:08.446839 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\helpers.sql
[0m04:50:08.447754 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\can_clone_table.sql
[0m04:50:08.448645 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\pivot.sql
[0m04:50:08.449569 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\surrogate_key.sql
[0m04:50:08.450550 [debug] [MainThread]: Partial parsing: deleted file: dbt://docs\overview.md
[0m04:50:08.451382 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\groupby.sql
[0m04:50:08.472131 [error] [MainThread]: Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[0m04:50:08.474169 [error] [MainThread]: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'

[0m04:50:08.476832 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 3.1509888, "process_in_blocks": "0", "process_kernel_time": 0.638936, "process_mem_max_rss": "297144", "process_out_blocks": "0", "process_user_time": 2.997424}
[0m04:50:08.478337 [debug] [MainThread]: Command `cli run` failed at 04:50:08.478199 after 3.15 seconds
[0m04:50:08.479510 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a0280>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81fb98d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81de9bd0>]}
[0m04:50:08.480618 [debug] [MainThread]: Flushing usage events
[0m04:50:09.421396 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m14:53:47.013723 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532B1D8C90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532B1D9410>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532B1D8590>]}
[0m14:53:47.013723 [debug] [MainThread]: An error was encountered while trying to send an event


============================== 14:53:47.013723 | 38b832d6-a7d7-4c6c-ae46-f12ea01605bb ==============================
[0m14:53:47.013723 [info ] [MainThread]: Running with dbt=1.9.6
[0m14:53:47.023727 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live', 'version_check': 'True', 'fail_fast': 'False', 'log_path': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'static_parser': 'True', 'invocation_command': 'dbt run --models staging', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m14:53:48.552174 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532AB868D0>]}
[0m14:53:48.552174 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:48.666584 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532A14AA90>]}
[0m14:53:48.666584 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:48.666584 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m14:53:49.320109 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m14:53:49.617074 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m14:53:49.617074 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m14:53:49.730245 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m14:53:49.802952 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532E4F6610>]}
[0m14:53:49.802952 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:49.954926 [debug] [MainThread]: Wrote artifact WritableManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\manifest.json
[0m14:53:49.954926 [debug] [MainThread]: Wrote artifact SemanticManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\semantic_manifest.json
[0m14:53:50.138531 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532F9A0390>]}
[0m14:53:50.138531 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:50.138531 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m14:53:50.138531 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532F8246D0>]}
[0m14:53:50.138531 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:50.138531 [info ] [MainThread]: 
[0m14:53:50.138531 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m14:53:50.138531 [info ] [MainThread]: 
[0m14:53:50.147460 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m14:53:50.147460 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m14:53:50.913716 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m14:53:50.913716 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m14:53:50.913716 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m14:53:52.574227 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 1.663 seconds
[0m14:53:52.580414 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m14:53:52.580414 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_dbt_test__audit'
[0m14:53:52.597111 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m14:53:52.597111 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m14:53:52.597111 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m14:53:52.597111 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m14:53:52.607762 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m14:53:52.607762 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m14:53:52.607762 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m14:53:52.607762 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m14:53:52.613263 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m14:53:52.615262 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m14:53:53.383437 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.775 seconds
[0m14:53:53.383437 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m14:53:53.397085 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m14:53:53.538170 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.923 seconds
[0m14:53:53.540171 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.929 seconds
[0m14:53:53.542201 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m14:53:53.544200 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m14:53:53.544200 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m14:53:53.545171 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m14:53:53.559608 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.162 seconds
[0m14:53:53.565062 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m14:53:53.566092 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m14:53:53.613924 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.056 seconds
[0m14:53:53.640488 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.097 seconds
[0m14:53:53.640488 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m14:53:53.640488 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m14:53:53.661488 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.115 seconds
[0m14:53:53.665004 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m14:53:53.666047 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m14:53:53.715231 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.072 seconds
[0m14:53:53.725261 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.062 seconds
[0m14:53:53.730955 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000015328065A10>]}
[0m14:53:53.730955 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:53.807179 [debug] [Thread-1 (]: Began running node model.live_c360.stg_events
[0m14:53:53.807179 [debug] [Thread-2 (]: Began running node model.live_c360.stg_orders
[0m14:53:53.807179 [debug] [Thread-3 (]: Began running node model.live_c360.stg_users
[0m14:53:53.807179 [info ] [Thread-1 (]: 1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m14:53:53.814192 [info ] [Thread-2 (]: 2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m14:53:53.814192 [debug] [Thread-1 (]: Acquiring new snowflake connection 'model.live_c360.stg_events'
[0m14:53:53.814192 [info ] [Thread-3 (]: 3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m14:53:53.814192 [debug] [Thread-2 (]: Acquiring new snowflake connection 'model.live_c360.stg_orders'
[0m14:53:53.814192 [debug] [Thread-1 (]: Began compiling node model.live_c360.stg_events
[0m14:53:53.814192 [debug] [Thread-3 (]: Acquiring new snowflake connection 'model.live_c360.stg_users'
[0m14:53:53.814192 [debug] [Thread-2 (]: Began compiling node model.live_c360.stg_orders
[0m14:53:53.837583 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m14:53:53.838614 [debug] [Thread-3 (]: Began compiling node model.live_c360.stg_users
[0m14:53:53.840641 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m14:53:53.847784 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m14:53:53.847784 [debug] [Thread-1 (]: Began executing node model.live_c360.stg_events
[0m14:53:53.873573 [debug] [Thread-2 (]: Began executing node model.live_c360.stg_orders
[0m14:53:53.873573 [debug] [Thread-3 (]: Began executing node model.live_c360.stg_users
[0m14:53:53.924899 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m14:53:53.924899 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m14:53:53.933195 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m14:53:53.933195 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.stg_users"
[0m14:53:53.943194 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.stg_events"
[0m14:53:53.949578 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m14:53:53.950578 [debug] [Thread-3 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m14:53:53.951581 [debug] [Thread-1 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m14:53:53.952580 [debug] [Thread-2 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m14:53:53.953581 [debug] [Thread-3 (]: Opening a new connection, currently in state init
[0m14:53:53.954583 [debug] [Thread-1 (]: Opening a new connection, currently in state init
[0m14:53:53.955579 [debug] [Thread-2 (]: Opening a new connection, currently in state init
[0m14:53:54.988349 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccc05-3204-7ffc-0002-4ad6000660c2
[0m14:53:54.988871 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccc05-3204-7ffc-0002-4ad6000660be
[0m14:53:54.989907 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccc05-3204-802a-0002-4ad60006903a
[0m14:53:54.990429 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m14:53:54.991491 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m14:53:54.992002 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m14:53:54.996520 [debug] [Thread-1 (]: Database Error in model stg_events (models\staging\stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_events.sql
[0m14:53:55.006528 [debug] [Thread-3 (]: Database Error in model stg_users (models\staging\stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_users.sql
[0m14:53:55.006528 [debug] [Thread-2 (]: Database Error in model stg_orders (models\staging\stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_orders.sql
[0m14:53:55.006528 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001533014CC90>]}
[0m14:53:55.006528 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001533010D1D0>]}
[0m14:53:55.006528 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '38b832d6-a7d7-4c6c-ae46-f12ea01605bb', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000015330099790>]}
[0m14:53:55.006528 [debug] [Thread-1 (]: An error was encountered while trying to send an event
[0m14:53:55.006528 [debug] [Thread-3 (]: An error was encountered while trying to send an event
[0m14:53:55.016529 [debug] [Thread-2 (]: An error was encountered while trying to send an event
[0m14:53:55.016529 [error] [Thread-1 (]: 1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 1.19s]
[0m14:53:55.016529 [error] [Thread-3 (]: 3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 1.19s]
[0m14:53:55.016529 [debug] [Thread-1 (]: Finished running node model.live_c360.stg_events
[0m14:53:55.016529 [error] [Thread-2 (]: 2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 1.19s]
[0m14:53:55.016529 [debug] [Thread-3 (]: Finished running node model.live_c360.stg_users
[0m14:53:55.016529 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models\staging\stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_events.sql.
[0m14:53:55.016529 [debug] [Thread-2 (]: Finished running node model.live_c360.stg_orders
[0m14:53:55.016529 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models\staging\stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_users.sql.
[0m14:53:55.026529 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models\staging\stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_orders.sql.
[0m14:53:55.028783 [debug] [MainThread]: Connection 'master' was properly closed.
[0m14:53:55.030967 [debug] [MainThread]: Connection 'list_MYDB' was left open.
[0m14:53:55.030967 [debug] [MainThread]: On list_MYDB: Close
[0m14:53:55.117393 [debug] [MainThread]: Connection 'list_MYDB_LIVE_DATA' was left open.
[0m14:53:55.117393 [debug] [MainThread]: On list_MYDB_LIVE_DATA: Close
[0m14:53:55.247237 [debug] [MainThread]: Connection 'list_MYDB_LIVE_DATA_dbt_test__audit' was left open.
[0m14:53:55.247237 [debug] [MainThread]: On list_MYDB_LIVE_DATA_dbt_test__audit: Close
[0m14:53:55.323945 [debug] [MainThread]: Connection 'list_MYDB_LIVE_DATA_ANALYTICS' was left open.
[0m14:53:55.323945 [debug] [MainThread]: On list_MYDB_LIVE_DATA_ANALYTICS: Close
[0m14:53:55.700558 [debug] [MainThread]: Connection 'model.live_c360.stg_events' was left open.
[0m14:53:55.710561 [debug] [MainThread]: On model.live_c360.stg_events: Close
[0m14:53:55.784421 [debug] [MainThread]: Connection 'model.live_c360.stg_orders' was left open.
[0m14:53:55.784952 [debug] [MainThread]: On model.live_c360.stg_orders: Close
[0m14:53:55.865433 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m14:53:55.865433 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m14:53:56.130889 [info ] [MainThread]: 
[0m14:53:56.130889 [info ] [MainThread]: Finished running 3 view models in 0 hours 0 minutes and 5.98 seconds (5.98s).
[0m14:53:56.130889 [debug] [MainThread]: Command end result
[0m14:53:56.180316 [debug] [MainThread]: Wrote artifact WritableManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\manifest.json
[0m14:53:56.180316 [debug] [MainThread]: Wrote artifact SemanticManifest to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\semantic_manifest.json
[0m14:53:56.197787 [debug] [MainThread]: Wrote artifact RunExecutionResult to G:\github\S3 dbt-snowflake c360\experiment2\dbt_live\target\run_results.json
[0m14:53:56.197787 [info ] [MainThread]: 
[0m14:53:56.197787 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m14:53:56.197787 [info ] [MainThread]: 
[0m14:53:56.197787 [error] [MainThread]:   Database Error in model stg_events (models\staging\stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_events.sql
[0m14:53:56.197787 [info ] [MainThread]: 
[0m14:53:56.197787 [error] [MainThread]:   Database Error in model stg_users (models\staging\stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_users.sql
[0m14:53:56.197787 [info ] [MainThread]: 
[0m14:53:56.206979 [error] [MainThread]:   Database Error in model stg_orders (models\staging\stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target\run\live_c360\models\staging\stg_orders.sql
[0m14:53:56.207978 [info ] [MainThread]: 
[0m14:53:56.208975 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3
[0m14:53:56.211865 [debug] [MainThread]: Command `dbt run` failed at 14:53:56.211865 after 9.34 seconds
[0m14:53:56.213940 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000153249E3410>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532FF805D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001532AF1AA50>]}
[0m14:53:56.214938 [debug] [MainThread]: An error was encountered while trying to send an event
[0m14:53:56.215938 [debug] [MainThread]: Flushing usage events
[0m04:55:10.365590 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c310>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca0d60>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca2440>]}


============================== 04:55:10.374107 | d1e79364-aa9c-4c55-bfdf-64ec125b8d8a ==============================
[0m04:55:10.374107 [info ] [MainThread]: Running with dbt=1.9.6
[0m04:55:10.379911 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m04:55:11.333811 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'd1e79364-aa9c-4c55-bfdf-64ec125b8d8a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d081240>]}
[0m04:55:11.408254 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'd1e79364-aa9c-4c55-bfdf-64ec125b8d8a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81deaad0>]}
[0m04:55:11.410313 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m04:55:11.778157 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m04:55:12.822979 [debug] [MainThread]: Partial parsing enabled: 208 files deleted, 208 files added, 0 files changed.
[0m04:55:12.824221 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/equals.sql
[0m04:55:12.825283 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/relationships.sql
[0m04:55:12.826330 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_filtered_columns_in_relation.sql
[0m04:55:12.827383 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/datediff.sql
[0m04:55:12.828515 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/not_null.sql
[0m04:55:12.831145 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/drop.sql
[0m04:55:12.832383 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_constant.sql
[0m04:55:12.833434 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/columns.sql
[0m04:55:12.834431 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast_bool_to_text.sql
[0m04:55:12.835592 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/accepted_values.sql
[0m04:55:12.836609 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/indexes.sql
[0m04:55:12.837728 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/haversine_distance.sql
[0m04:55:12.838761 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/relationships_where.sql
[0m04:55:12.839794 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/refresh.sql
[0m04:55:12.841265 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename_intermediate.sql
[0m04:55:12.842473 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/any_value.sql
[0m04:55:12.843605 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop.sql
[0m04:55:12.846450 [debug] [MainThread]: Partial parsing: added file: live_c360://models/sources.yml
[0m04:55:12.847989 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/accepted_range.sql
[0m04:55:12.849204 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/escape_single_quotes.sql
[0m04:55:12.850950 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/groupby.sql
[0m04:55:12.852422 [debug] [MainThread]: Partial parsing: added file: dbt://macros/python_model/python.sql
[0m04:55:12.853706 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/describe.sql
[0m04:55:12.854875 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental.sql
[0m04:55:12.855917 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename.sql
[0m04:55:12.856977 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_schema.sql
[0m04:55:12.858167 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/table.sql
[0m04:55:12.859425 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/fact_orders.sql
[0m04:55:12.862187 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/generate_series.sql
[0m04:55:12.863820 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_concat.sql
[0m04:55:12.864975 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/create.sql
[0m04:55:12.866112 [debug] [MainThread]: Partial parsing: added file: dbt://docs/overview.md
[0m04:55:12.867221 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/alter.sql
[0m04:55:12.868396 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_database.sql
[0m04:55:12.869489 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_accepted_values.sql
[0m04:55:12.870538 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_parameter.sql
[0m04:55:12.871519 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/bool_or.sql
[0m04:55:12.872608 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/metadata.sql
[0m04:55:12.873703 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_alias.sql
[0m04:55:12.875045 [debug] [MainThread]: Partial parsing: added file: dbt://macros/unit_test_sql/get_fixture_sql.sql
[0m04:55:12.877748 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/apply_grants.sql
[0m04:55:12.879401 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/position.sql
[0m04:55:12.880825 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_column_values.sql
[0m04:55:12.882241 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_orders.sql
[0m04:55:12.883685 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/rename.sql
[0m04:55:12.885275 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/rename.sql
[0m04:55:12.886674 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/schema.sql
[0m04:55:12.888136 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/timestamps.sql
[0m04:55:12.889650 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/helpers.sql
[0m04:55:12.893396 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_backup.sql
[0m04:55:12.894955 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/dateadd.sql
[0m04:55:12.896285 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/view.sql
[0m04:55:12.898051 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/sequential_values.sql
[0m04:55:12.899500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/drop.sql
[0m04:55:12.900911 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/create.sql
[0m04:55:12.902299 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata/list_relations_without_caching.sql
[0m04:55:12.903729 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/dbt_test_health.sql
[0m04:55:12.905110 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/strategies.sql
[0m04:55:12.908077 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/datetime.sql
[0m04:55:12.909542 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/escape_single_quotes.sql
[0m04:55:12.910952 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/column_helpers.sql
[0m04:55:12.912509 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/is_incremental.sql
[0m04:55:12.914175 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equal_rowcount.sql
[0m04:55:12.915820 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/right.sql
[0m04:55:12.917253 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_construct.sql
[0m04:55:12.918577 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/schema.sql
[0m04:55:12.919967 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/drop.sql
[0m04:55:12.921410 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/hooks.sql
[0m04:55:12.924430 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create.sql
[0m04:55:12.925659 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/query_history_health.sql
[0m04:55:12.926909 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/clone.sql
[0m04:55:12.928291 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/test.sql
[0m04:55:12.929547 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/fewer_rows_than.sql
[0m04:55:12.930851 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/last_day.sql
[0m04:55:12.932162 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast.sql
[0m04:55:12.933488 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/test.sql
[0m04:55:12.934758 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_subtract.sql
[0m04:55:12.935901 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date.sql
[0m04:55:12.938207 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/timestamps.sql
[0m04:55:12.939838 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/unit.sql
[0m04:55:12.941093 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users_scd2.sql
[0m04:55:12.942195 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/create.sql
[0m04:55:12.943267 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_pattern.sql
[0m04:55:12.944342 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/slugify.sql
[0m04:55:12.945420 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/replace.sql
[0m04:55:12.946566 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/replace.sql
[0m04:55:12.947754 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_single_value.sql
[0m04:55:12.948991 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/bool_or.sql
[0m04:55:12.950465 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/merge.sql
[0m04:55:12.951719 [debug] [MainThread]: Partial parsing: added file: dbt://tests/generic/builtin.sql
[0m04:55:12.954390 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create_backup.sql
[0m04:55:12.955582 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/replace.sql
[0m04:55:12.956521 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/rename.sql
[0m04:55:12.957441 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/dynamic_table.sql
[0m04:55:12.958429 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/right.sql
[0m04:55:12.959507 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/materialized_view.sql
[0m04:55:12.960454 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/can_clone_table.sql
[0m04:55:12.961468 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/expression_is_true.sql
[0m04:55:12.962439 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/statement.sql
[0m04:55:12.963433 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/replace.sql
[0m04:55:12.964369 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/replace.sql
[0m04:55:12.965364 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/where_subquery.sql
[0m04:55:12.966364 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/date_spine.sql
[0m04:55:12.967526 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_null_proportion.sql
[0m04:55:12.969745 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/clone.sql
[0m04:55:12.971049 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/listagg.sql
[0m04:55:12.972164 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/data_types.sql
[0m04:55:12.973331 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/catalog.sql
[0m04:55:12.974519 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/create.sql
[0m04:55:12.976021 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/column/columns_spec_ddl.sql
[0m04:55:12.977298 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/apply_grants.sql
[0m04:55:12.978548 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/cardinality_equality.sql
[0m04:55:12.979704 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/unique.sql
[0m04:55:12.980770 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_divide.sql
[0m04:55:12.981836 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename.sql
[0m04:55:12.984516 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/length.sql
[0m04:55:12.986209 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/at_least_one.sql
[0m04:55:12.987531 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/helpers.sql
[0m04:55:12.988735 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/on_schema_change.sql
[0m04:55:12.989957 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/pipeline_runtime_health.sql
[0m04:55:12.991164 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/freshness.sql
[0m04:55:12.992406 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/data_quality_health.sql
[0m04:55:12.993783 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/create_or_replace_clone.sql
[0m04:55:12.995164 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/literal.sql
[0m04:55:12.996468 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename_intermediate.sql
[0m04:55:12.997696 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_trunc.sql
[0m04:55:13.000316 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/strategies.sql
[0m04:55:13.001645 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/cast.sql
[0m04:55:13.003067 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot.sql
[0m04:55:13.004397 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/show.sql
[0m04:55:13.005522 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck.sql
[0m04:55:13.006653 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/union.sql
[0m04:55:13.007888 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/mutually_exclusive_ranges.sql
[0m04:55:13.009067 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/insert_overwrite.sql
[0m04:55:13.010147 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/split_part.sql
[0m04:55:13.011336 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/except.sql
[0m04:55:13.012462 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_query_results_as_dict.sql
[0m04:55:13.013493 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equality.sql
[0m04:55:13.015865 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/configs.sql
[0m04:55:13.017272 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/deduplicate.sql
[0m04:55:13.018484 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_path.sql
[0m04:55:13.019667 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users.sql
[0m04:55:13.020809 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/safe_cast.sql
[0m04:55:13.021927 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/log_info.sql
[0m04:55:13.022946 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_events.sql
[0m04:55:13.024001 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/array_construct.sql
[0m04:55:13.025018 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/concat.sql
[0m04:55:13.026035 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_prefix.sql
[0m04:55:13.027041 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/relation.sql
[0m04:55:13.028026 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot_merge.sql
[0m04:55:13.030819 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/hash.sql
[0m04:55:13.032332 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/drop.sql
[0m04:55:13.033546 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/incremental.sql
[0m04:55:13.034795 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/table.sql
[0m04:55:13.035935 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/helpers.sql
[0m04:55:13.037075 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/rename.sql
[0m04:55:13.038478 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/pivot.sql
[0m04:55:13.039774 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/etl_health_dashboard.sql
[0m04:55:13.040990 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/intersect.sql
[0m04:55:13.042152 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_empty_string.sql
[0m04:55:13.043307 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/replace.sql
[0m04:55:13.044478 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/validate_sql.sql
[0m04:55:13.047015 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_ephemeral.sql
[0m04:55:13.048574 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/unique_combination_of_columns.sql
[0m04:55:13.049756 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/adapters.sql
[0m04:55:13.050804 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_relation.sql
[0m04:55:13.051809 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/replace.sql
[0m04:55:13.052830 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_log_format.sql
[0m04:55:13.053775 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/width_bucket.sql
[0m04:55:13.054815 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_users.sql
[0m04:55:13.055882 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/drop.sql
[0m04:55:13.057301 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/snapshot.sql
[0m04:55:13.059317 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/refresh.sql
[0m04:55:13.063258 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_pattern_sql.sql
[0m04:55:13.065535 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/seed.sql
[0m04:55:13.067297 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/create.sql
[0m04:55:13.068985 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata.sql
[0m04:55:13.070985 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/alter.sql
[0m04:55:13.073085 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_prefix_sql.sql
[0m04:55:13.074789 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/mv_fact_orders.sql
[0m04:55:13.077737 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/drop.sql
[0m04:55:13.079014 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/replace.sql
[0m04:55:13.080281 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/replace.sql
[0m04:55:13.081592 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_intermediate.sql
[0m04:55:13.082888 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create.sql
[0m04:55:13.084129 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_host.sql
[0m04:55:13.085340 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop_backup.sql
[0m04:55:13.086502 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/recency.sql
[0m04:55:13.087720 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/surrogate_key.sql
[0m04:55:13.088936 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/unpivot.sql
[0m04:55:13.090124 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/rename.sql
[0m04:55:13.092698 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_time.sql
[0m04:55:13.093973 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/optional.sql
[0m04:55:13.095218 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/persist_docs.sql
[0m04:55:13.096425 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_series.sql
[0m04:55:13.097592 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/drop.sql
[0m04:55:13.098907 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_add.sql
[0m04:55:13.100299 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_table_types_sql.sql
[0m04:55:13.101526 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/merge.sql
[0m04:55:13.102734 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/star.sql
[0m04:55:13.103902 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_spine.sql
[0m04:55:13.105050 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/safe_cast.sql
[0m04:55:13.106199 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck_table.sql
[0m04:55:13.108746 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_surrogate_key.sql
[0m04:55:13.110093 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/view.sql
[0m04:55:13.111297 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/create.sql
[0m04:55:13.112450 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/seed.sql
[0m04:55:13.113595 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_append.sql
[0m04:55:13.115249 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\relation.sql
[0m04:55:13.116410 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\replace.sql
[0m04:55:13.117514 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_prefix.sql
[0m04:55:13.118651 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\rename.sql
[0m04:55:13.121002 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\clone.sql
[0m04:55:13.124335 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\cast.sql
[0m04:55:13.125548 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\is_incremental.sql
[0m04:55:13.127054 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\apply_grants.sql
[0m04:55:13.128085 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_surrogate_key.sql
[0m04:55:13.129175 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\date_spine.sql
[0m04:55:13.130443 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\etl_health_dashboard.sql
[0m04:55:13.131770 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\rename.sql
[0m04:55:13.132984 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\clone.sql
[0m04:55:13.134105 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\incremental.sql
[0m04:55:13.135166 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\unpivot.sql
[0m04:55:13.136227 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_construct.sql
[0m04:55:13.138774 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck.sql
[0m04:55:13.140210 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\describe.sql
[0m04:55:13.141419 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\unit.sql
[0m04:55:13.142617 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\deduplicate.sql
[0m04:55:13.143831 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot_merge.sql
[0m04:55:13.145017 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\refresh.sql
[0m04:55:13.146231 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\drop.sql
[0m04:55:13.147477 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\log_info.sql
[0m04:55:13.148936 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\create.sql
[0m04:55:13.150237 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\schema.sql
[0m04:55:13.151474 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_divide.sql
[0m04:55:13.154271 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop.sql
[0m04:55:13.155538 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\optional.sql
[0m04:55:13.156850 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\create.sql
[0m04:55:13.158104 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\data_types.sql
[0m04:55:13.159270 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_time.sql
[0m04:55:13.160463 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\test.sql
[0m04:55:13.161727 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental.sql
[0m04:55:13.163135 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\unique.sql
[0m04:55:13.164344 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\at_least_one.sql
[0m04:55:13.165474 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_backup.sql
[0m04:55:13.166599 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\table.sql
[0m04:55:13.169814 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\statement.sql
[0m04:55:13.171398 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equal_rowcount.sql
[0m04:55:13.172685 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\relationships.sql
[0m04:55:13.173933 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\merge.sql
[0m04:55:13.175052 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\not_null.sql
[0m04:55:13.176146 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_append.sql
[0m04:55:13.177235 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\length.sql
[0m04:55:13.178364 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\query_history_health.sql
[0m04:55:13.179698 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\create.sql
[0m04:55:13.180824 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\replace.sql
[0m04:55:13.181936 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_orders.sql
[0m04:55:13.184349 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\insert_overwrite.sql
[0m04:55:13.186013 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_schema.sql
[0m04:55:13.187222 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_concat.sql
[0m04:55:13.188404 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\hash.sql
[0m04:55:13.189565 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users.sql
[0m04:55:13.190699 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\helpers.sql
[0m04:55:13.191922 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\replace.sql
[0m04:55:13.193067 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\listagg.sql
[0m04:55:13.194315 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\catalog.sql
[0m04:55:13.195570 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\replace.sql
[0m04:55:13.196940 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create.sql
[0m04:55:13.197989 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\concat.sql
[0m04:55:13.200771 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\safe_cast.sql
[0m04:55:13.202342 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\fewer_rows_than.sql
[0m04:55:13.203589 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_series.sql
[0m04:55:13.205225 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\column\columns_spec_ddl.sql
[0m04:55:13.206451 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\literal.sql
[0m04:55:13.207571 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\validate_sql.sql
[0m04:55:13.208720 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata\list_relations_without_caching.sql
[0m04:55:13.209784 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\any_value.sql
[0m04:55:13.210810 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_subtract.sql
[0m04:55:13.211926 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\mv_fact_orders.sql
[0m04:55:13.213124 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\drop.sql
[0m04:55:13.216424 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\bool_or.sql
[0m04:55:13.217744 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\strategies.sql
[0m04:55:13.218998 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\drop.sql
[0m04:55:13.220245 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_trunc.sql
[0m04:55:13.221420 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\position.sql
[0m04:55:13.222561 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_relation.sql
[0m04:55:13.223772 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\hooks.sql
[0m04:55:13.224986 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\materialized_view.sql
[0m04:55:13.226253 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\relationships_where.sql
[0m04:55:13.227412 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\test.sql
[0m04:55:13.228563 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\accepted_range.sql
[0m04:55:13.231169 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\slugify.sql
[0m04:55:13.232458 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_spine.sql
[0m04:55:13.233543 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equality.sql
[0m04:55:13.234628 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_host.sql
[0m04:55:13.235933 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_empty_string.sql
[0m04:55:13.237211 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\create.sql
[0m04:55:13.238387 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\replace.sql
[0m04:55:13.239576 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename.sql
[0m04:55:13.240682 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date.sql
[0m04:55:13.241839 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata.sql
[0m04:55:13.243008 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\seed.sql
[0m04:55:13.244051 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\dynamic_table.sql
[0m04:55:13.246681 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\rename.sql
[0m04:55:13.248128 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast.sql
[0m04:55:13.249325 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename_intermediate.sql
[0m04:55:13.250504 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\create_or_replace_clone.sql
[0m04:55:13.251654 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast_bool_to_text.sql
[0m04:55:13.252868 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\create.sql
[0m04:55:13.253873 [debug] [MainThread]: Partial parsing: deleted file: dbt://tests\generic\builtin.sql
[0m04:55:13.254807 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\recency.sql
[0m04:55:13.255836 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_column_values.sql
[0m04:55:13.257235 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\refresh.sql
[0m04:55:13.258252 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\split_part.sql
[0m04:55:13.259151 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\star.sql
[0m04:55:13.261412 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\generate_series.sql
[0m04:55:13.262629 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\view.sql
[0m04:55:13.263645 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\timestamps.sql
[0m04:55:13.264589 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\column_helpers.sql
[0m04:55:13.265550 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\drop.sql
[0m04:55:13.266487 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\drop.sql
[0m04:55:13.267745 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_filtered_columns_in_relation.sql
[0m04:55:13.268763 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\fact_orders.sql
[0m04:55:13.269799 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\replace.sql
[0m04:55:13.270571 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_accepted_values.sql
[0m04:55:13.271436 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\merge.sql
[0m04:55:13.272206 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\haversine_distance.sql
[0m04:55:13.272959 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users_scd2.sql
[0m04:55:13.273723 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\snapshot.sql
[0m04:55:13.274479 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\view.sql
[0m04:55:13.276845 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\table.sql
[0m04:55:13.277837 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\data_quality_health.sql
[0m04:55:13.278871 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_users.sql
[0m04:55:13.279829 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\alter.sql
[0m04:55:13.280734 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_alias.sql
[0m04:55:13.281692 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\unique_combination_of_columns.sql
[0m04:55:13.282760 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\union.sql
[0m04:55:13.283799 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\strategies.sql
[0m04:55:13.284740 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\sequential_values.sql
[0m04:55:13.285680 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\width_bucket.sql
[0m04:55:13.286578 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\mutually_exclusive_ranges.sql
[0m04:55:13.287724 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\datetime.sql
[0m04:55:13.288859 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop_backup.sql
[0m04:55:13.289715 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\right.sql
[0m04:55:13.290730 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_path.sql
[0m04:55:13.293662 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\except.sql
[0m04:55:13.294840 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename_intermediate.sql
[0m04:55:13.295971 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create_backup.sql
[0m04:55:13.297677 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_database.sql
[0m04:55:13.298924 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\last_day.sql
[0m04:55:13.300035 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\escape_single_quotes.sql
[0m04:55:13.301008 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot.sql
[0m04:55:13.302075 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck_table.sql
[0m04:55:13.303173 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\apply_grants.sql
[0m04:55:13.304270 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\drop.sql
[0m04:55:13.305459 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\right.sql
[0m04:55:13.308464 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\columns.sql
[0m04:55:13.309784 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\accepted_values.sql
[0m04:55:13.310954 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\rename.sql
[0m04:55:13.312191 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\rename.sql
[0m04:55:13.313347 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\replace.sql
[0m04:55:13.314924 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_pattern_sql.sql
[0m04:55:13.316157 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_null_proportion.sql
[0m04:55:13.317333 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\datediff.sql
[0m04:55:13.318574 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\configs.sql
[0m04:55:13.319758 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\alter.sql
[0m04:55:13.320952 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\create.sql
[0m04:55:13.323759 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\equals.sql
[0m04:55:13.324980 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_prefix_sql.sql
[0m04:55:13.326368 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\adapters.sql
[0m04:55:13.328023 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\replace.sql
[0m04:55:13.329587 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\safe_cast.sql
[0m04:55:13.330913 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\persist_docs.sql
[0m04:55:13.332051 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_pattern.sql
[0m04:55:13.333191 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\array_construct.sql
[0m04:55:13.334341 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\on_schema_change.sql
[0m04:55:13.335509 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_table_types_sql.sql
[0m04:55:13.336661 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\bool_or.sql
[0m04:55:13.339383 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\replace.sql
[0m04:55:13.341000 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\schema.sql
[0m04:55:13.342163 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\dateadd.sql
[0m04:55:13.343318 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename.sql
[0m04:55:13.344437 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_events.sql
[0m04:55:13.345663 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\helpers.sql
[0m04:55:13.346812 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_query_results_as_dict.sql
[0m04:55:13.347952 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create.sql
[0m04:55:13.349027 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\freshness.sql
[0m04:55:13.350265 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\expression_is_true.sql
[0m04:55:13.351452 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\drop.sql
[0m04:55:13.354609 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\indexes.sql
[0m04:55:13.356017 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_ephemeral.sql
[0m04:55:13.357205 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_intermediate.sql
[0m04:55:13.358458 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\seed.sql
[0m04:55:13.359555 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_constant.sql
[0m04:55:13.360709 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\cardinality_equality.sql
[0m04:55:13.361949 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\dbt_test_health.sql
[0m04:55:13.363144 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\metadata.sql
[0m04:55:13.364375 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_parameter.sql
[0m04:55:13.365523 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\pipeline_runtime_health.sql
[0m04:55:13.366864 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\show.sql
[0m04:55:13.371092 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\timestamps.sql
[0m04:55:13.372541 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\unit_test_sql\get_fixture_sql.sql
[0m04:55:13.373817 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\escape_single_quotes.sql
[0m04:55:13.375025 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\intersect.sql
[0m04:55:13.376146 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_log_format.sql
[0m04:55:13.377370 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\where_subquery.sql
[0m04:55:13.378663 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\python_model\python.sql
[0m04:55:13.379880 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_single_value.sql
[0m04:55:13.381231 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_add.sql
[0m04:55:13.382395 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\helpers.sql
[0m04:55:13.385113 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\can_clone_table.sql
[0m04:55:13.386264 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\pivot.sql
[0m04:55:13.387431 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\surrogate_key.sql
[0m04:55:13.388454 [debug] [MainThread]: Partial parsing: deleted file: dbt://docs\overview.md
[0m04:55:13.389580 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\groupby.sql
[0m04:55:13.410809 [error] [MainThread]: Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[0m04:55:13.412820 [error] [MainThread]: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'

[0m04:55:13.416239 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 3.1375446, "process_in_blocks": "0", "process_kernel_time": 0.698381, "process_mem_max_rss": "297140", "process_out_blocks": "0", "process_user_time": 3.470081}
[0m04:55:13.417631 [debug] [MainThread]: Command `cli run` failed at 04:55:13.417512 after 3.14 seconds
[0m04:55:13.418850 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d89c310>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81da1090>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81fb4730>]}
[0m04:55:13.420008 [debug] [MainThread]: Flushing usage events
[0m04:55:14.349513 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m05:00:16.776588 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a4160>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8ccaa1d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cca9ed0>]}


============================== 05:00:16.784792 | a91e4697-84f4-4200-afce-ba1093374c7b ==============================
[0m05:00:16.784792 [info ] [MainThread]: Running with dbt=1.9.6
[0m05:00:16.786719 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt celery worker', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m05:00:18.552638 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'a91e4697-84f4-4200-afce-ba1093374c7b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8e3a3f10>]}
[0m05:00:18.635267 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'a91e4697-84f4-4200-afce-ba1093374c7b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8cf8b3d0>]}
[0m05:00:18.639106 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m05:00:20.391886 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m05:00:21.450083 [debug] [MainThread]: Partial parsing enabled: 208 files deleted, 208 files added, 0 files changed.
[0m05:00:21.451836 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/equals.sql
[0m05:00:21.453063 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/relationships.sql
[0m05:00:21.454006 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_filtered_columns_in_relation.sql
[0m05:00:21.455088 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/datediff.sql
[0m05:00:21.456199 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/not_null.sql
[0m05:00:21.457201 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/drop.sql
[0m05:00:21.458156 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_constant.sql
[0m05:00:21.460760 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/columns.sql
[0m05:00:21.461941 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast_bool_to_text.sql
[0m05:00:21.463231 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/accepted_values.sql
[0m05:00:21.464611 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/indexes.sql
[0m05:00:21.465764 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/haversine_distance.sql
[0m05:00:21.466889 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/relationships_where.sql
[0m05:00:21.468008 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/refresh.sql
[0m05:00:21.469141 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename_intermediate.sql
[0m05:00:21.470234 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/any_value.sql
[0m05:00:21.471319 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop.sql
[0m05:00:21.472883 [debug] [MainThread]: Partial parsing: added file: live_c360://models/sources.yml
[0m05:00:21.474115 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/accepted_range.sql
[0m05:00:21.476428 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/escape_single_quotes.sql
[0m05:00:21.477862 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/groupby.sql
[0m05:00:21.478947 [debug] [MainThread]: Partial parsing: added file: dbt://macros/python_model/python.sql
[0m05:00:21.480038 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/describe.sql
[0m05:00:21.481062 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental.sql
[0m05:00:21.482073 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/rename.sql
[0m05:00:21.483252 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_schema.sql
[0m05:00:21.484414 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/table.sql
[0m05:00:21.485537 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/fact_orders.sql
[0m05:00:21.486946 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/generate_series.sql
[0m05:00:21.487951 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_concat.sql
[0m05:00:21.488908 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/create.sql
[0m05:00:21.491156 [debug] [MainThread]: Partial parsing: added file: dbt://docs/overview.md
[0m05:00:21.492511 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/alter.sql
[0m05:00:21.493480 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_database.sql
[0m05:00:21.494421 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_accepted_values.sql
[0m05:00:21.495479 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_parameter.sql
[0m05:00:21.496464 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/bool_or.sql
[0m05:00:21.497429 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/metadata.sql
[0m05:00:21.498382 [debug] [MainThread]: Partial parsing: added file: dbt://macros/get_custom_name/get_custom_alias.sql
[0m05:00:21.499336 [debug] [MainThread]: Partial parsing: added file: dbt://macros/unit_test_sql/get_fixture_sql.sql
[0m05:00:21.500308 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/apply_grants.sql
[0m05:00:21.501245 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/position.sql
[0m05:00:21.502192 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_column_values.sql
[0m05:00:21.503170 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_orders.sql
[0m05:00:21.505051 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/rename.sql
[0m05:00:21.507599 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/rename.sql
[0m05:00:21.508931 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/schema.sql
[0m05:00:21.510059 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/timestamps.sql
[0m05:00:21.511052 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/helpers.sql
[0m05:00:21.512126 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_backup.sql
[0m05:00:21.513354 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/dateadd.sql
[0m05:00:21.514446 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/view.sql
[0m05:00:21.515551 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/sequential_values.sql
[0m05:00:21.516600 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/drop.sql
[0m05:00:21.517623 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/create.sql
[0m05:00:21.518656 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata/list_relations_without_caching.sql
[0m05:00:21.519807 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/dbt_test_health.sql
[0m05:00:21.520993 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/strategies.sql
[0m05:00:21.523178 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/datetime.sql
[0m05:00:21.524448 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/escape_single_quotes.sql
[0m05:00:21.525609 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/column_helpers.sql
[0m05:00:21.526693 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/is_incremental.sql
[0m05:00:21.528057 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equal_rowcount.sql
[0m05:00:21.529203 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/right.sql
[0m05:00:21.530257 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_construct.sql
[0m05:00:21.531319 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/schema.sql
[0m05:00:21.532382 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/drop.sql
[0m05:00:21.533460 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/hooks.sql
[0m05:00:21.534572 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create.sql
[0m05:00:21.535639 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/query_history_health.sql
[0m05:00:21.538611 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/clone.sql
[0m05:00:21.539789 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/test.sql
[0m05:00:21.540860 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/fewer_rows_than.sql
[0m05:00:21.541998 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/last_day.sql
[0m05:00:21.543158 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/cast.sql
[0m05:00:21.544442 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/test.sql
[0m05:00:21.547773 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_subtract.sql
[0m05:00:21.552128 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date.sql
[0m05:00:21.555533 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/timestamps.sql
[0m05:00:21.557135 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/unit.sql
[0m05:00:21.559021 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users_scd2.sql
[0m05:00:21.560670 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/create.sql
[0m05:00:21.562238 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_pattern.sql
[0m05:00:21.563710 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/slugify.sql
[0m05:00:21.565301 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/replace.sql
[0m05:00:21.566531 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/replace.sql
[0m05:00:21.567711 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_single_value.sql
[0m05:00:21.570396 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/bool_or.sql
[0m05:00:21.571845 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/merge.sql
[0m05:00:21.573712 [debug] [MainThread]: Partial parsing: added file: dbt://tests/generic/builtin.sql
[0m05:00:21.575297 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/create_backup.sql
[0m05:00:21.576692 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/replace.sql
[0m05:00:21.577872 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/rename.sql
[0m05:00:21.579242 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/dynamic_table.sql
[0m05:00:21.580513 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/right.sql
[0m05:00:21.581589 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/materialized_view.sql
[0m05:00:21.582974 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/can_clone_table.sql
[0m05:00:21.585956 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/expression_is_true.sql
[0m05:00:21.587061 [debug] [MainThread]: Partial parsing: added file: dbt://macros/etc/statement.sql
[0m05:00:21.588315 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/replace.sql
[0m05:00:21.589533 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/replace.sql
[0m05:00:21.590598 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/where_subquery.sql
[0m05:00:21.591618 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/date_spine.sql
[0m05:00:21.592643 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_null_proportion.sql
[0m05:00:21.593658 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/clone.sql
[0m05:00:21.594711 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/listagg.sql
[0m05:00:21.595907 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/data_types.sql
[0m05:00:21.601430 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/catalog.sql
[0m05:00:21.604605 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/create.sql
[0m05:00:21.605909 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/column/columns_spec_ddl.sql
[0m05:00:21.607640 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/apply_grants.sql
[0m05:00:21.609669 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/cardinality_equality.sql
[0m05:00:21.611463 [debug] [MainThread]: Partial parsing: added file: dbt://macros/generic_test_sql/unique.sql
[0m05:00:21.612864 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_divide.sql
[0m05:00:21.617128 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename.sql
[0m05:00:21.618982 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/length.sql
[0m05:00:21.620357 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/at_least_one.sql
[0m05:00:21.621826 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/helpers.sql
[0m05:00:21.622772 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/on_schema_change.sql
[0m05:00:21.623691 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/pipeline_runtime_health.sql
[0m05:00:21.624603 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/freshness.sql
[0m05:00:21.625479 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/data_quality_health.sql
[0m05:00:21.626349 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/clone/create_or_replace_clone.sql
[0m05:00:21.627246 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/literal.sql
[0m05:00:21.628128 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/rename_intermediate.sql
[0m05:00:21.628962 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_trunc.sql
[0m05:00:21.629794 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/strategies.sql
[0m05:00:21.634415 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/cast.sql
[0m05:00:21.635894 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot.sql
[0m05:00:21.637310 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/show.sql
[0m05:00:21.638760 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck.sql
[0m05:00:21.640172 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/union.sql
[0m05:00:21.641630 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/mutually_exclusive_ranges.sql
[0m05:00:21.642609 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/insert_overwrite.sql
[0m05:00:21.643677 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/split_part.sql
[0m05:00:21.645100 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/except.sql
[0m05:00:21.648316 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_query_results_as_dict.sql
[0m05:00:21.649681 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/equality.sql
[0m05:00:21.650867 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/configs.sql
[0m05:00:21.652329 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/deduplicate.sql
[0m05:00:21.653492 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_path.sql
[0m05:00:21.654741 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/dim_users.sql
[0m05:00:21.656254 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/safe_cast.sql
[0m05:00:21.657646 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/log_info.sql
[0m05:00:21.659049 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_events.sql
[0m05:00:21.660526 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/array_construct.sql
[0m05:00:21.663572 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/concat.sql
[0m05:00:21.664952 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_relations_by_prefix.sql
[0m05:00:21.666358 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/relation.sql
[0m05:00:21.667720 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/snapshots/snapshot_merge.sql
[0m05:00:21.669114 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/hash.sql
[0m05:00:21.670631 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/drop.sql
[0m05:00:21.671842 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/incremental/incremental.sql
[0m05:00:21.673110 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/table.sql
[0m05:00:21.674294 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/tests/helpers.sql
[0m05:00:21.675524 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/view/rename.sql
[0m05:00:21.676657 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/pivot.sql
[0m05:00:21.679781 [debug] [MainThread]: Partial parsing: added file: live_c360://models/monitoring/etl_health_dashboard.sql
[0m05:00:21.681115 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/intersect.sql
[0m05:00:21.682481 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/not_empty_string.sql
[0m05:00:21.684145 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/replace.sql
[0m05:00:21.685165 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/validate_sql.sql
[0m05:00:21.686311 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_ephemeral.sql
[0m05:00:21.687475 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/unique_combination_of_columns.sql
[0m05:00:21.688733 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/adapters.sql
[0m05:00:21.691352 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/_is_relation.sql
[0m05:00:21.692691 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/replace.sql
[0m05:00:21.695635 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_log_format.sql
[0m05:00:21.697124 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/width_bucket.sql
[0m05:00:21.698468 [debug] [MainThread]: Partial parsing: added file: live_c360://models/staging/stg_users.sql
[0m05:00:21.700044 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/drop.sql
[0m05:00:21.703429 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/snapshot.sql
[0m05:00:21.706510 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/materialized_view/refresh.sql
[0m05:00:21.714044 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_pattern_sql.sql
[0m05:00:21.715259 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/seeds/seed.sql
[0m05:00:21.716520 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/table/create.sql
[0m05:00:21.718428 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/metadata.sql
[0m05:00:21.721912 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/alter.sql
[0m05:00:21.722942 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_tables_by_prefix_sql.sql
[0m05:00:21.725845 [debug] [MainThread]: Partial parsing: added file: live_c360://models/marts/mv_fact_orders.sql
[0m05:00:21.726983 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/view/drop.sql
[0m05:00:21.727954 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/replace.sql
[0m05:00:21.728887 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/replace.sql
[0m05:00:21.729853 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create_intermediate.sql
[0m05:00:21.731392 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/create.sql
[0m05:00:21.732725 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/web/get_url_host.sql
[0m05:00:21.735583 [debug] [MainThread]: Partial parsing: added file: dbt://macros/relations/drop_backup.sql
[0m05:00:21.736884 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/generic_tests/recency.sql
[0m05:00:21.738081 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/surrogate_key.sql
[0m05:00:21.739333 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/unpivot.sql
[0m05:00:21.743541 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/rename.sql
[0m05:00:21.744787 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/jinja_helpers/pretty_time.sql
[0m05:00:21.746525 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/utils/optional.sql
[0m05:00:21.747756 [debug] [MainThread]: Partial parsing: added file: dbt://macros/adapters/persist_docs.sql
[0m05:00:21.748821 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_series.sql
[0m05:00:21.750446 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/dynamic_table/drop.sql
[0m05:00:21.751672 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/safe_add.sql
[0m05:00:21.752876 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/get_table_types_sql.sql
[0m05:00:21.754040 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/incremental/merge.sql
[0m05:00:21.756675 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/star.sql
[0m05:00:21.758099 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/date_spine.sql
[0m05:00:21.760437 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/safe_cast.sql
[0m05:00:21.761476 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/nullcheck_table.sql
[0m05:00:21.762515 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros/sql/generate_surrogate_key.sql
[0m05:00:21.763632 [debug] [MainThread]: Partial parsing: added file: dbt://macros/materializations/models/view.sql
[0m05:00:21.764578 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/relations/table/create.sql
[0m05:00:21.765521 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros/materializations/seed.sql
[0m05:00:21.766429 [debug] [MainThread]: Partial parsing: added file: dbt://macros/utils/array_append.sql
[0m05:00:21.767988 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\relation.sql
[0m05:00:21.768988 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\replace.sql
[0m05:00:21.769824 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_prefix.sql
[0m05:00:21.772289 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\rename.sql
[0m05:00:21.773423 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\clone.sql
[0m05:00:21.774674 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\cast.sql
[0m05:00:21.775831 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\is_incremental.sql
[0m05:00:21.777215 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\apply_grants.sql
[0m05:00:21.778269 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_surrogate_key.sql
[0m05:00:21.779395 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\date_spine.sql
[0m05:00:21.780317 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\etl_health_dashboard.sql
[0m05:00:21.781222 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\rename.sql
[0m05:00:21.782057 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\clone.sql
[0m05:00:21.782881 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\incremental.sql
[0m05:00:21.783694 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\unpivot.sql
[0m05:00:21.784551 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_construct.sql
[0m05:00:21.785364 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck.sql
[0m05:00:21.786528 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\describe.sql
[0m05:00:21.788890 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\unit.sql
[0m05:00:21.789837 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\deduplicate.sql
[0m05:00:21.790715 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot_merge.sql
[0m05:00:21.791567 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\refresh.sql
[0m05:00:21.792463 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\drop.sql
[0m05:00:21.793321 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\log_info.sql
[0m05:00:21.794466 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\create.sql
[0m05:00:21.795331 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\schema.sql
[0m05:00:21.796822 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_divide.sql
[0m05:00:21.797956 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop.sql
[0m05:00:21.798920 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\optional.sql
[0m05:00:21.800235 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\create.sql
[0m05:00:21.801292 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\data_types.sql
[0m05:00:21.803832 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_time.sql
[0m05:00:21.804814 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\test.sql
[0m05:00:21.807230 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental.sql
[0m05:00:21.808211 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\unique.sql
[0m05:00:21.809117 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\at_least_one.sql
[0m05:00:21.810001 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_backup.sql
[0m05:00:21.811683 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\table.sql
[0m05:00:21.813118 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\statement.sql
[0m05:00:21.814076 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equal_rowcount.sql
[0m05:00:21.814952 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\relationships.sql
[0m05:00:21.815903 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\merge.sql
[0m05:00:21.816933 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\not_null.sql
[0m05:00:21.819969 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_append.sql
[0m05:00:21.820919 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\length.sql
[0m05:00:21.821781 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\query_history_health.sql
[0m05:00:21.822759 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\create.sql
[0m05:00:21.823756 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\replace.sql
[0m05:00:21.824681 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_orders.sql
[0m05:00:21.825572 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\insert_overwrite.sql
[0m05:00:21.826436 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_schema.sql
[0m05:00:21.827763 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\array_concat.sql
[0m05:00:21.828786 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\hash.sql
[0m05:00:21.829654 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users.sql
[0m05:00:21.830520 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\helpers.sql
[0m05:00:21.831463 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\replace.sql
[0m05:00:21.832360 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\listagg.sql
[0m05:00:21.835134 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\catalog.sql
[0m05:00:21.836228 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\replace.sql
[0m05:00:21.837194 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create.sql
[0m05:00:21.838137 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\concat.sql
[0m05:00:21.839235 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\safe_cast.sql
[0m05:00:21.840168 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\fewer_rows_than.sql
[0m05:00:21.841058 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\generate_series.sql
[0m05:00:21.842422 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\column\columns_spec_ddl.sql
[0m05:00:21.843394 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\literal.sql
[0m05:00:21.844303 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\validate_sql.sql
[0m05:00:21.845144 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata\list_relations_without_caching.sql
[0m05:00:21.845977 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\any_value.sql
[0m05:00:21.846787 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_subtract.sql
[0m05:00:21.852206 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\mv_fact_orders.sql
[0m05:00:21.853272 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\drop.sql
[0m05:00:21.854167 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\bool_or.sql
[0m05:00:21.855101 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\strategies.sql
[0m05:00:21.856329 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\drop.sql
[0m05:00:21.857371 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_trunc.sql
[0m05:00:21.858289 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\position.sql
[0m05:00:21.859227 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_relation.sql
[0m05:00:21.860279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\hooks.sql
[0m05:00:21.861260 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\materialized_view.sql
[0m05:00:21.863072 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\relationships_where.sql
[0m05:00:21.864346 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\test.sql
[0m05:00:21.866458 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\accepted_range.sql
[0m05:00:21.867691 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\slugify.sql
[0m05:00:21.868931 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date_spine.sql
[0m05:00:21.870074 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\equality.sql
[0m05:00:21.871236 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_host.sql
[0m05:00:21.872412 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_empty_string.sql
[0m05:00:21.874130 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\create.sql
[0m05:00:21.875314 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\table\replace.sql
[0m05:00:21.876511 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename.sql
[0m05:00:21.877591 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\date.sql
[0m05:00:21.878685 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\metadata.sql
[0m05:00:21.879933 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\seed.sql
[0m05:00:21.882145 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\dynamic_table.sql
[0m05:00:21.883349 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\rename.sql
[0m05:00:21.884643 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast.sql
[0m05:00:21.885867 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\rename_intermediate.sql
[0m05:00:21.886954 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\create_or_replace_clone.sql
[0m05:00:21.888165 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\cast_bool_to_text.sql
[0m05:00:21.889541 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\create.sql
[0m05:00:21.890671 [debug] [MainThread]: Partial parsing: deleted file: dbt://tests\generic\builtin.sql
[0m05:00:21.891743 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\recency.sql
[0m05:00:21.892840 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_column_values.sql
[0m05:00:21.893912 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\refresh.sql
[0m05:00:21.895029 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\split_part.sql
[0m05:00:21.897815 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\star.sql
[0m05:00:21.898970 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\generate_series.sql
[0m05:00:21.900149 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\view.sql
[0m05:00:21.901369 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\timestamps.sql
[0m05:00:21.902604 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\column_helpers.sql
[0m05:00:21.903783 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\drop.sql
[0m05:00:21.904915 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\drop.sql
[0m05:00:21.906073 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_filtered_columns_in_relation.sql
[0m05:00:21.907378 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\fact_orders.sql
[0m05:00:21.908626 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\replace.sql
[0m05:00:21.909820 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_accepted_values.sql
[0m05:00:21.910937 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\incremental\merge.sql
[0m05:00:21.913634 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\haversine_distance.sql
[0m05:00:21.914717 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\marts\dim_users_scd2.sql
[0m05:00:21.915686 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\materializations\snapshot.sql
[0m05:00:21.917752 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\view.sql
[0m05:00:21.919102 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\table.sql
[0m05:00:21.920239 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\data_quality_health.sql
[0m05:00:21.921198 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_users.sql
[0m05:00:21.922175 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\alter.sql
[0m05:00:21.923082 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_alias.sql
[0m05:00:21.923984 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\unique_combination_of_columns.sql
[0m05:00:21.924888 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\union.sql
[0m05:00:21.925932 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\strategies.sql
[0m05:00:21.926893 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\sequential_values.sql
[0m05:00:21.929095 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\width_bucket.sql
[0m05:00:21.930113 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\mutually_exclusive_ranges.sql
[0m05:00:21.931063 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\etc\datetime.sql
[0m05:00:21.932115 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\drop_backup.sql
[0m05:00:21.933095 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\right.sql
[0m05:00:21.934066 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_path.sql
[0m05:00:21.935062 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\except.sql
[0m05:00:21.936074 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename_intermediate.sql
[0m05:00:21.936995 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create_backup.sql
[0m05:00:21.937864 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\get_custom_name\get_custom_database.sql
[0m05:00:21.938707 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\last_day.sql
[0m05:00:21.939877 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\escape_single_quotes.sql
[0m05:00:21.940803 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\snapshot.sql
[0m05:00:21.941608 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\nullcheck_table.sql
[0m05:00:21.942439 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\apply_grants.sql
[0m05:00:21.944725 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\drop.sql
[0m05:00:21.945631 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\right.sql
[0m05:00:21.946919 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\columns.sql
[0m05:00:21.947835 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\generic_test_sql\accepted_values.sql
[0m05:00:21.948642 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\rename.sql
[0m05:00:21.949418 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\rename.sql
[0m05:00:21.950274 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\replace.sql
[0m05:00:21.951158 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_pattern_sql.sql
[0m05:00:21.952688 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_null_proportion.sql
[0m05:00:21.953692 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\datediff.sql
[0m05:00:21.954670 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\configs.sql
[0m05:00:21.955645 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\materialized_view\alter.sql
[0m05:00:21.956675 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\dynamic_table\create.sql
[0m05:00:21.957737 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\equals.sql
[0m05:00:21.962294 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_tables_by_prefix_sql.sql
[0m05:00:21.967926 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\adapters.sql
[0m05:00:21.969153 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\table\replace.sql
[0m05:00:21.971614 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\safe_cast.sql
[0m05:00:21.973206 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\persist_docs.sql
[0m05:00:21.978349 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_relations_by_pattern.sql
[0m05:00:21.979906 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\array_construct.sql
[0m05:00:21.980969 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\incremental\on_schema_change.sql
[0m05:00:21.981979 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_table_types_sql.sql
[0m05:00:21.983025 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\utils\bool_or.sql
[0m05:00:21.984107 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\view\replace.sql
[0m05:00:21.985269 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\schema.sql
[0m05:00:21.986376 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\dateadd.sql
[0m05:00:21.987436 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\rename.sql
[0m05:00:21.988506 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\staging\stg_events.sql
[0m05:00:21.991716 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\helpers.sql
[0m05:00:21.992830 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_query_results_as_dict.sql
[0m05:00:21.994237 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\create.sql
[0m05:00:21.995278 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\freshness.sql
[0m05:00:21.996142 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\expression_is_true.sql
[0m05:00:21.996928 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros\relations\view\drop.sql
[0m05:00:21.997758 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\indexes.sql
[0m05:00:21.998561 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\_is_ephemeral.sql
[0m05:00:21.999761 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\relations\create_intermediate.sql
[0m05:00:22.000743 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\seeds\seed.sql
[0m05:00:22.001823 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\not_constant.sql
[0m05:00:22.002883 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\generic_tests\cardinality_equality.sql
[0m05:00:22.004954 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\dbt_test_health.sql
[0m05:00:22.006951 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\metadata.sql
[0m05:00:22.007839 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\web\get_url_parameter.sql
[0m05:00:22.008572 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models\monitoring\pipeline_runtime_health.sql
[0m05:00:22.009315 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\show.sql
[0m05:00:22.010082 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\adapters\timestamps.sql
[0m05:00:22.010824 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\unit_test_sql\get_fixture_sql.sql
[0m05:00:22.011566 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\escape_single_quotes.sql
[0m05:00:22.012287 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\utils\intersect.sql
[0m05:00:22.013082 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\jinja_helpers\pretty_log_format.sql
[0m05:00:22.013797 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\tests\where_subquery.sql
[0m05:00:22.014609 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\python_model\python.sql
[0m05:00:22.015307 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\get_single_value.sql
[0m05:00:22.016016 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\safe_add.sql
[0m05:00:22.016797 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\snapshots\helpers.sql
[0m05:00:22.017483 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros\materializations\models\clone\can_clone_table.sql
[0m05:00:22.018165 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\pivot.sql
[0m05:00:22.018843 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\surrogate_key.sql
[0m05:00:22.019587 [debug] [MainThread]: Partial parsing: deleted file: dbt://docs\overview.md
[0m05:00:22.020278 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros\sql\groupby.sql
[0m05:00:22.042720 [error] [MainThread]: Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[0m05:00:22.044377 [error] [MainThread]: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'

[0m05:00:22.045851 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 3.0940006, "process_in_blocks": "0", "process_kernel_time": 0.6178, "process_mem_max_rss": "297264", "process_out_blocks": "0", "process_user_time": 3.144773}
[0m05:00:22.046794 [debug] [MainThread]: Command `cli run` failed at 05:00:22.046674 after 3.10 seconds
[0m05:00:22.047698 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8d8a4160>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed81788fd0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x77ed8178a1d0>]}
[0m05:00:22.048522 [debug] [MainThread]: Flushing usage events
[0m05:00:23.011888 [debug] [MainThread]: An error was encountered while trying to flush usage events
