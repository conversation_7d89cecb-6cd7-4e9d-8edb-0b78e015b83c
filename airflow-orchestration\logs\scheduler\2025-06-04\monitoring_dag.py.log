[2025-06-04T01:41:02.799+0000] {processor.py:161} INFO - Started process (PID=119) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:02.801+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:41:02.805+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:02.804+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:03.035+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:03.397+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.396+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:etl_monitoring_hourly
[2025-06-04T01:41:03.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.424+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:etl_monitoring_hourly
[2025-06-04T01:41:03.443+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.442+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:etl_monitoring_hourly
[2025-06-04T01:41:03.443+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.443+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:03.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.466+0000] {dag.py:3058} INFO - Creating ORM DAG for etl_monitoring_hourly
[2025-06-04T01:41:03.483+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.483+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:41:03.518+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.727 seconds
[2025-06-04T01:41:33.655+0000] {processor.py:161} INFO - Started process (PID=127) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:33.658+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:41:33.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.665+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:33.740+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:41:33.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.834+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:33.888+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.888+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:41:33.924+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.289 seconds
[2025-06-04T01:42:04.067+0000] {processor.py:161} INFO - Started process (PID=135) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:04.070+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:42:04.072+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.072+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:04.105+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:04.138+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.138+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:04.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.165+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:42:04.187+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T01:42:35.044+0000] {processor.py:161} INFO - Started process (PID=143) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:35.047+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:42:35.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.049+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:35.118+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:42:35.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.161+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:35.198+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.197+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:42:35.234+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.199 seconds
[2025-06-04T01:43:05.447+0000] {processor.py:161} INFO - Started process (PID=151) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:05.450+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:43:05.452+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.452+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:05.784+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:05.926+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.926+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:06.064+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:06.064+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:43:06.111+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.668 seconds
[2025-06-04T01:43:36.466+0000] {processor.py:161} INFO - Started process (PID=153) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:36.469+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:43:36.471+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.471+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:36.493+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:43:36.524+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.524+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:36.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.549+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:43:36.573+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.110 seconds
[2025-06-04T01:44:06.644+0000] {processor.py:161} INFO - Started process (PID=161) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:06.646+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:44:06.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.648+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:06.683+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:06.729+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.729+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:06.757+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.757+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:44:06.788+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.150 seconds
[2025-06-04T01:44:37.532+0000] {processor.py:161} INFO - Started process (PID=169) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:37.534+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:44:37.538+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.538+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:37.571+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:44:37.610+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.609+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:37.637+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.637+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:44:37.662+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.134 seconds
[2025-06-04T01:45:08.487+0000] {processor.py:161} INFO - Started process (PID=177) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:08.488+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:45:08.492+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.491+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:08.519+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:08.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:08.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.575+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:45:08.599+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T01:45:39.604+0000] {processor.py:161} INFO - Started process (PID=184) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:39.607+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:45:39.610+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.609+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:39.636+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:45:39.669+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.669+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:39.695+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.695+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:45:39.720+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T01:46:09.954+0000] {processor.py:161} INFO - Started process (PID=194) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:09.956+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:46:09.958+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:09.958+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:09.980+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:10.011+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.011+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:10.039+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.038+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:46:10.066+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T01:46:40.866+0000] {processor.py:161} INFO - Started process (PID=203) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:40.868+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:46:40.871+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.871+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:40.894+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:46:40.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.931+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:40.957+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.957+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:46:40.986+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T01:47:11.087+0000] {processor.py:161} INFO - Started process (PID=212) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:11.089+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:47:11.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.092+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:11.124+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:11.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.155+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:11.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.180+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:47:11.202+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T01:47:41.241+0000] {processor.py:161} INFO - Started process (PID=221) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:41.242+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:47:41.244+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.244+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:41.266+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:47:41.296+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.296+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:41.319+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.318+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:47:41.338+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.102 seconds
[2025-06-04T01:48:11.454+0000] {processor.py:161} INFO - Started process (PID=230) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:11.457+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:48:11.459+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.459+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:11.482+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:11.510+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.510+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:11.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.542+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:48:11.628+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.178 seconds
[2025-06-04T01:48:42.481+0000] {processor.py:161} INFO - Started process (PID=239) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:42.482+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:48:42.486+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.485+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:42.511+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:48:42.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.547+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:42.570+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.570+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:48:42.592+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T01:49:12.661+0000] {processor.py:161} INFO - Started process (PID=248) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:12.663+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:49:12.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.666+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:12.693+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:12.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.726+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:12.755+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.755+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:49:12.780+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T01:49:42.927+0000] {processor.py:161} INFO - Started process (PID=256) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:42.929+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:49:42.932+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:42.932+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:42.955+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:49:42.986+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:42.986+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:43.008+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.008+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:49:43.028+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.105 seconds
[2025-06-04T01:50:13.289+0000] {processor.py:161} INFO - Started process (PID=265) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:13.291+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:50:13.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.296+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:13.327+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:13.385+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.385+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:13.410+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.410+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:50:13.435+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.154 seconds
[2025-06-04T01:50:44.299+0000] {processor.py:161} INFO - Started process (PID=274) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:44.301+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:50:44.304+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.303+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:44.335+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:50:44.374+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.374+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:44.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.407+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:50:44.438+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.143 seconds
[2025-06-04T01:51:14.659+0000] {processor.py:161} INFO - Started process (PID=283) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:14.660+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:51:14.663+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.663+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:14.694+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:14.729+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.729+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:14.754+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.754+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:51:14.777+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T01:51:44.931+0000] {processor.py:161} INFO - Started process (PID=291) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:44.934+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:51:44.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:44.937+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:44.964+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:51:45.007+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.006+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:45.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.041+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:51:45.067+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.142 seconds
[2025-06-04T01:52:15.962+0000] {processor.py:161} INFO - Started process (PID=300) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:15.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:52:15.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:15.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:15.991+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:16.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.024+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:16.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.051+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:52:16.086+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T01:52:47.108+0000] {processor.py:161} INFO - Started process (PID=309) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:47.110+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:52:47.113+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.112+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:47.139+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:52:47.175+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.174+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:47.205+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.205+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:52:47.235+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T01:53:19.297+0000] {processor.py:161} INFO - Started process (PID=319) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:19.299+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:53:19.301+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.301+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:19.325+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:19.359+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.359+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:19.385+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.385+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:53:19.413+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T01:53:50.732+0000] {processor.py:161} INFO - Started process (PID=328) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:50.738+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:53:50.741+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.740+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:50.784+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:53:50.824+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.824+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:50.855+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.855+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:53:50.879+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.155 seconds
[2025-06-04T01:54:21.586+0000] {processor.py:161} INFO - Started process (PID=337) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:21.589+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:54:21.593+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:21.625+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:21.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.660+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:21.689+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.688+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:54:21.726+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.144 seconds
[2025-06-04T01:54:52.058+0000] {processor.py:161} INFO - Started process (PID=346) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:52.061+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:54:52.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.063+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:52.095+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:54:52.139+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.139+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:52.182+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.182+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:54:52.233+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.179 seconds
[2025-06-04T01:55:22.426+0000] {processor.py:161} INFO - Started process (PID=355) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:22.427+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:55:22.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.431+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:22.470+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:22.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.507+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:22.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.540+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:55:22.583+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.162 seconds
[2025-06-04T01:55:52.851+0000] {processor.py:161} INFO - Started process (PID=364) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:52.853+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:55:52.856+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:52.855+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:52.879+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:55:52.913+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:52.912+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:52.943+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:52.943+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:55:52.965+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T01:56:23.920+0000] {processor.py:161} INFO - Started process (PID=373) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:23.922+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:56:23.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:23.924+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:23.948+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:23.991+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:23.991+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:24.020+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.020+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:56:24.050+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.134 seconds
[2025-06-04T01:56:54.944+0000] {processor.py:161} INFO - Started process (PID=382) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:54.946+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:56:54.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:54.949+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:54.982+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:56:55.045+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.045+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:55.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.085+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:56:55.114+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.174 seconds
[2025-06-04T01:57:26.012+0000] {processor.py:161} INFO - Started process (PID=391) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:26.013+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:57:26.016+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.015+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:26.037+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:26.070+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.070+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:26.097+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.097+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:57:26.119+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.112 seconds
[2025-06-04T01:57:56.290+0000] {processor.py:161} INFO - Started process (PID=401) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:56.293+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:57:56.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.297+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:56.331+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:57:56.367+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.367+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:56.399+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.398+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:57:56.427+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.143 seconds
[2025-06-04T01:58:26.505+0000] {processor.py:161} INFO - Started process (PID=410) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:26.509+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:58:26.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:26.511+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:26.755+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:27.205+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.205+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:27.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.243+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:58:27.281+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.780 seconds
[2025-06-04T01:58:58.113+0000] {processor.py:161} INFO - Started process (PID=419) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:58.115+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:58:58.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.117+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:58.162+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:58:58.203+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.203+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:58.283+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.273+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:58:58.765+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.656 seconds
[2025-06-04T01:59:28.990+0000] {processor.py:161} INFO - Started process (PID=422) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:28.992+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:59:28.994+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:28.994+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:29.025+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:29.064+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:29.064+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:29.091+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:29.091+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:59:29.125+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.139 seconds
[2025-06-04T01:59:59.253+0000] {processor.py:161} INFO - Started process (PID=430) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:59.254+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T01:59:59.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:59.256+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:59.282+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T01:59:59.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:59.320+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:59.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:59.348+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T01:59:59.373+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T02:00:29.547+0000] {processor.py:161} INFO - Started process (PID=438) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:00:29.548+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:00:29.551+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:29.551+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:00:29.584+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:00:29.618+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:29.617+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:29.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:29.648+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:00:29.674+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.134 seconds
[2025-06-04T02:01:00.507+0000] {processor.py:161} INFO - Started process (PID=447) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:00.510+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:01:00.513+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:00.513+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:00.535+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:00.569+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:00.569+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:00.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:00.592+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:01:00.613+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.111 seconds
[2025-06-04T02:01:30.691+0000] {processor.py:161} INFO - Started process (PID=456) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:30.693+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:01:30.696+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:30.696+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:30.719+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:01:30.749+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:30.748+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:30.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:30.771+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:01:30.803+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T02:02:00.902+0000] {processor.py:161} INFO - Started process (PID=466) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:00.903+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:02:00.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:00.905+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:00.932+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:00.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:00.963+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:00.988+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:00.988+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:02:01.024+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T02:02:31.154+0000] {processor.py:161} INFO - Started process (PID=475) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:31.155+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:02:31.158+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:31.158+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:31.184+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:02:31.221+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:31.221+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:31.246+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:31.246+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:02:31.269+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T02:03:02.119+0000] {processor.py:161} INFO - Started process (PID=484) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:02.120+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:03:02.123+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:02.122+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:02.172+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:02.244+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:02.243+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:02.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:02.269+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:03:02.290+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.176 seconds
[2025-06-04T02:03:32.465+0000] {processor.py:161} INFO - Started process (PID=493) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:32.467+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:03:32.469+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:32.469+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:32.490+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:03:32.523+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:32.522+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:32.545+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:32.545+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:03:32.566+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.105 seconds
[2025-06-04T02:04:02.643+0000] {processor.py:161} INFO - Started process (PID=502) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:02.645+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:04:02.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:02.648+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:02.684+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:02.720+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:02.720+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:02.749+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:02.749+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:04:02.774+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.135 seconds
[2025-06-04T02:04:32.894+0000] {processor.py:161} INFO - Started process (PID=511) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:32.896+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:04:32.900+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:32.900+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:32.923+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:04:32.956+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:32.955+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:32.982+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:32.982+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:04:33.024+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.135 seconds
[2025-06-04T02:05:03.220+0000] {processor.py:161} INFO - Started process (PID=521) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:03.221+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:05:03.224+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:03.224+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:03.245+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:03.275+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:03.275+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:03.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:03.297+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:05:03.317+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.102 seconds
[2025-06-04T02:05:33.461+0000] {processor.py:161} INFO - Started process (PID=529) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:33.463+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:05:33.465+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:33.464+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:33.492+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:05:33.524+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:33.524+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:33.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:33.546+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:05:33.569+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.112 seconds
[2025-06-04T02:06:03.768+0000] {processor.py:161} INFO - Started process (PID=538) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:03.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:06:03.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.772+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:03.797+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:03.829+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.829+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:03.852+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:03.852+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:06:03.873+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.108 seconds
[2025-06-04T02:06:34.843+0000] {processor.py:161} INFO - Started process (PID=547) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:34.845+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:06:34.848+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.847+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:34.878+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:06:34.911+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.911+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:34.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:34.939+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:06:34.965+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T02:07:05.832+0000] {processor.py:161} INFO - Started process (PID=556) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:05.836+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:07:05.844+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:05.843+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:05.872+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:05.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:05.930+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:05.961+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:05.961+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:07:06.034+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.207 seconds
[2025-06-04T02:07:36.279+0000] {processor.py:161} INFO - Started process (PID=564) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:36.280+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:07:36.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.284+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:36.306+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:07:36.358+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.357+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:36.405+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.405+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:07:36.448+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.173 seconds
[2025-06-04T02:08:06.837+0000] {processor.py:161} INFO - Started process (PID=574) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:06.840+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:08:06.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:06.842+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:06.868+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:07.033+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.033+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:07.104+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.104+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:08:07.148+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.316 seconds
[2025-06-04T02:08:38.494+0000] {processor.py:161} INFO - Started process (PID=583) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:38.496+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:08:38.499+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.499+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:38.520+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:08:38.551+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.551+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:38.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.574+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:08:38.603+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.113 seconds
[2025-06-04T02:09:10.499+0000] {processor.py:161} INFO - Started process (PID=592) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:10.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:09:10.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:10.526+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:10.560+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.559+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:10.587+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.586+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:09:10.610+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T02:09:42.671+0000] {processor.py:161} INFO - Started process (PID=601) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:42.673+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:09:42.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.675+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:42.702+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:09:42.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.735+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:42.760+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.760+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:09:42.787+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T02:10:14.129+0000] {processor.py:161} INFO - Started process (PID=610) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:14.130+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:10:14.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.132+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:14.156+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:14.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.193+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:14.220+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.220+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:10:14.261+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.140 seconds
[2025-06-04T02:10:46.471+0000] {processor.py:161} INFO - Started process (PID=619) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:46.472+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:10:46.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.474+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:46.515+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:10:46.550+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.550+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:46.577+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.577+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:10:46.607+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.140 seconds
[2025-06-04T02:11:17.706+0000] {processor.py:161} INFO - Started process (PID=628) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:17.709+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:11:17.712+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.712+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:17.742+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:17.774+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.774+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:17.798+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.797+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:11:17.818+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T02:11:48.940+0000] {processor.py:161} INFO - Started process (PID=637) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:48.942+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:11:48.945+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:48.944+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:48.970+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:11:49.007+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.007+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:49.034+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.033+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:11:49.057+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T02:17:18.110+0000] {processor.py:161} INFO - Started process (PID=118) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:17:18.115+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:17:18.122+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.120+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:17:18.186+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:17:18.525+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.525+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:17:18.576+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.575+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:17:18.632+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.537 seconds
[2025-06-04T02:20:34.234+0000] {processor.py:161} INFO - Started process (PID=62) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:20:34.256+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:20:34.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.262+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:20:34.297+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:20:34.694+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.694+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:20:34.719+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:34.719+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:20:35.577+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 1.346 seconds
[2025-06-04T02:21:00.109+0000] {processor.py:161} INFO - Started process (PID=61) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:00.111+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:21:00.117+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.117+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:00.145+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:00.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.199+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:00.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.235+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:21:00.619+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.515 seconds
[2025-06-04T02:21:17.791+0000] {processor.py:161} INFO - Started process (PID=56) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:17.792+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:21:17.795+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.795+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:17.821+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:17.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.874+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:17.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.897+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:21:17.926+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.145 seconds
[2025-06-04T02:21:48.131+0000] {processor.py:161} INFO - Started process (PID=65) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:48.133+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:21:48.136+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.135+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:48.168+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:21:48.226+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.226+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:48.276+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.275+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:21:48.311+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.185 seconds
[2025-06-04T02:22:18.582+0000] {processor.py:161} INFO - Started process (PID=74) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:18.584+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:22:18.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.586+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:18.613+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:18.651+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.651+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:18.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.675+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:22:18.707+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T02:22:48.916+0000] {processor.py:161} INFO - Started process (PID=84) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:48.918+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:22:48.923+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:48.922+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:48.956+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:22:48.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:48.991+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:49.016+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.015+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:22:49.043+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T02:38:09.733+0000] {processor.py:161} INFO - Started process (PID=52) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:09.737+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:38:09.742+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:09.742+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:09.812+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:10.242+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.242+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:10.294+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.294+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:38:10.348+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.622 seconds
[2025-06-04T02:38:40.514+0000] {processor.py:161} INFO - Started process (PID=61) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:40.518+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:38:40.520+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.520+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:40.552+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:38:40.594+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.594+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:40.620+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.620+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:38:40.646+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.139 seconds
[2025-06-04T02:39:11.011+0000] {processor.py:161} INFO - Started process (PID=70) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:11.016+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:39:11.026+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.025+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:11.097+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:11.172+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.171+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:11.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.223+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:39:11.264+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.261 seconds
[2025-06-04T02:39:41.461+0000] {processor.py:161} INFO - Started process (PID=79) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:41.463+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:39:41.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.466+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:41.493+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:39:41.528+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.528+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:41.556+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:41.556+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:39:41.586+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.130 seconds
[2025-06-04T02:40:11.725+0000] {processor.py:161} INFO - Started process (PID=88) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:11.726+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:40:11.730+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.728+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:11.752+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:11.787+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.786+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:11.815+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:11.814+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:40:11.837+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T02:40:41.989+0000] {processor.py:161} INFO - Started process (PID=97) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:41.991+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:40:41.995+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:41.995+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:42.029+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:40:42.061+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:42.061+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:42.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:42.086+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:40:42.107+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T02:41:12.226+0000] {processor.py:161} INFO - Started process (PID=106) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:12.227+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:41:12.231+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.231+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:12.260+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:12.294+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.294+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:12.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:12.320+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:41:12.342+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T02:41:43.062+0000] {processor.py:161} INFO - Started process (PID=115) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:43.064+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:41:43.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.067+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:43.099+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:41:43.139+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.139+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:43.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:43.236+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:41:43.326+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.270 seconds
[2025-06-04T02:42:14.073+0000] {processor.py:161} INFO - Started process (PID=125) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:14.076+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:42:14.079+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.079+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:14.109+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:14.143+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.143+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:14.170+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.170+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:42:14.197+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T02:42:44.571+0000] {processor.py:161} INFO - Started process (PID=134) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:44.576+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:42:44.579+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.579+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:44.602+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:42:44.639+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.639+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:44.665+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.665+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:42:44.688+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T02:43:14.985+0000] {processor.py:161} INFO - Started process (PID=143) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:14.987+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:43:14.990+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:14.990+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:15.027+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:15.070+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.070+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:15.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.095+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:43:15.125+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.148 seconds
[2025-06-04T02:43:45.426+0000] {processor.py:161} INFO - Started process (PID=152) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:45.430+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:43:45.439+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.438+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:45.471+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:43:45.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.539+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:45.582+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.582+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:43:45.618+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.196 seconds
[2025-06-04T02:44:16.051+0000] {processor.py:161} INFO - Started process (PID=161) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:16.055+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:44:16.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.058+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:16.090+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:16.124+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.124+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:16.153+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:16.153+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:44:16.189+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.142 seconds
[2025-06-04T02:44:47.143+0000] {processor.py:161} INFO - Started process (PID=169) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:47.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:44:47.147+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.147+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:47.176+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:44:47.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.212+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:47.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:47.268+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:44:47.340+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.204 seconds
[2025-06-04T02:45:17.925+0000] {processor.py:161} INFO - Started process (PID=178) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:17.926+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:45:17.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:17.931+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:17.978+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:18.030+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.030+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:18.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.066+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:45:18.097+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.179 seconds
[2025-06-04T02:45:48.304+0000] {processor.py:161} INFO - Started process (PID=187) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:48.306+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:45:48.308+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.308+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:48.331+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:45:48.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.370+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:48.395+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:48.395+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:45:48.423+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T02:46:18.631+0000] {processor.py:161} INFO - Started process (PID=196) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:18.633+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:46:18.635+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.635+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:18.659+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:18.691+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.690+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:18.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:18.718+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:46:18.742+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T02:46:49.159+0000] {processor.py:161} INFO - Started process (PID=205) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:49.161+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:46:49.164+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:49.163+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:49.193+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:46:49.231+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:49.230+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:49.261+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:49.260+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:46:49.288+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T02:47:19.429+0000] {processor.py:161} INFO - Started process (PID=214) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:19.433+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:47:19.436+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:19.435+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:19.466+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:19.535+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:19.535+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:19.725+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:19.725+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:47:19.784+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.362 seconds
[2025-06-04T02:47:50.225+0000] {processor.py:161} INFO - Started process (PID=223) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:50.227+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:47:50.231+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:50.230+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:50.260+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:47:50.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:50.409+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:50.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:50.435+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:47:50.460+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.239 seconds
[2025-06-04T02:48:21.703+0000] {processor.py:161} INFO - Started process (PID=232) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:21.704+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:48:21.708+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.707+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:21.744+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:21.796+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.795+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:21.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.833+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:48:21.861+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.162 seconds
[2025-06-04T02:48:53.252+0000] {processor.py:161} INFO - Started process (PID=241) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:53.253+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:48:53.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.259+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:53.285+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:48:53.318+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.318+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:53.345+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.344+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:48:53.370+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T02:49:25.385+0000] {processor.py:161} INFO - Started process (PID=250) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:25.387+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:49:25.390+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.389+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:25.420+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:25.459+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.459+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:25.491+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.490+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:49:25.513+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T02:49:56.776+0000] {processor.py:161} INFO - Started process (PID=259) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:56.777+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:49:56.779+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:56.779+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:56.903+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:49:56.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:56.971+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:57.000+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.000+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:49:58.198+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 1.429 seconds
[2025-06-04T02:50:28.788+0000] {processor.py:161} INFO - Started process (PID=268) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:28.790+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:50:28.797+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.797+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:28.840+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:28.885+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.884+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:28.913+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.912+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:50:28.942+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.161 seconds
[2025-06-04T02:50:59.030+0000] {processor.py:161} INFO - Started process (PID=271) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:59.032+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:50:59.036+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:59.035+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:59.061+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:50:59.109+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:59.109+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:59.141+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:59.141+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:50:59.168+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.144 seconds
[2025-06-04T02:51:32.545+0000] {processor.py:161} INFO - Started process (PID=286) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:51:32.547+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:51:32.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.549+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:51:32.581+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:51:32.616+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.616+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:32.645+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.645+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:51:32.668+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T02:52:03.315+0000] {processor.py:161} INFO - Started process (PID=295) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:03.318+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:52:03.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.321+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:03.352+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:03.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.395+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:03.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.423+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:52:03.451+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.140 seconds
[2025-06-04T02:52:34.124+0000] {processor.py:161} INFO - Started process (PID=298) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:34.153+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:52:34.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:34.177+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:35.392+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:52:36.181+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.180+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:36.440+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.439+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:52:36.491+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 1.756 seconds
[2025-06-04T02:53:07.716+0000] {processor.py:161} INFO - Started process (PID=307) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:07.720+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:53:07.723+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.722+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:07.750+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:07.800+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.800+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:07.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.832+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:53:07.858+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.147 seconds
[2025-06-04T02:53:39.328+0000] {processor.py:161} INFO - Started process (PID=316) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:39.357+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:53:39.401+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.390+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:39.510+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:53:39.572+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.572+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:39.601+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.601+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 00:00:00+00:00, run_after=2025-06-03 01:00:00+00:00
[2025-06-04T02:53:39.627+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.316 seconds
[2025-06-04T02:54:17.179+0000] {processor.py:161} INFO - Started process (PID=325) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:17.243+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:54:17.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:17.317+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:17.723+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:18.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.062+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:18.227+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 1.904 seconds
[2025-06-04T02:54:48.432+0000] {processor.py:161} INFO - Started process (PID=334) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:48.435+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:54:48.440+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.438+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:48.463+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:54:48.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.501+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:48.551+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T02:55:18.665+0000] {processor.py:161} INFO - Started process (PID=343) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:18.668+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:55:18.673+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.672+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:18.710+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:18.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.756+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:18.822+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.162 seconds
[2025-06-04T02:55:49.741+0000] {processor.py:161} INFO - Started process (PID=352) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:49.744+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:55:49.747+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.747+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:49.788+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:55:49.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.832+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:49.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.168 seconds
[2025-06-04T02:56:20.094+0000] {processor.py:161} INFO - Started process (PID=362) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:20.098+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:56:20.101+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.100+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:20.134+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:20.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.169+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:20.215+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.126 seconds
[2025-06-04T02:56:50.428+0000] {processor.py:161} INFO - Started process (PID=371) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:50.430+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:56:50.433+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.433+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:50.458+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:56:50.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.495+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:50.553+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T02:57:21.661+0000] {processor.py:161} INFO - Started process (PID=380) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:21.665+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:57:21.668+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.667+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:21.692+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:21.728+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.727+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:21.784+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.128 seconds
[2025-06-04T02:57:53.688+0000] {processor.py:161} INFO - Started process (PID=389) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:53.690+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:57:53.692+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.692+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:53.727+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:57:53.761+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.761+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:53.810+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T02:58:26.030+0000] {processor.py:161} INFO - Started process (PID=398) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:26.039+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:58:26.047+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.046+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:26.081+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:26.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.147+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:26.317+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.292 seconds
[2025-06-04T02:58:57.520+0000] {processor.py:161} INFO - Started process (PID=407) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:57.549+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:58:57.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.551+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:57.577+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:58:57.615+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.615+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:57.657+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.656+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 01:00:00+00:00, run_after=2025-06-03 02:00:00+00:00
[2025-06-04T02:58:57.685+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.173 seconds
[2025-06-04T02:59:28.700+0000] {processor.py:161} INFO - Started process (PID=416) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:59:28.702+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T02:59:28.708+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.707+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:59:28.747+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T02:59:28.812+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.812+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:59:28.877+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.182 seconds
[2025-06-04T03:00:00.875+0000] {processor.py:161} INFO - Started process (PID=425) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:00.876+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:00:00.883+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:00.883+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:00.910+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:00.956+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:00.955+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:01.006+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.135 seconds
[2025-06-04T03:00:32.058+0000] {processor.py:161} INFO - Started process (PID=434) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:32.061+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:00:32.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.064+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:32.151+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:00:32.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.213+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:32.296+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.245 seconds
[2025-06-04T03:01:03.643+0000] {processor.py:161} INFO - Started process (PID=443) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:03.645+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:01:03.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.648+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:03.679+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:03.716+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.715+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:03.764+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:01:35.743+0000] {processor.py:161} INFO - Started process (PID=452) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:35.744+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:01:35.747+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:35.747+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:35.781+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:01:35.853+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:35.852+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:36.000+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.266 seconds
[2025-06-04T03:02:07.069+0000] {processor.py:161} INFO - Started process (PID=461) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:07.071+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:02:07.074+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.073+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:07.107+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:07.145+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.142+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:07.190+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:02:39.235+0000] {processor.py:161} INFO - Started process (PID=470) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:39.236+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:02:39.239+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.238+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:39.271+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:02:39.310+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.310+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:39.358+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T03:03:10.776+0000] {processor.py:161} INFO - Started process (PID=479) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:10.778+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:03:10.791+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:10.791+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:10.830+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:10.892+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:10.892+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:10.994+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.225 seconds
[2025-06-04T03:03:43.047+0000] {processor.py:161} INFO - Started process (PID=488) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:43.048+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:03:43.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.050+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:43.082+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:03:43.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.114+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:43.163+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T03:04:14.161+0000] {processor.py:161} INFO - Started process (PID=497) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:14.162+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:04:14.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.165+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:14.186+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:14.216+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.216+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:14.257+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.101 seconds
[2025-06-04T03:04:45.724+0000] {processor.py:161} INFO - Started process (PID=506) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:45.725+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:04:45.728+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.728+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:45.754+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:04:45.788+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.788+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:45.839+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:05:18.176+0000] {processor.py:161} INFO - Started process (PID=515) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:18.177+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:05:18.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.179+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:18.204+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:18.240+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.239+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:18.315+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.143 seconds
[2025-06-04T03:05:49.491+0000] {processor.py:161} INFO - Started process (PID=524) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:49.495+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:05:49.498+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.498+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:49.524+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:05:49.559+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.559+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:49.608+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:06:21.786+0000] {processor.py:161} INFO - Started process (PID=532) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:21.812+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:06:21.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:21.833+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:21.957+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:22.037+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.037+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:22.168+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.386 seconds
[2025-06-04T03:06:52.847+0000] {processor.py:161} INFO - Started process (PID=541) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:52.849+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:06:52.856+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:52.851+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:52.895+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:06:52.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:52.938+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:52.990+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.149 seconds
[2025-06-04T03:07:25.062+0000] {processor.py:161} INFO - Started process (PID=551) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:25.064+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:07:25.067+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:25.066+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:25.386+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:25.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:25.456+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:25.607+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.549 seconds
[2025-06-04T03:07:56.625+0000] {processor.py:161} INFO - Started process (PID=561) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:56.628+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:07:56.635+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:56.634+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:56.717+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:07:56.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:56.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:56.869+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.248 seconds
[2025-06-04T03:08:29.084+0000] {processor.py:161} INFO - Started process (PID=571) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:08:29.086+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:08:29.097+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:29.089+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:08:29.170+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:08:29.221+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:29.221+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:08:29.296+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.219 seconds
[2025-06-04T03:09:00.436+0000] {processor.py:161} INFO - Started process (PID=580) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:00.437+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:09:00.440+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:00.440+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:00.468+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:00.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:00.502+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:00.554+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T03:09:32.762+0000] {processor.py:161} INFO - Started process (PID=590) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:32.764+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:09:32.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:32.768+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:32.796+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:09:32.845+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:32.845+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:32.945+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.192 seconds
[2025-06-04T03:10:03.019+0000] {processor.py:161} INFO - Started process (PID=599) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:03.021+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:10:03.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:03.024+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:03.054+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:03.084+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:03.084+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:03.126+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.111 seconds
[2025-06-04T03:10:33.225+0000] {processor.py:161} INFO - Started process (PID=608) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:33.229+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:10:33.233+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:33.232+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:33.273+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:10:33.317+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:33.317+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:33.555+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.335 seconds
[2025-06-04T03:11:03.763+0000] {processor.py:161} INFO - Started process (PID=617) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:03.765+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:11:03.768+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:03.767+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:03.795+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:03.838+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:03.838+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:03.889+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.131 seconds
[2025-06-04T03:11:34.706+0000] {processor.py:161} INFO - Started process (PID=626) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:34.707+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:11:34.710+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:34.710+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:34.743+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:11:34.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:34.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:34.831+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T03:12:05.841+0000] {processor.py:161} INFO - Started process (PID=635) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:05.844+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:12:05.847+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:05.847+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:05.878+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:05.913+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:05.913+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:05.963+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.126 seconds
[2025-06-04T03:12:37.059+0000] {processor.py:161} INFO - Started process (PID=644) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:37.062+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:12:37.065+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:37.064+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:37.097+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:12:37.138+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:37.138+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:37.224+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.170 seconds
[2025-06-04T03:13:07.483+0000] {processor.py:161} INFO - Started process (PID=653) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:07.484+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:13:07.487+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:07.486+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:07.517+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:08.799+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:08.799+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:08.847+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T03:13:40.930+0000] {processor.py:161} INFO - Started process (PID=663) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:40.937+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:13:40.947+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:40.947+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:40.987+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:13:41.029+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.029+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:41.086+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.160 seconds
[2025-06-04T03:14:12.169+0000] {processor.py:161} INFO - Started process (PID=672) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:12.175+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:14:12.178+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.177+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:12.208+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:12.248+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.248+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:12.297+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T03:14:44.416+0000] {processor.py:161} INFO - Started process (PID=681) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:44.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:14:44.420+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.420+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:44.446+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:14:44.479+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.479+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:44.531+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T03:15:16.590+0000] {processor.py:161} INFO - Started process (PID=690) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:16.591+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:15:16.594+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.593+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:16.626+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:16.663+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.663+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:16.871+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.286 seconds
[2025-06-04T03:15:47.832+0000] {processor.py:161} INFO - Started process (PID=699) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:47.834+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:15:47.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.837+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:47.865+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:15:47.896+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.896+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:47.947+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:16:19.929+0000] {processor.py:161} INFO - Started process (PID=708) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:19.934+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:16:19.937+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:19.937+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:19.964+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:20.003+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:20.002+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:20.240+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.315 seconds
[2025-06-04T03:16:51.193+0000] {processor.py:161} INFO - Started process (PID=717) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:51.195+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:16:51.198+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.198+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:51.228+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:16:51.264+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.264+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:51.309+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:17:23.391+0000] {processor.py:161} INFO - Started process (PID=726) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:23.393+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:17:23.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.395+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:23.426+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:23.464+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.463+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:23.661+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.277 seconds
[2025-06-04T03:17:54.756+0000] {processor.py:161} INFO - Started process (PID=735) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:54.759+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:17:54.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.761+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:54.796+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:17:54.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.832+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:54.888+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T03:18:26.059+0000] {processor.py:161} INFO - Started process (PID=744) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:26.061+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:18:26.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.063+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:26.088+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:26.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.120+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:26.328+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.275 seconds
[2025-06-04T03:18:57.906+0000] {processor.py:161} INFO - Started process (PID=753) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:57.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:18:57.913+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:57.913+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:57.941+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:18:57.990+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:57.989+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:58.046+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.146 seconds
[2025-06-04T03:19:30.420+0000] {processor.py:161} INFO - Started process (PID=761) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:19:30.421+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:19:30.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.423+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:19:30.447+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:19:30.480+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.480+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:19:30.524+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.112 seconds
[2025-06-04T03:20:01.749+0000] {processor.py:161} INFO - Started process (PID=770) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:01.751+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:20:01.754+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.753+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:01.782+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:01.815+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.815+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:01.866+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T03:20:33.878+0000] {processor.py:161} INFO - Started process (PID=780) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:33.880+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:20:33.883+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:33.882+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:33.911+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:20:33.944+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:33.944+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:33.995+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:21:05.253+0000] {processor.py:161} INFO - Started process (PID=789) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:05.256+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:21:05.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.259+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:05.285+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:05.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.320+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:05.374+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T03:21:37.561+0000] {processor.py:161} INFO - Started process (PID=798) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:37.563+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:21:37.566+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.566+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:37.589+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:21:37.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.649+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:37.723+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.166 seconds
[2025-06-04T03:22:09.811+0000] {processor.py:161} INFO - Started process (PID=807) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:09.813+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:22:09.816+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:09.815+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:09.863+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:09.929+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:09.929+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:09.978+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.173 seconds
[2025-06-04T03:22:41.101+0000] {processor.py:161} INFO - Started process (PID=816) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:41.104+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:22:41.107+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.107+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:41.139+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:22:41.172+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.172+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:41.246+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.151 seconds
[2025-06-04T03:23:12.749+0000] {processor.py:161} INFO - Started process (PID=825) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:12.751+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:23:12.758+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.758+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:12.795+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:12.829+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.828+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:12.877+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T03:23:45.150+0000] {processor.py:161} INFO - Started process (PID=834) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:45.151+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:23:45.154+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.153+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:45.181+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:23:45.215+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.214+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:45.262+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T03:24:16.425+0000] {processor.py:161} INFO - Started process (PID=843) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:16.427+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:24:16.430+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.430+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:16.473+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:16.513+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.513+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:16.590+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.169 seconds
[2025-06-04T03:24:46.766+0000] {processor.py:161} INFO - Started process (PID=852) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:46.768+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:24:46.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:46.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:46.795+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:24:46.827+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:46.827+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:48.115+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.110 seconds
[2025-06-04T03:25:20.408+0000] {processor.py:161} INFO - Started process (PID=861) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:20.409+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:25:20.412+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:20.412+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:20.440+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:20.476+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:20.475+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:20.539+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T03:25:52.682+0000] {processor.py:161} INFO - Started process (PID=869) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:52.683+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:25:52.686+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:52.685+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:52.716+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:25:52.749+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:52.749+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:52.795+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.118 seconds
[2025-06-04T03:26:23.910+0000] {processor.py:161} INFO - Started process (PID=878) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:23.911+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:26:23.916+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:23.915+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:23.945+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:24.001+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:24.001+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:24.071+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.166 seconds
[2025-06-04T03:26:56.220+0000] {processor.py:161} INFO - Started process (PID=887) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:56.223+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:26:56.226+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:56.225+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:56.259+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:26:56.293+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:56.292+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:56.342+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:27:27.515+0000] {processor.py:161} INFO - Started process (PID=896) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:27.517+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:27:27.520+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:27.519+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:27.553+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:27.591+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:27.591+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:27:27.633+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:27:59.909+0000] {processor.py:161} INFO - Started process (PID=905) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:59.913+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:27:59.919+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:59.918+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:59.948+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:27:59.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:59.983+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:00.029+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T03:28:31.332+0000] {processor.py:161} INFO - Started process (PID=913) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:28:31.334+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:28:31.336+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:31.336+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:28:31.369+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:28:31.401+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:31.401+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:31.790+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.465 seconds
[2025-06-04T03:29:03.420+0000] {processor.py:161} INFO - Started process (PID=922) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:03.421+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:29:03.425+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:03.424+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:03.454+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:03.494+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:03.493+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:03.539+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T03:29:33.638+0000] {processor.py:161} INFO - Started process (PID=930) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:33.640+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:29:33.642+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:33.642+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:33.678+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:29:33.715+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:33.715+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:33.765+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.131 seconds
[2025-06-04T03:30:04.319+0000] {processor.py:161} INFO - Started process (PID=939) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:04.323+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:30:04.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:04.325+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:04.354+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:04.389+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:04.388+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:04.452+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.137 seconds
[2025-06-04T03:30:34.551+0000] {processor.py:161} INFO - Started process (PID=948) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:34.556+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:30:34.558+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:34.558+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:34.589+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:30:34.626+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:34.626+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:34.672+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T03:31:04.890+0000] {processor.py:161} INFO - Started process (PID=957) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:04.891+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:31:04.895+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:04.895+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:04.922+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:04.966+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:04.965+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:05.015+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T03:31:35.213+0000] {processor.py:161} INFO - Started process (PID=966) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:35.216+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:31:35.219+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:35.219+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:35.243+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:31:35.281+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:35.281+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:35.334+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.126 seconds
[2025-06-04T03:32:05.569+0000] {processor.py:161} INFO - Started process (PID=975) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:05.571+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:32:05.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:05.573+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:05.608+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:05.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:05.648+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:05.700+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T03:32:35.854+0000] {processor.py:161} INFO - Started process (PID=984) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:35.856+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:32:35.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:35.859+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:35.885+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:32:35.928+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:35.928+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:35.978+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.128 seconds
[2025-06-04T03:33:06.305+0000] {processor.py:161} INFO - Started process (PID=993) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:06.306+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:33:06.313+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:06.313+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:06.343+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:06.378+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:06.378+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:06.428+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:33:36.687+0000] {processor.py:161} INFO - Started process (PID=1002) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:36.688+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:33:36.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:36.692+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:36.728+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:33:36.787+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:36.787+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:36.854+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.172 seconds
[2025-06-04T03:34:07.164+0000] {processor.py:161} INFO - Started process (PID=1011) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:07.170+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:34:07.174+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:07.173+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:07.198+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:07.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:07.236+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:07.287+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.126 seconds
[2025-06-04T03:34:37.351+0000] {processor.py:161} INFO - Started process (PID=1020) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:37.352+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:34:37.356+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:37.354+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:37.381+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:34:37.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:37.415+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:37.469+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:35:07.661+0000] {processor.py:161} INFO - Started process (PID=1028) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:07.663+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:35:07.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:07.666+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:07.689+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:07.725+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:07.724+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:07.777+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T03:35:38.047+0000] {processor.py:161} INFO - Started process (PID=1031) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:38.049+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:35:38.053+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:38.052+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:38.087+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:35:38.123+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:38.123+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:38.170+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.128 seconds
[2025-06-04T03:36:08.306+0000] {processor.py:161} INFO - Started process (PID=1040) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:08.311+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:36:08.314+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:08.313+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:08.338+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:08.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:08.370+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:36:08.426+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T03:36:38.765+0000] {processor.py:161} INFO - Started process (PID=1049) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:38.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:36:38.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:38.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:38.800+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:36:38.839+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:38.838+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:36:38.896+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.138 seconds
[2025-06-04T03:37:09.317+0000] {processor.py:161} INFO - Started process (PID=1058) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:09.319+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:37:09.322+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:09.322+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:09.354+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:09.392+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:09.391+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:09.447+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.137 seconds
[2025-06-04T03:37:39.738+0000] {processor.py:161} INFO - Started process (PID=1067) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:39.740+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:37:39.743+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:39.742+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:39.783+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:37:39.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:39.819+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:39.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.130 seconds
[2025-06-04T03:38:10.148+0000] {processor.py:161} INFO - Started process (PID=1076) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:10.149+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:38:10.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:10.156+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:10.194+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:10.237+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:10.237+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:10.285+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.142 seconds
[2025-06-04T03:38:40.657+0000] {processor.py:161} INFO - Started process (PID=1084) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:40.659+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:38:40.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:40.662+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:40.688+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:38:40.726+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:40.726+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:40.776+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T03:39:10.951+0000] {processor.py:161} INFO - Started process (PID=1093) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:10.953+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:39:10.955+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:10.955+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:10.986+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:11.021+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:11.021+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:11.077+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.130 seconds
[2025-06-04T03:39:41.308+0000] {processor.py:161} INFO - Started process (PID=1102) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:41.310+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:39:41.314+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.314+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:41.335+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:39:41.367+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.367+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:41.418+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.114 seconds
[2025-06-04T03:40:13.692+0000] {processor.py:161} INFO - Started process (PID=1111) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:13.695+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:40:13.698+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.697+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:13.740+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:13.777+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.776+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:13.824+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.137 seconds
[2025-06-04T03:40:45.223+0000] {processor.py:161} INFO - Started process (PID=1119) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:45.225+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:40:45.229+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.228+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:45.255+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:40:45.287+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.287+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:45.334+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T03:41:17.500+0000] {processor.py:161} INFO - Started process (PID=1127) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:17.502+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:41:17.505+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.504+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:17.528+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:17.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.562+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:17.611+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T03:41:49.006+0000] {processor.py:161} INFO - Started process (PID=1136) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:49.008+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:41:49.011+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.010+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:49.040+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:41:49.074+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.073+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:49.122+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T03:42:21.376+0000] {processor.py:161} INFO - Started process (PID=1145) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:21.380+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:42:21.382+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.382+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:21.410+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:21.446+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.446+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:21.496+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T03:42:53.065+0000] {processor.py:161} INFO - Started process (PID=1154) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:53.082+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:42:53.091+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:53.085+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:53.156+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:42:53.251+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:53.251+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:53.424+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.367 seconds
[2025-06-04T03:43:25.432+0000] {processor.py:161} INFO - Started process (PID=1162) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:25.436+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:43:25.439+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.438+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:25.470+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:25.511+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.510+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:25.560+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T03:43:56.911+0000] {processor.py:161} INFO - Started process (PID=1171) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:56.914+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:43:56.917+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:56.917+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:56.950+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:43:56.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:56.983+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:57.044+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.139 seconds
[2025-06-04T03:44:29.327+0000] {processor.py:161} INFO - Started process (PID=1180) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:44:29.328+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:44:29.331+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.331+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:44:29.372+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:44:29.427+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.427+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:44:29.508+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.185 seconds
[2025-06-04T03:45:00.788+0000] {processor.py:161} INFO - Started process (PID=1189) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:00.789+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:45:00.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.792+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:00.822+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:00.854+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.853+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:00.914+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.135 seconds
[2025-06-04T03:45:33.121+0000] {processor.py:161} INFO - Started process (PID=1198) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:33.123+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:45:33.127+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.126+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:33.154+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:45:33.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.187+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:33.240+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:46:03.372+0000] {processor.py:161} INFO - Started process (PID=1206) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:03.374+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:46:03.377+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:03.377+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:04.636+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:04.669+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.669+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:04.713+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.110 seconds
[2025-06-04T03:46:36.979+0000] {processor.py:161} INFO - Started process (PID=1215) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:36.980+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:46:36.985+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:36.985+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:37.012+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:46:37.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.068+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:37.131+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.157 seconds
[2025-06-04T03:47:08.861+0000] {processor.py:161} INFO - Started process (PID=1224) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:08.863+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:47:08.870+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:08.869+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:08.903+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:08.940+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:08.940+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:09.011+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.155 seconds
[2025-06-04T03:47:39.115+0000] {processor.py:161} INFO - Started process (PID=1233) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:39.117+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:47:39.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:39.121+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:39.158+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:47:39.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:39.194+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:40.483+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T03:48:13.015+0000] {processor.py:161} INFO - Started process (PID=1243) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:13.017+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:48:13.020+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:13.019+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:13.049+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:13.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:13.087+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:13.147+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.137 seconds
[2025-06-04T03:48:44.637+0000] {processor.py:161} INFO - Started process (PID=1252) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:44.640+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:48:44.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:44.644+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:44.665+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:48:44.702+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:44.701+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:44.856+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.223 seconds
[2025-06-04T03:49:17.004+0000] {processor.py:161} INFO - Started process (PID=1261) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:17.005+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:49:17.010+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:17.009+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:17.037+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:17.079+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:17.078+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:17.135+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T03:49:48.451+0000] {processor.py:161} INFO - Started process (PID=1270) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:48.452+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:49:48.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:48.456+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:48.483+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:49:48.517+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:48.517+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:48.569+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:50:20.953+0000] {processor.py:161} INFO - Started process (PID=1280) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:20.955+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:50:20.961+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:20.961+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:20.996+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:21.046+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:21.045+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:21.125+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.177 seconds
[2025-06-04T03:50:52.534+0000] {processor.py:161} INFO - Started process (PID=1290) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:52.537+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:50:52.541+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:52.541+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:52.570+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:50:52.603+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:52.603+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:52.650+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T03:51:24.953+0000] {processor.py:161} INFO - Started process (PID=1299) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:24.954+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:51:24.957+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:24.957+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:24.986+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:25.021+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:25.021+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:25.067+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T03:51:56.493+0000] {processor.py:161} INFO - Started process (PID=1308) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:56.497+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:51:56.500+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:56.499+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:56.529+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:51:56.571+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:56.571+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:56.622+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T03:52:28.987+0000] {processor.py:161} INFO - Started process (PID=1317) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:52:28.989+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:52:28.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:28.991+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:52:29.017+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:52:29.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:29.050+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:52:29.097+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T03:52:59.151+0000] {processor.py:161} INFO - Started process (PID=1327) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:52:59.152+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:52:59.155+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:59.154+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:52:59.182+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:53:00.417+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:00.417+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:00.465+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:53:32.832+0000] {processor.py:161} INFO - Started process (PID=1335) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:53:32.833+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:53:32.836+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:32.836+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:53:32.867+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:53:32.908+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:32.908+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:32.959+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T03:54:03.021+0000] {processor.py:161} INFO - Started process (PID=1344) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:03.024+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:54:03.027+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:03.026+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:03.053+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:03.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:03.086+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:04.374+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T03:54:36.798+0000] {processor.py:161} INFO - Started process (PID=1354) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:36.800+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:54:36.803+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:36.803+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:36.830+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:54:36.861+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:36.860+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:36.914+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T03:55:07.008+0000] {processor.py:161} INFO - Started process (PID=1363) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:07.012+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:55:07.016+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:07.016+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:07.047+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:07.098+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:07.097+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:08.400+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.166 seconds
[2025-06-04T03:55:40.671+0000] {processor.py:161} INFO - Started process (PID=1372) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:40.675+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:55:40.678+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.678+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:40.702+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:55:40.745+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.745+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:40.802+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T03:56:10.964+0000] {processor.py:161} INFO - Started process (PID=1381) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:10.968+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:56:10.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:10.970+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:10.993+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:11.029+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:11.027+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:11.081+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T03:56:41.613+0000] {processor.py:161} INFO - Started process (PID=1390) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:41.614+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:56:41.620+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:41.619+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:41.640+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:56:41.673+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:41.673+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:41.720+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.112 seconds
[2025-06-04T03:57:11.979+0000] {processor.py:161} INFO - Started process (PID=1399) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:11.981+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:57:11.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:11.983+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:12.012+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:12.052+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:12.052+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:12.118+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.143 seconds
[2025-06-04T03:57:42.405+0000] {processor.py:161} INFO - Started process (PID=1408) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:42.406+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:57:42.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:42.409+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:42.441+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:57:42.478+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:42.478+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:42.528+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.129 seconds
[2025-06-04T03:58:12.825+0000] {processor.py:161} INFO - Started process (PID=1417) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:12.827+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:58:12.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:12.837+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:12.872+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:12.926+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:12.926+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:12.982+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.173 seconds
[2025-06-04T03:58:43.419+0000] {processor.py:161} INFO - Started process (PID=1426) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:43.420+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:58:43.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:43.422+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:43.450+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:58:43.487+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:43.487+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:43.533+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T03:59:13.839+0000] {processor.py:161} INFO - Started process (PID=1435) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:13.843+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:59:13.846+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:13.845+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:13.876+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:13.907+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:13.907+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:13.958+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T03:59:44.211+0000] {processor.py:161} INFO - Started process (PID=1444) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:44.212+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T03:59:44.215+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:44.214+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:44.242+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T03:59:44.276+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:44.275+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:44.325+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T04:00:14.639+0000] {processor.py:161} INFO - Started process (PID=1452) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:14.641+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:00:14.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:14.643+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:14.669+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:14.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:14.712+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:14.853+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.218 seconds
[2025-06-04T04:00:45.192+0000] {processor.py:161} INFO - Started process (PID=1455) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:45.193+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:00:45.199+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:45.196+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:45.238+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:00:45.285+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:45.285+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:45.359+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.172 seconds
[2025-06-04T04:01:15.581+0000] {processor.py:161} INFO - Started process (PID=1465) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:15.582+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:01:15.584+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:15.584+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:15.611+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:15.650+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:15.649+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:15.713+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.138 seconds
[2025-06-04T04:01:46.012+0000] {processor.py:161} INFO - Started process (PID=1474) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:46.013+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:01:46.015+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:46.015+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:46.037+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:01:46.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:46.068+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:46.115+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.108 seconds
[2025-06-04T04:02:16.442+0000] {processor.py:161} INFO - Started process (PID=1483) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:16.444+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:02:16.446+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:16.446+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:16.475+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:16.510+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:16.509+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:16.560+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T04:02:47.001+0000] {processor.py:161} INFO - Started process (PID=1492) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:47.002+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:02:47.005+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:47.004+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:47.026+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:02:47.058+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:47.058+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:47.103+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.108 seconds
[2025-06-04T04:03:17.589+0000] {processor.py:161} INFO - Started process (PID=1501) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:17.593+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:03:17.601+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:17.600+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:17.627+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:17.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:17.662+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:17.706+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T04:03:47.999+0000] {processor.py:161} INFO - Started process (PID=1510) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:48.000+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:03:48.003+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:48.002+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:48.032+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:03:48.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:48.068+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:48.123+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.128 seconds
[2025-06-04T04:04:18.702+0000] {processor.py:161} INFO - Started process (PID=1519) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:18.706+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:04:18.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:18.713+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:18.743+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:18.776+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:18.775+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:04:18.823+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.140 seconds
[2025-06-04T04:04:49.226+0000] {processor.py:161} INFO - Started process (PID=1528) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:49.227+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:04:49.230+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:49.229+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:49.259+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:04:49.291+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:04:49.290+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:04:49.337+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.115 seconds
[2025-06-04T04:05:19.645+0000] {processor.py:161} INFO - Started process (PID=1537) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:19.647+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:05:19.649+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:19.649+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:19.681+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:19.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:19.713+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:05:19.764+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T04:05:50.162+0000] {processor.py:161} INFO - Started process (PID=1546) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:50.164+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:05:50.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:50.166+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:50.192+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:05:50.221+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:05:50.221+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:05:50.267+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.110 seconds
[2025-06-04T04:06:20.853+0000] {processor.py:161} INFO - Started process (PID=1555) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:20.859+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:06:20.865+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:20.864+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:20.912+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:20.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:20.954+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:06:21.006+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.159 seconds
[2025-06-04T04:06:53.503+0000] {processor.py:161} INFO - Started process (PID=1564) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:53.514+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:06:53.518+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:53.517+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:53.556+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:06:53.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:06:53.595+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:06:53.655+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.156 seconds
[2025-06-04T04:07:24.969+0000] {processor.py:161} INFO - Started process (PID=1572) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:24.971+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:07:24.974+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:24.974+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:25.111+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:25.151+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:25.151+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:07:25.197+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.233 seconds
[2025-06-04T04:07:57.472+0000] {processor.py:161} INFO - Started process (PID=1581) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:57.473+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:07:57.477+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:57.476+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:57.505+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:07:57.539+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:07:57.539+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:07:57.585+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.118 seconds
[2025-06-04T04:08:28.935+0000] {processor.py:161} INFO - Started process (PID=1590) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:08:28.937+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:08:28.940+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:08:28.940+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:08:28.967+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:08:29.002+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:08:29.002+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:08:29.222+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.293 seconds
[2025-06-04T04:09:00.945+0000] {processor.py:161} INFO - Started process (PID=1600) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:00.946+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:09:00.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:00.948+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:00.970+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:01.004+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:01.004+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:09:01.052+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.111 seconds
[2025-06-04T04:09:33.397+0000] {processor.py:161} INFO - Started process (PID=1609) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:33.403+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:09:33.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:33.407+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:33.443+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:09:33.504+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:09:33.504+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:09:33.571+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.179 seconds
[2025-06-04T04:10:05.039+0000] {processor.py:161} INFO - Started process (PID=1618) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:05.044+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:10:05.047+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:05.046+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:05.074+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:05.108+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:05.108+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:10:05.161+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.126 seconds
[2025-06-04T04:10:37.346+0000] {processor.py:161} INFO - Started process (PID=1626) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:37.348+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:10:37.351+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:37.350+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:37.376+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:10:37.420+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:10:37.419+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:10:37.475+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.134 seconds
[2025-06-04T04:11:09.590+0000] {processor.py:161} INFO - Started process (PID=1636) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:09.592+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:11:09.596+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:09.595+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:09.624+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:09.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:09.659+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:11:09.710+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T04:11:41.041+0000] {processor.py:161} INFO - Started process (PID=1644) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:41.046+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:11:41.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:41.051+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:41.081+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:11:41.123+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:11:41.123+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:11:41.170+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.135 seconds
[2025-06-04T04:12:13.534+0000] {processor.py:161} INFO - Started process (PID=1653) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:13.536+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:12:13.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:13.541+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:13.573+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:13.621+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:13.621+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:12:13.682+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.155 seconds
[2025-06-04T04:12:45.143+0000] {processor.py:161} INFO - Started process (PID=1662) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:45.146+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:12:45.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:45.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:45.182+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:12:45.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:12:45.223+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:12:45.272+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T04:13:17.727+0000] {processor.py:161} INFO - Started process (PID=1671) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:17.729+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:13:17.733+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:17.732+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:17.755+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:17.791+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:17.791+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:13:17.842+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.119 seconds
[2025-06-04T04:13:49.163+0000] {processor.py:161} INFO - Started process (PID=1680) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:49.165+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:13:49.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:49.168+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:49.203+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:13:49.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:13:49.236+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:13:49.298+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.139 seconds
[2025-06-04T04:14:21.796+0000] {processor.py:161} INFO - Started process (PID=1689) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:21.798+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:14:21.800+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:21.800+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:21.824+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:21.856+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:21.856+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:14:21.903+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.111 seconds
[2025-06-04T04:14:53.186+0000] {processor.py:161} INFO - Started process (PID=1697) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:53.188+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:14:53.192+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:53.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:53.224+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:14:53.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:14:53.262+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:14:53.334+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.154 seconds
[2025-06-04T04:15:25.695+0000] {processor.py:161} INFO - Started process (PID=1706) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:25.700+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:15:25.703+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:25.702+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:25.731+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:25.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:25.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:15:25.839+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.150 seconds
[2025-06-04T04:15:57.167+0000] {processor.py:161} INFO - Started process (PID=1714) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:57.168+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:15:57.171+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:57.170+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:57.210+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:15:57.247+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:15:57.246+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:15:57.294+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T04:16:29.809+0000] {processor.py:161} INFO - Started process (PID=1723) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:16:29.812+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:16:29.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:16:29.814+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:16:29.846+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:16:29.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:16:29.878+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:16:29.925+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T04:17:01.259+0000] {processor.py:161} INFO - Started process (PID=1732) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:01.261+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:17:01.264+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:01.263+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:01.316+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:01.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:01.369+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:17:01.429+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.175 seconds
[2025-06-04T04:17:33.818+0000] {processor.py:161} INFO - Started process (PID=1740) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:33.820+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:17:33.823+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:33.822+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:33.854+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:17:33.897+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:17:33.897+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:17:33.985+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.173 seconds
[2025-06-04T04:18:05.412+0000] {processor.py:161} INFO - Started process (PID=1749) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:05.416+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:18:05.418+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:05.418+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:05.444+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:05.481+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:05.480+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:18:05.528+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T04:18:38.545+0000] {processor.py:161} INFO - Started process (PID=1758) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:38.559+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:18:38.569+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:38.569+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:38.951+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:18:39.139+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:18:39.139+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:18:39.931+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 1.398 seconds
[2025-06-04T04:27:15.567+0000] {processor.py:161} INFO - Started process (PID=62) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:15.592+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:27:15.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:15.595+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:15.672+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:15.789+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:15.788+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:27:15.885+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.330 seconds
[2025-06-04T04:27:46.697+0000] {processor.py:161} INFO - Started process (PID=71) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:46.700+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:27:46.706+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:46.706+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:46.739+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:27:46.780+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:27:46.779+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:27:46.836+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.154 seconds
[2025-06-04T04:28:17.073+0000] {processor.py:161} INFO - Started process (PID=80) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:17.076+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:28:17.079+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:17.079+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:17.105+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:17.150+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:17.150+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:28:17.283+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.213 seconds
[2025-06-04T04:28:47.624+0000] {processor.py:161} INFO - Started process (PID=89) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:47.625+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:28:47.628+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:47.627+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:47.660+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:28:47.692+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:28:47.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:28:47.756+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.137 seconds
[2025-06-04T04:29:18.412+0000] {processor.py:161} INFO - Started process (PID=98) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:18.413+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:29:18.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:18.418+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:18.453+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:18.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:18.500+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:29:18.575+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.167 seconds
[2025-06-04T04:29:49.867+0000] {processor.py:161} INFO - Started process (PID=107) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:49.868+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:29:49.872+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:49.870+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:49.941+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:29:50.014+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:29:50.013+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:29:50.071+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.208 seconds
[2025-06-04T04:30:21.340+0000] {processor.py:161} INFO - Started process (PID=116) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:30:21.342+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:30:21.345+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:30:21.345+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:30:21.503+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:30:21.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:30:21.547+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:30:21.593+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.259 seconds
[2025-06-04T04:31:36.806+0000] {processor.py:161} INFO - Started process (PID=58) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:31:36.814+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:31:36.817+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:31:36.816+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:31:36.870+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:31:37.373+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:31:37.372+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:31:37.588+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.795 seconds
[2025-06-04T04:32:08.176+0000] {processor.py:161} INFO - Started process (PID=61) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:08.186+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:32:08.190+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:08.189+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:08.244+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:08.354+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:08.354+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:32:08.896+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.734 seconds
[2025-06-04T04:32:39.533+0000] {processor.py:161} INFO - Started process (PID=69) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:39.561+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:32:39.593+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:39.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:39.695+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:32:39.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:32:39.733+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:32:39.848+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.334 seconds
[2025-06-04T04:33:10.130+0000] {processor.py:161} INFO - Started process (PID=78) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:10.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:33:10.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:10.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:10.188+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:10.222+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:10.222+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:33:10.269+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.145 seconds
[2025-06-04T04:33:40.496+0000] {processor.py:161} INFO - Started process (PID=87) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:40.499+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:33:40.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:40.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:40.575+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:33:40.626+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:33:40.619+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:33:40.668+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.189 seconds
[2025-06-04T04:34:10.775+0000] {processor.py:161} INFO - Started process (PID=96) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:10.776+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:34:10.779+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:10.779+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:10.799+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:10.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:10.833+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:34:10.874+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.106 seconds
[2025-06-04T04:34:41.124+0000] {processor.py:161} INFO - Started process (PID=105) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:41.126+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:34:41.128+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:41.128+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:41.153+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:34:41.185+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:34:41.185+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:34:41.268+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.148 seconds
[2025-06-04T04:35:11.862+0000] {processor.py:161} INFO - Started process (PID=114) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:11.878+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:35:11.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:11.898+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:12.021+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:12.123+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:12.113+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:35:12.273+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.414 seconds
[2025-06-04T04:35:43.222+0000] {processor.py:161} INFO - Started process (PID=123) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:43.248+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:35:43.271+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:43.271+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:43.349+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:35:43.387+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:35:43.387+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:35:43.511+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.294 seconds
[2025-06-04T04:36:13.742+0000] {processor.py:161} INFO - Started process (PID=132) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:13.744+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:36:13.747+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:13.746+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:13.795+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:13.837+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:13.836+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:36:13.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:13.874+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:36:13.911+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.174 seconds
[2025-06-04T04:36:45.236+0000] {processor.py:161} INFO - Started process (PID=141) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:45.238+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:36:45.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:45.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:45.269+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:36:45.306+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:45.306+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:36:45.337+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:36:45.336+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:36:45.360+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.128 seconds
[2025-06-04T04:37:16.800+0000] {processor.py:161} INFO - Started process (PID=150) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:16.802+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:37:16.804+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:16.804+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:16.833+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:16.869+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:16.869+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:37:16.894+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:16.893+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:37:16.918+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T04:37:49.235+0000] {processor.py:161} INFO - Started process (PID=159) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:49.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:37:49.244+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:49.243+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:49.275+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:37:49.312+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:49.311+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:37:49.347+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:37:49.346+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:37:49.377+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.147 seconds
[2025-06-04T04:38:20.606+0000] {processor.py:161} INFO - Started process (PID=168) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:20.608+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:38:20.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:20.610+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:20.633+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:20.665+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:20.664+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:38:20.687+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:20.687+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:38:20.709+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.107 seconds
[2025-06-04T04:38:50.855+0000] {processor.py:161} INFO - Started process (PID=177) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:50.856+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:38:50.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:50.858+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:50.885+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:38:50.918+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:50.917+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:38:50.942+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:38:50.942+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:38:50.974+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.125 seconds
[2025-06-04T04:39:21.156+0000] {processor.py:161} INFO - Started process (PID=185) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:21.158+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:39:21.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.166+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:21.218+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:21.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.282+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:39:21.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:21.325+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:39:21.356+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.205 seconds
[2025-06-04T04:39:51.746+0000] {processor.py:161} INFO - Started process (PID=194) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:51.749+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:39:51.751+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.751+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:51.775+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:39:51.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.809+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:39:51.836+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:39:51.835+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:39:51.858+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.118 seconds
[2025-06-04T04:40:22.611+0000] {processor.py:161} INFO - Started process (PID=203) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:22.613+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:40:22.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.617+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:22.641+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:22.680+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.680+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:40:22.710+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:22.710+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:40:22.734+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T04:40:53.065+0000] {processor.py:161} INFO - Started process (PID=213) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:53.067+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:40:53.072+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.069+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:53.100+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:40:53.134+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.134+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:40:53.161+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:40:53.161+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:40:53.184+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T04:41:23.743+0000] {processor.py:161} INFO - Started process (PID=222) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:23.747+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:41:23.757+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.756+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:23.816+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:23.856+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.856+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:41:23.881+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:23.881+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:41:23.910+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.172 seconds
[2025-06-04T04:41:54.495+0000] {processor.py:161} INFO - Started process (PID=231) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:54.496+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:41:54.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:54.542+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:41:54.602+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.601+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:41:54.634+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:41:54.634+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:41:54.663+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.172 seconds
[2025-06-04T04:42:24.857+0000] {processor.py:161} INFO - Started process (PID=240) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:24.858+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:42:24.862+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.862+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:24.892+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:24.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.931+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:42:24.959+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:24.959+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:42:24.983+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.131 seconds
[2025-06-04T04:42:55.328+0000] {processor.py:161} INFO - Started process (PID=249) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:55.330+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:42:55.335+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.334+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:55.372+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:42:55.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.409+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:42:55.441+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:42:55.441+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:42:55.461+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.139 seconds
[2025-06-04T04:43:25.958+0000] {processor.py:161} INFO - Started process (PID=258) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:25.965+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:43:25.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:25.972+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:26.014+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:26.061+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:26.061+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:43:26.100+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:26.100+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:43:26.144+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.191 seconds
[2025-06-04T04:43:56.404+0000] {processor.py:161} INFO - Started process (PID=267) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:56.406+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:43:56.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.408+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:56.430+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:43:56.472+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.472+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:43:56.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:43:56.506+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:43:56.542+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.143 seconds
[2025-06-04T04:44:26.697+0000] {processor.py:161} INFO - Started process (PID=275) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:26.698+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:44:26.706+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.706+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:26.761+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:26.835+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.835+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:44:26.894+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:26.894+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:44:26.968+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.276 seconds
[2025-06-04T04:44:57.797+0000] {processor.py:161} INFO - Started process (PID=278) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:57.799+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:44:57.813+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:57.812+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:57.864+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:44:57.916+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:57.916+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:44:57.947+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:44:57.946+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:44:57.969+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.178 seconds
[2025-06-04T04:45:28.405+0000] {processor.py:161} INFO - Started process (PID=287) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:28.406+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:45:28.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:28.408+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:28.435+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:28.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:28.466+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:45:28.489+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:28.489+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:45:28.515+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T04:45:58.905+0000] {processor.py:161} INFO - Started process (PID=296) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:58.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:45:58.910+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:58.909+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:58.940+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:45:58.975+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:58.975+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:45:59.002+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:45:59.001+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:45:59.025+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.124 seconds
[2025-06-04T04:46:29.395+0000] {processor.py:161} INFO - Started process (PID=305) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:46:29.396+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:46:29.399+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:29.398+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:46:29.430+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:46:29.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:29.468+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:46:29.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:29.502+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:46:29.524+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.136 seconds
[2025-06-04T04:46:59.943+0000] {processor.py:161} INFO - Started process (PID=314) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:46:59.945+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:46:59.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:46:59.948+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:46:59.978+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:47:00.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:00.012+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:47:00.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:00.042+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:47:00.064+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T04:47:30.495+0000] {processor.py:161} INFO - Started process (PID=323) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:47:30.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:47:30.506+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:30.504+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:47:30.573+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:47:30.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:30.627+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:47:30.688+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:47:30.688+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:47:30.729+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.241 seconds
[2025-06-04T04:48:01.160+0000] {processor.py:161} INFO - Started process (PID=332) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:01.164+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:48:01.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:01.166+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:01.192+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:01.231+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:01.230+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:48:01.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:01.281+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:48:01.327+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.172 seconds
[2025-06-04T04:48:31.694+0000] {processor.py:161} INFO - Started process (PID=341) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:31.696+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:48:31.698+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:31.698+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:31.719+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:48:31.754+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:31.754+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:48:31.778+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:48:31.778+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:48:31.801+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.113 seconds
[2025-06-04T04:49:03.183+0000] {processor.py:161} INFO - Started process (PID=350) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:03.184+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:49:03.190+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:03.189+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:03.217+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:03.247+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:03.247+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:49:03.272+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:03.272+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:49:03.297+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.118 seconds
[2025-06-04T04:49:35.736+0000] {processor.py:161} INFO - Started process (PID=359) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:35.738+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:49:35.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:35.744+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:35.769+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:49:35.829+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:35.829+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:49:35.867+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:49:35.866+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:49:35.895+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.164 seconds
[2025-06-04T04:50:07.366+0000] {processor.py:161} INFO - Started process (PID=368) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:07.368+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:50:07.374+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:07.374+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:07.402+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:07.441+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:07.441+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:50:07.474+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:07.473+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:50:07.497+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.138 seconds
[2025-06-04T04:50:38.650+0000] {processor.py:161} INFO - Started process (PID=377) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:38.652+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:50:38.655+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.654+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:38.679+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:50:38.712+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.711+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:50:38.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:50:38.735+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:50:38.761+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.116 seconds
[2025-06-04T04:51:09.110+0000] {processor.py:161} INFO - Started process (PID=386) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:09.111+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:51:09.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.114+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:09.150+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:09.190+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.190+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:51:09.213+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:09.213+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:51:09.239+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.134 seconds
[2025-06-04T04:51:40.117+0000] {processor.py:161} INFO - Started process (PID=395) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:40.118+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:51:40.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.121+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:40.144+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:51:40.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.179+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:51:40.203+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:51:40.202+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:51:40.225+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.113 seconds
[2025-06-04T04:52:10.719+0000] {processor.py:161} INFO - Started process (PID=404) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:10.720+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:52:10.723+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.723+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:10.753+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:10.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:52:10.807+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:10.807+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:52:10.831+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T04:52:41.388+0000] {processor.py:161} INFO - Started process (PID=413) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:41.389+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:52:41.393+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.392+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:41.417+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:52:41.457+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.456+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:52:41.483+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:52:41.483+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:52:41.503+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T04:53:11.982+0000] {processor.py:161} INFO - Started process (PID=422) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:11.983+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:53:11.986+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:11.985+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:12.008+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:12.048+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:12.048+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:53:12.078+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:12.077+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:53:12.111+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.133 seconds
[2025-06-04T04:53:43.066+0000] {processor.py:161} INFO - Started process (PID=431) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:43.067+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:53:43.070+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.069+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:43.094+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:53:43.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.125+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:53:43.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:53:43.156+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:53:43.182+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.122 seconds
[2025-06-04T04:54:13.958+0000] {processor.py:161} INFO - Started process (PID=439) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:13.960+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:54:13.964+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:13.963+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:13.993+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:14.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:14.024+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:54:14.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:14.049+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:54:14.070+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T04:54:44.490+0000] {processor.py:161} INFO - Started process (PID=448) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:44.493+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:54:44.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.495+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:44.526+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:54:44.563+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.562+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:54:44.590+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:54:44.590+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:54:44.617+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.132 seconds
[2025-06-04T04:55:15.202+0000] {processor.py:161} INFO - Started process (PID=457) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:15.204+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:55:15.206+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:15.206+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:15.228+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:15.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:15.257+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:55:15.283+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:15.283+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:55:15.302+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.107 seconds
[2025-06-04T04:55:45.928+0000] {processor.py:161} INFO - Started process (PID=466) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:45.929+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:55:45.937+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:45.937+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:45.968+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:55:45.999+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:45.999+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:55:46.025+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:55:46.024+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:55:46.051+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.127 seconds
[2025-06-04T04:56:16.575+0000] {processor.py:161} INFO - Started process (PID=469) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:16.576+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:56:16.578+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:16.578+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:16.605+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:16.641+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:16.640+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:56:16.668+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:16.668+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:56:16.690+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T04:56:47.202+0000] {processor.py:161} INFO - Started process (PID=478) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:47.204+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:56:47.206+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:47.206+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:47.231+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:56:47.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:47.260+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:56:47.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:56:47.284+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:56:47.306+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.110 seconds
[2025-06-04T04:57:17.768+0000] {processor.py:161} INFO - Started process (PID=487) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:17.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:57:17.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:17.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:17.793+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:17.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:17.822+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:57:17.844+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:17.844+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:57:17.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.100 seconds
[2025-06-04T04:57:48.413+0000] {processor.py:161} INFO - Started process (PID=496) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:48.415+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:57:48.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:48.419+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:48.443+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:57:48.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:48.475+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:57:48.497+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:57:48.497+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:57:48.518+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.114 seconds
[2025-06-04T04:58:19.031+0000] {processor.py:161} INFO - Started process (PID=505) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:19.032+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:58:19.035+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:19.035+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:19.062+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:19.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:19.095+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:58:19.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:19.121+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:58:19.146+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.120 seconds
[2025-06-04T04:58:49.703+0000] {processor.py:161} INFO - Started process (PID=514) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:49.705+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:58:49.707+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:49.707+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:49.729+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:58:49.761+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:49.761+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:58:49.786+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:58:49.786+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:58:49.809+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.111 seconds
[2025-06-04T04:59:20.343+0000] {processor.py:161} INFO - Started process (PID=523) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:20.345+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:59:20.348+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:20.347+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:20.375+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:20.407+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:20.407+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:59:20.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:20.434+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:59:20.460+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.121 seconds
[2025-06-04T04:59:51.028+0000] {processor.py:161} INFO - Started process (PID=532) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:51.030+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T04:59:51.033+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:51.032+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:51.055+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T04:59:51.087+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:51.086+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:59:51.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:59:51.112+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T04:59:51.142+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.117 seconds
[2025-06-04T05:00:21.671+0000] {processor.py:161} INFO - Started process (PID=541) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:21.673+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:00:21.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:21.675+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:21.703+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:21.743+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:21.743+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:00:21.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:21.966+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:00:21.988+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.323 seconds
[2025-06-04T05:00:52.507+0000] {processor.py:161} INFO - Started process (PID=550) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:52.509+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:00:52.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.512+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:52.540+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:00:52.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.573+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:00:52.599+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:00:52.599+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:00:52.640+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.140 seconds
[2025-06-04T05:01:23.142+0000] {processor.py:161} INFO - Started process (PID=559) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:23.144+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:01:23.146+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:23.146+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:23.178+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:23.218+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:23.217+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:01:24.816+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:24.815+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:01:24.849+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.322 seconds
[2025-06-04T05:01:55.396+0000] {processor.py:161} INFO - Started process (PID=568) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:55.397+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:01:55.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.399+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:55.432+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:01:55.474+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.473+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:01:55.498+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:01:55.497+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:01:57.057+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.286 seconds
[2025-06-04T05:02:29.607+0000] {processor.py:161} INFO - Started process (PID=577) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:02:29.609+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:02:29.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.612+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:02:29.641+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:02:29.875+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.874+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:02:29.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:02:29.902+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:02:29.931+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.329 seconds
[2025-06-04T05:03:01.555+0000] {processor.py:161} INFO - Started process (PID=587) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:01.556+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:03:01.558+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:01.558+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:01.584+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:01.618+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:01.618+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:03:01.788+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:01.788+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:03:01.918+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.368 seconds
[2025-06-04T05:03:33.764+0000] {processor.py:161} INFO - Started process (PID=596) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:33.765+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:03:33.767+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:33.767+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:33.927+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:03:33.951+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:33.950+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:03:33.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:03:33.971+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:03:33.991+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.230 seconds
[2025-06-04T05:04:06.446+0000] {processor.py:161} INFO - Started process (PID=605) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:06.448+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:04:06.450+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:06.450+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:06.476+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:06.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:06.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:04:06.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:06.540+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:04:06.564+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.123 seconds
[2025-06-04T05:04:36.734+0000] {processor.py:161} INFO - Started process (PID=615) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:36.736+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:04:36.738+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:36.738+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:36.762+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:04:36.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:36.792+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:04:36.816+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:04:36.816+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:04:36.838+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.108 seconds
[2025-06-04T05:05:07.499+0000] {processor.py:161} INFO - Started process (PID=625) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:07.501+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:05:07.504+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:07.503+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:07.527+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:07.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:07.561+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:05:07.588+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:07.588+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:05:07.613+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.118 seconds
[2025-06-04T05:05:38.208+0000] {processor.py:161} INFO - Started process (PID=634) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:38.209+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:05:38.212+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:38.211+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:38.234+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:05:38.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:38.261+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:05:38.283+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:05:38.283+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:05:38.304+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.100 seconds
[2025-06-04T05:06:08.916+0000] {processor.py:161} INFO - Started process (PID=643) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:08.918+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:06:08.920+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:08.920+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:08.942+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:08.976+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:08.975+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:06:08.998+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:08.998+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:06:09.020+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.108 seconds
[2025-06-04T05:06:39.829+0000] {processor.py:161} INFO - Started process (PID=652) to work on /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:39.830+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/monitoring_dag.py for tasks to queue
[2025-06-04T05:06:39.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:39.834+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:39.855+0000] {processor.py:840} INFO - DAG(s) 'etl_monitoring_hourly' retrieved from /opt/airflow/dags/monitoring_dag.py
[2025-06-04T05:06:39.886+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:39.886+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T05:06:39.908+0000] {logging_mixin.py:188} INFO - [2025-06-04T05:06:39.908+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_monitoring_hourly to 2025-06-03 20:00:00+00:00, run_after=2025-06-03 21:00:00+00:00
[2025-06-04T05:06:39.927+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/monitoring_dag.py took 0.102 seconds
