version: 2

models:
  - name: test_get_single_value
    tests:
      - assert_equal:
          actual: date_actual
          expected: date_expected
      - assert_equal:
          actual: float_actual
          expected: float_expected
      - assert_equal:
          actual: int_actual
          expected: int_expected
      - assert_equal:
          actual: string_actual
          expected: string_expected

  - name: test_get_single_value_default
    tests:
      - assert_equal:
          actual: date_actual
          expected: date_expected
      - assert_equal:
          actual: float_actual
          expected: float_expected
      - assert_equal:
          actual: int_actual
          expected: int_expected
      - assert_equal:
          actual: string_actual
          expected: string_expected

  - name: test_generate_series
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_generate_series')

  - name: test_get_column_values
    columns:
      - name: count_a
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_b
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_c
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_d
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_e
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_f
        tests:
          - accepted_values:
              values:
                - '1'

      - name: count_g
        tests:
          - accepted_values:
              values:
                - '5'

  - name: test_get_column_values_where
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_get_column_values_where_expected')

  - name: test_get_filtered_columns_in_relation
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_filtered_columns_in_relation_expected')

  - name: test_get_relations_by_prefix_and_union
    columns:
      - name: event
        tests:
          - not_null
      - name: user_id
        tests:
          - dbt_utils.at_least_one
          - not_null
          - unique

  - name: test_nullcheck_table
    columns:
      - name: field_1
        tests:
          - not_empty_string

      - name: field_2
        tests:
          - not_empty_string

      - name: field_3
        tests:
          - not_empty_string

  - name: test_safe_add
    tests:
      - assert_equal:
          actual: actual
          expected: expected
  
  - name: test_safe_subtract
    tests:
      - assert_equal:
          actual: actual
          expected: expected

  - name: test_safe_divide
    tests:
      - assert_equal:
          actual: actual
          expected: expected

  - name: test_pivot
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_pivot_expected')

  - name: test_pivot_apostrophe
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_pivot_expected_apostrophe')

  - name: test_unpivot_original_api
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_unpivot_original_api_expected')

  - name: test_unpivot
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_unpivot_expected')

  - name: test_unpivot_bool
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_unpivot_bool_expected')

  - name: test_star
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_star_expected')

  - name: test_star_quote_identifiers
    tests:
      - assert_equal:
          actual: actual
          expected: expected

  - name: test_star_prefix_suffix
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_star_prefix_suffix_expected')

  - name: test_star_aggregate
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_star_aggregate_expected')

  - name: test_star_uppercase
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_star_expected')

  - name: test_star_no_columns
    columns: 
      - name: canary_column #If the no-columns state isn't hit, this table won't be queryable because there will be a missing comma
        tests: 
          - not_null

  - name: test_generate_surrogate_key
    tests:
      - assert_equal:
          actual: actual_column_1_only
          expected: expected_column_1_only
      - assert_equal:
          actual: actual_all_columns_list
          expected: expected_all_columns

  - name: test_union
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_union_expected')

  - name: test_union_where
    columns:
      - name: id
        tests:
          - dbt_utils.expression_is_true:
              expression: "= 1"
      - name: favorite_number
        tests:
          - dbt_utils.not_constant
  
  - name: test_union_no_source_column
    tests:
      - expect_table_columns_to_match_set:
          column_list: ["id", "name", "favorite_color", "favorite_number"]

  - name: test_union_exclude_lowercase
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_union_exclude_expected')

  - name: test_union_exclude_uppercase
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_union_exclude_expected')

  - name: test_get_relations_by_pattern
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_union_events_expected')

  - name: test_deduplicate
    tests:
      - dbt_utils.equality:
          compare_model: ref('data_deduplicate_expected')

  - name: test_not_empty_string_failing
    columns:
      - name: string_trim_whitespace_true
        tests:
          - dbt_utils.not_empty_string:
              config:
                severity: error
                error_if: "<1"
                warn_if: "<0"

  - name: test_not_empty_string_passing
    columns:
      - name: string_trim_whitespace_true
        tests:
          - dbt_utils.not_empty_string
      - name: string_trim_whitespace_false
        tests:
          - dbt_utils.not_empty_string:
              trim_whitespace: false

  - name: test_width_bucket
    tests:
      - assert_equal:
          actual: actual
          expected: expected
