"""
Simple ETL Pipeline DAG for Local Airflow
Orchestrates the S3 dbt-Snowflake C360 Pipeline
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
import os
import subprocess
import logging

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'catchup': False,
}

# DAG definition
dag = DAG(
    'simple_etl_pipeline',
    default_args=default_args,
    description='Simple ETL Pipeline for Local Development',
    schedule_interval='@daily',  # Daily at midnight
    max_active_runs=1,
    tags=['etl', 'simple', 'local'],
)

def get_workspace_path():
    """Get the workspace path"""
    # In Docker container, workspace is mounted at /opt/airflow/workspace
    return "/opt/airflow/workspace"

def run_python_script(script_name, **context):
    """Run a Python script in the workspace"""
    workspace_path = get_workspace_path()
    script_path = os.path.join(workspace_path, script_name)
    
    logging.info(f"Running script: {script_path}")
    
    if not os.path.exists(script_path):
        logging.error(f"Script not found: {script_path}")
        raise FileNotFoundError(f"Script not found: {script_path}")
    
    # Change to workspace directory
    original_cwd = os.getcwd()
    os.chdir(workspace_path)
    
    try:
        result = subprocess.run([
            'python', script_name
        ], capture_output=True, text=True, cwd=workspace_path)
        
        if result.returncode != 0:
            logging.error(f"Script failed: {result.stderr}")
            raise Exception(f"Script failed: {result.stderr}")
        
        logging.info(f"Script completed successfully: {result.stdout}")
        return result.stdout
        
    finally:
        os.chdir(original_cwd)

def run_dbt_command(command, **context):
    """Run a dbt command"""
    workspace_path = get_workspace_path()
    dbt_path = os.path.join(workspace_path, "dbt_live")
    
    logging.info(f"Running dbt command: {command}")
    
    if not os.path.exists(dbt_path):
        logging.error(f"dbt project not found: {dbt_path}")
        raise FileNotFoundError(f"dbt project not found: {dbt_path}")
    
    # Change to dbt directory
    original_cwd = os.getcwd()
    os.chdir(dbt_path)
    
    try:
        result = subprocess.run(
            command.split(), 
            capture_output=True, 
            text=True, 
            cwd=dbt_path
        )
        
        if result.returncode != 0:
            logging.error(f"dbt command failed: {result.stderr}")
            raise Exception(f"dbt command failed: {result.stderr}")
        
        logging.info(f"dbt command completed: {result.stdout}")
        return result.stdout
        
    finally:
        os.chdir(original_cwd)

# Task functions
def generate_data(**context):
    """Generate daily data"""
    return run_python_script('simple_data_generator.py', **context)

def refresh_stages(**context):
    """Refresh Snowflake stages"""
    workspace_path = get_workspace_path()

    logging.info("Refreshing Snowflake external stages...")

    # Change to workspace directory
    original_cwd = os.getcwd()
    os.chdir(workspace_path)

    try:
        # Import and call the Airflow-specific function
        import sys
        sys.path.append(workspace_path)

        from create_snowflake_stages import setup_stages_for_airflow

        result = setup_stages_for_airflow()

        logging.info(f"Stages refreshed successfully: {result}")
        return f"Stage refresh successful: {result}"

    except Exception as e:
        logging.error(f"Stage refresh failed: {e}")
        raise Exception(f"Stage refresh failed: {e}")

    finally:
        os.chdir(original_cwd)

def run_dbt_models(**context):
    """Run dbt models"""
    return run_dbt_command('dbt run', **context)

def run_dbt_tests(**context):
    """Run dbt tests"""
    return run_dbt_command('dbt test', **context)

def run_monitoring(**context):
    """Run monitoring models"""
    return run_dbt_command('dbt run --models monitoring', **context)

def show_health_dashboard(**context):
    """Show health dashboard"""
    return run_python_script('show_etl_health_dashboard.py', **context)

def validate_results(**context):
    """Validate pipeline results"""
    return run_python_script('check_snowflake_dbt_tables.py', **context)

# Task definitions
start_task = DummyOperator(
    task_id='start_pipeline',
    dag=dag,
)

generate_data_task = PythonOperator(
    task_id='generate_daily_data',
    python_callable=generate_data,
    dag=dag,
)

refresh_stages_task = PythonOperator(
    task_id='refresh_snowflake_stages',
    python_callable=refresh_stages,
    dag=dag,
)

dbt_run_task = PythonOperator(
    task_id='run_dbt_models',
    python_callable=run_dbt_models,
    dag=dag,
)

dbt_test_task = PythonOperator(
    task_id='run_dbt_tests',
    python_callable=run_dbt_tests,
    dag=dag,
)

monitoring_task = PythonOperator(
    task_id='run_monitoring_models',
    python_callable=run_monitoring,
    dag=dag,
)

dashboard_task = PythonOperator(
    task_id='show_health_dashboard',
    python_callable=show_health_dashboard,
    dag=dag,
)

validate_task = PythonOperator(
    task_id='validate_results',
    python_callable=validate_results,
    dag=dag,
)

end_task = DummyOperator(
    task_id='end_pipeline',
    dag=dag,
)

# Task dependencies
start_task >> generate_data_task >> refresh_stages_task >> dbt_run_task
dbt_run_task >> dbt_test_task >> monitoring_task >> dashboard_task >> validate_task >> end_task
