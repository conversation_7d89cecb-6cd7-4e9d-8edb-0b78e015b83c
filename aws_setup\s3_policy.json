{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["s3:GetObject", "s3:GetObjectVersion", "s3:ListBucket", "s3:GetBucketLocation"], "Resource": ["arn:aws:s3:::lake-loader-input-365542662955-20250525-001439", "arn:aws:s3:::lake-loader-input-365542662955-20250525-001439/*", "arn:aws:s3:::lake-loader-output-365542662955-20250525-001439", "arn:aws:s3:::lake-loader-output-365542662955-20250525-001439/*", "arn:aws:s3:::lake-loader-iceberg-catalog-365542662955-20250525-001439", "arn:aws:s3:::lake-loader-iceberg-catalog-365542662955-20250525-001439/*"]}, {"Effect": "Allow", "Action": ["s3:PutObject", "s3:PutObjectAcl", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::lake-loader-output-365542662955-20250525-001439/*", "arn:aws:s3:::lake-loader-iceberg-catalog-365542662955-20250525-001439/*"]}, {"Effect": "Allow", "Action": ["s3:ListAllMyBuckets"], "Resource": "*"}]}