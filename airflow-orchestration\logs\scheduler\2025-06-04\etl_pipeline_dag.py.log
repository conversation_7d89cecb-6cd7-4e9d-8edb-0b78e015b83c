[2025-06-04T01:41:02.893+0000] {processor.py:161} INFO - Started process (PID=120) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:02.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:41:02.995+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:02.993+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:03.047+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:03.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.398+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.425+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.425+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.441+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.441+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.442+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.442+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:03.472+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.472+0000] {dag.py:3058} INFO - Creating ORM DAG for etl_pipeline_daily
[2025-06-04T01:41:03.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.503+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:41:03.549+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.698 seconds
[2025-06-04T01:41:33.708+0000] {processor.py:161} INFO - Started process (PID=128) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.713+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:41:33.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.720+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.773+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.864+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:33.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.915+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:41:33.946+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.254 seconds
[2025-06-04T01:42:04.085+0000] {processor.py:161} INFO - Started process (PID=136) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.088+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:42:04.091+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.090+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.120+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.155+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.154+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:04.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.179+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:42:04.202+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T01:42:35.063+0000] {processor.py:161} INFO - Started process (PID=144) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.065+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:42:35.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.068+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.119+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.161+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.161+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:35.197+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.197+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:42:35.239+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.180 seconds
[2025-06-04T01:43:05.463+0000] {processor.py:161} INFO - Started process (PID=152) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.464+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:43:05.593+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.738+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.927+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.927+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:06.056+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:06.056+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:43:06.143+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.688 seconds
[2025-06-04T01:43:36.480+0000] {processor.py:161} INFO - Started process (PID=154) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.481+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:43:36.485+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.485+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.512+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.546+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:36.570+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.570+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:43:36.592+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T01:44:06.668+0000] {processor.py:161} INFO - Started process (PID=162) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.670+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:44:06.674+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.674+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.709+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.753+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:06.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.783+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:44:06.808+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.146 seconds
[2025-06-04T01:44:37.550+0000] {processor.py:161} INFO - Started process (PID=170) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.552+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:44:37.555+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.554+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.593+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.629+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.629+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:37.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.660+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:44:37.685+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.140 seconds
[2025-06-04T01:45:08.503+0000] {processor.py:161} INFO - Started process (PID=178) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.504+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:45:08.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.506+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.535+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.567+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.567+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:08.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.591+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:45:08.619+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T01:45:39.619+0000] {processor.py:161} INFO - Started process (PID=185) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.620+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:45:39.623+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.623+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.648+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.682+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:39.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.709+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:45:39.733+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:46:09.969+0000] {processor.py:161} INFO - Started process (PID=195) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:09.970+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:46:09.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:09.973+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:10.001+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:10.032+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.032+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:10.058+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.058+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:46:10.081+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T01:46:40.882+0000] {processor.py:161} INFO - Started process (PID=204) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.883+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:46:40.887+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.886+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.919+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.952+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.952+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:40.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.978+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:46:41.006+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T01:47:11.099+0000] {processor.py:161} INFO - Started process (PID=213) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.101+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:47:11.105+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.103+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.128+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.163+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.163+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:11.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.186+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:47:11.210+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T01:47:41.384+0000] {processor.py:161} INFO - Started process (PID=222) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.386+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:47:41.388+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.388+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.418+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.449+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:41.471+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.471+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:47:41.491+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.111 seconds
[2025-06-04T01:48:11.657+0000] {processor.py:161} INFO - Started process (PID=231) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.658+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:48:11.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.660+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.683+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.714+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:11.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.738+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:48:11.761+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T01:48:42.496+0000] {processor.py:161} INFO - Started process (PID=240) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.498+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:48:42.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.524+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.554+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.554+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:42.578+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.578+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:48:42.612+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T01:49:12.826+0000] {processor.py:161} INFO - Started process (PID=249) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.828+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:49:12.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.831+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.853+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.896+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.895+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:12.934+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.934+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:49:12.980+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.160 seconds
[2025-06-04T01:49:43.081+0000] {processor.py:161} INFO - Started process (PID=257) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.083+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:49:43.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.085+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.108+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.142+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.141+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:43.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.162+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:49:43.186+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T01:50:13.295+0000] {processor.py:161} INFO - Started process (PID=266) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.296+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:50:13.299+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.299+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.326+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.385+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.385+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:13.413+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.413+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:50:13.441+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.150 seconds
[2025-06-04T01:50:44.317+0000] {processor.py:161} INFO - Started process (PID=275) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.319+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:50:44.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.321+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.357+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.401+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.401+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:44.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.431+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:50:44.462+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.148 seconds
[2025-06-04T01:51:14.671+0000] {processor.py:161} INFO - Started process (PID=284) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.672+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:51:14.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.675+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.700+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.737+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.737+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:14.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.762+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:51:14.786+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T01:51:44.949+0000] {processor.py:161} INFO - Started process (PID=292) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:44.951+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:51:44.953+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:44.953+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:44.994+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:45.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.042+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:45.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.068+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:51:45.098+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.156 seconds
[2025-06-04T01:52:16.128+0000] {processor.py:161} INFO - Started process (PID=301) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:52:16.135+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.135+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.159+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.193+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:16.218+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.218+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:52:16.241+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:52:47.266+0000] {processor.py:161} INFO - Started process (PID=310) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.267+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:52:47.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.269+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.295+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.357+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.357+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:47.425+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.425+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:52:47.448+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.192 seconds
[2025-06-04T01:53:19.452+0000] {processor.py:161} INFO - Started process (PID=320) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.454+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:53:19.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.455+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.479+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:19.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.536+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:53:19.561+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.113 seconds
[2025-06-04T01:53:50.937+0000] {processor.py:161} INFO - Started process (PID=329) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:50.938+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:53:50.941+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.941+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:50.968+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:51.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:51.012+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:51.039+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:51.039+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:53:51.069+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T01:54:21.762+0000] {processor.py:161} INFO - Started process (PID=338) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.763+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:54:21.765+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.765+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.787+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.820+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:21.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.843+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:54:21.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T01:54:52.295+0000] {processor.py:161} INFO - Started process (PID=347) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.297+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:54:52.299+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.299+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.326+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.379+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:52.411+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.411+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:54:52.446+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.164 seconds
[2025-06-04T01:55:22.628+0000] {processor.py:161} INFO - Started process (PID=356) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.630+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:55:22.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.633+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.655+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.692+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:22.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.721+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:55:22.741+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:55:53.002+0000] {processor.py:161} INFO - Started process (PID=365) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.003+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:55:53.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.006+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.030+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.064+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.064+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:53.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.088+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:55:53.117+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T01:56:24.067+0000] {processor.py:161} INFO - Started process (PID=374) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.069+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:56:24.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.071+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.094+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.125+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:24.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.179+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:56:24.235+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.172 seconds
[2025-06-04T01:56:55.124+0000] {processor.py:161} INFO - Started process (PID=383) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.126+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:56:55.129+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.129+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.151+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.187+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:55.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.211+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:56:55.235+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T01:57:26.156+0000] {processor.py:161} INFO - Started process (PID=392) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.157+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:57:26.159+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.159+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.190+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.223+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:26.248+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.248+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:57:26.267+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T01:57:56.361+0000] {processor.py:161} INFO - Started process (PID=402) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.363+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:57:56.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.391+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.436+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.436+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:56.467+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.466+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:57:56.490+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.135 seconds
[2025-06-04T01:58:27.255+0000] {processor.py:161} INFO - Started process (PID=411) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.263+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:58:27.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.283+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.319+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.348+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:27.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.372+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:58:27.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.148 seconds
[2025-06-04T01:58:58.846+0000] {processor.py:161} INFO - Started process (PID=420) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:58.848+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:58:59.001+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.991+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:59.084+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:59.269+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:59.269+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:59.348+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:59.348+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:58:59.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.553 seconds
[2025-06-04T01:59:30.165+0000] {processor.py:161} INFO - Started process (PID=428) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.167+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:59:30.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.169+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.199+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.236+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:30.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.259+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:59:30.283+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:00:00.413+0000] {processor.py:161} INFO - Started process (PID=431) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.414+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:00:00.417+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.417+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.448+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.484+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:00.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.516+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:00:00.541+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:00:30.596+0000] {processor.py:161} INFO - Started process (PID=439) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.598+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:00:30.600+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.600+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.625+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.662+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:30.689+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.689+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:00:30.713+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:01:01.663+0000] {processor.py:161} INFO - Started process (PID=448) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.664+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:01:01.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.666+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.705+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.741+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.741+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:01.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.771+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:01:01.790+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T02:01:31.873+0000] {processor.py:161} INFO - Started process (PID=457) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.874+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:01:31.880+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.879+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.900+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.930+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.930+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:31.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.953+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:01:31.974+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.105 seconds
[2025-06-04T02:02:02.061+0000] {processor.py:161} INFO - Started process (PID=467) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.063+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:02:02.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.065+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.092+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.124+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.123+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:02.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.148+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:02:02.172+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:02:32.316+0000] {processor.py:161} INFO - Started process (PID=476) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.318+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:02:32.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.320+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.363+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.397+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:32.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.422+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:02:32.442+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T02:03:03.302+0000] {processor.py:161} INFO - Started process (PID=485) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.303+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:03:03.306+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.305+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.338+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.380+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:03.406+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.406+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:03:03.434+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:03:33.489+0000] {processor.py:161} INFO - Started process (PID=494) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.491+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:03:33.493+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.493+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.517+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.548+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:33.579+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.579+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:03:33.603+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T02:04:03.810+0000] {processor.py:161} INFO - Started process (PID=503) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.812+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:04:03.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.813+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.838+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.870+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.869+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:03.891+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.891+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:04:03.913+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
[2025-06-04T02:04:34.047+0000] {processor.py:161} INFO - Started process (PID=512) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.048+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:04:34.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.050+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.080+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.112+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:34.133+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.133+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:04:34.158+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:05:04.239+0000] {processor.py:161} INFO - Started process (PID=522) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:05:04.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.265+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.298+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.297+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:04.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.321+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:05:04.342+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T02:05:34.484+0000] {processor.py:161} INFO - Started process (PID=530) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.486+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:05:34.489+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.488+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.516+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:34.580+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.579+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:05:34.606+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T02:06:04.796+0000] {processor.py:161} INFO - Started process (PID=539) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.797+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:06:04.799+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.799+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.824+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.857+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.857+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:04.878+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.878+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:06:04.899+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T02:06:35.002+0000] {processor.py:161} INFO - Started process (PID=548) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.004+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:06:35.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.005+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.028+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.063+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:35.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.087+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:06:35.124+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T02:07:06.356+0000] {processor.py:161} INFO - Started process (PID=557) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.361+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:07:06.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.363+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.389+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.434+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.434+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:06.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.468+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:07:06.504+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.153 seconds
[2025-06-04T02:07:36.604+0000] {processor.py:161} INFO - Started process (PID=566) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.606+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:07:36.608+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.608+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.628+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.658+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:36.685+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.684+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:07:36.707+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T02:08:07.185+0000] {processor.py:161} INFO - Started process (PID=575) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.187+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:08:07.191+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.214+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:07.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.297+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:08:07.319+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.138 seconds
[2025-06-04T02:08:38.644+0000] {processor.py:161} INFO - Started process (PID=584) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.647+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:08:38.650+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.650+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.673+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.712+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:38.741+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.741+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:08:38.763+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:09:10.632+0000] {processor.py:161} INFO - Started process (PID=593) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.633+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:09:10.636+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.635+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.657+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.690+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.690+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:10.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.713+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:09:10.734+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
[2025-06-04T02:09:42.820+0000] {processor.py:161} INFO - Started process (PID=602) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.822+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:09:42.825+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.825+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.847+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.878+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.878+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:42.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.902+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:09:42.921+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.105 seconds
[2025-06-04T02:10:14.288+0000] {processor.py:161} INFO - Started process (PID=611) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.290+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:10:14.294+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.293+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.317+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.354+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.354+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:14.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.380+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:10:14.404+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T02:10:46.502+0000] {processor.py:161} INFO - Started process (PID=620) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.503+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:10:46.506+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.505+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.539+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.575+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:46.607+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.607+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:10:46.635+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T02:11:17.723+0000] {processor.py:161} INFO - Started process (PID=629) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.725+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:11:17.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.727+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.752+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:17.807+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.806+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:11:17.828+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T02:11:48.955+0000] {processor.py:161} INFO - Started process (PID=638) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:48.957+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:11:48.960+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:48.959+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:48.986+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:49.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.023+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:49.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.050+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:11:49.078+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.128 seconds
[2025-06-04T02:17:18.674+0000] {processor.py:161} INFO - Started process (PID=119) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.696+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:17:18.699+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.699+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.731+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.858+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:17:18.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.879+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:17:18.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.234 seconds
[2025-06-04T02:20:35.621+0000] {processor.py:161} INFO - Started process (PID=63) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.641+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:20:35.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.643+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.666+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.795+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.795+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:20:35.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.819+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:20:35.918+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.303 seconds
[2025-06-04T02:21:00.690+0000] {processor.py:161} INFO - Started process (PID=67) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.691+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:00.694+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.694+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.717+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.730+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.730+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:00.752+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.752+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:00.924+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.238 seconds
[2025-06-04T02:21:17.963+0000] {processor.py:161} INFO - Started process (PID=57) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:17.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:17.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:17.992+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:18.026+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:18.026+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:18.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:18.050+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:18.074+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:21:48.143+0000] {processor.py:161} INFO - Started process (PID=66) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:48.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.171+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.240+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:48.291+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.291+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:48.318+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.179 seconds
[2025-06-04T02:22:18.748+0000] {processor.py:161} INFO - Started process (PID=75) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.750+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:22:18.752+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.752+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.779+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.814+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:18.845+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.845+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:22:18.870+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:22:49.071+0000] {processor.py:161} INFO - Started process (PID=85) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.073+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:22:49.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.075+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.101+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.136+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.136+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:49.159+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.158+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:22:49.186+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.119 seconds
[2025-06-04T02:38:10.398+0000] {processor.py:161} INFO - Started process (PID=53) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.405+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:38:10.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.407+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.433+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:10.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.574+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:38:10.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.209 seconds
[2025-06-04T02:38:40.673+0000] {processor.py:161} INFO - Started process (PID=62) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.674+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:38:40.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.676+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.701+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.732+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.732+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:40.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.753+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:38:40.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T02:39:11.340+0000] {processor.py:161} INFO - Started process (PID=71) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.342+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:39:11.345+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.344+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.396+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.539+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:11.572+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.572+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:39:11.600+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.273 seconds
[2025-06-04T02:39:42.627+0000] {processor.py:161} INFO - Started process (PID=80) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.628+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:39:42.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.632+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.667+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.705+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.704+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:42.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.734+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:39:42.756+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:40:12.876+0000] {processor.py:161} INFO - Started process (PID=89) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.877+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:40:12.880+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.880+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.903+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.934+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.933+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:12.955+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.955+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:40:12.976+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.104 seconds
[2025-06-04T02:40:43.067+0000] {processor.py:161} INFO - Started process (PID=98) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.069+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:40:43.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.071+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.097+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.157+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.157+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:43.178+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.178+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:40:43.203+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.147 seconds
[2025-06-04T02:41:13.387+0000] {processor.py:161} INFO - Started process (PID=107) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:41:13.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.449+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.505+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.536+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:13.558+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.558+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:41:13.580+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.198 seconds
[2025-06-04T02:41:44.086+0000] {processor.py:161} INFO - Started process (PID=116) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.088+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:41:44.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.092+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.118+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.148+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:44.178+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.177+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:41:44.206+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:42:14.276+0000] {processor.py:161} INFO - Started process (PID=126) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.277+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:42:14.280+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.279+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.303+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.339+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:14.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.363+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:42:14.384+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T02:42:44.725+0000] {processor.py:161} INFO - Started process (PID=135) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.729+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:42:44.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.731+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.752+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:44.817+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.817+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:42:44.843+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:43:15.215+0000] {processor.py:161} INFO - Started process (PID=144) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.218+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:43:15.221+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.221+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.253+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.292+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.292+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:15.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.320+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:43:15.343+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:43:45.918+0000] {processor.py:161} INFO - Started process (PID=153) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:45.921+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:43:45.923+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.923+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:46.202+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:46.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:46.270+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:46.315+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:46.315+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:43:46.373+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.459 seconds
[2025-06-04T02:44:17.239+0000] {processor.py:161} INFO - Started process (PID=162) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:44:17.242+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.282+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.320+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:17.358+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.357+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:44:17.376+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.142 seconds
[2025-06-04T02:44:48.192+0000] {processor.py:161} INFO - Started process (PID=170) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.194+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:44:48.196+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.196+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.230+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.290+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:48.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.339+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:44:48.390+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.205 seconds
[2025-06-04T02:45:18.915+0000] {processor.py:161} INFO - Started process (PID=179) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.917+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:45:18.920+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.920+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.954+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.994+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.993+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:19.017+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:19.017+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:45:19.039+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:45:49.326+0000] {processor.py:161} INFO - Started process (PID=188) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.339+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:45:49.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.342+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.371+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.409+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:49.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.435+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:45:49.466+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.150 seconds
[2025-06-04T02:46:19.654+0000] {processor.py:161} INFO - Started process (PID=197) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.656+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:46:19.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.658+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.683+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.718+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:19.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.744+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:46:19.767+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T02:46:50.321+0000] {processor.py:161} INFO - Started process (PID=206) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.323+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:46:50.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.326+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.348+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.379+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:50.403+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.403+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:46:50.426+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T02:47:20.659+0000] {processor.py:161} INFO - Started process (PID=215) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.660+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:47:20.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.665+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.691+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.731+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:20.756+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.756+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:47:20.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:47:51.469+0000] {processor.py:161} INFO - Started process (PID=224) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.470+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:47:51.473+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.472+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.504+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.538+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.538+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:51.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.561+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:47:51.586+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:48:21.837+0000] {processor.py:161} INFO - Started process (PID=233) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.838+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:48:21.846+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.846+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.871+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.907+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.906+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:21.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.931+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:48:21.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:48:53.268+0000] {processor.py:161} INFO - Started process (PID=242) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.269+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:48:53.273+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.273+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.298+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.336+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.336+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:53.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.366+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:48:53.392+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T02:49:25.556+0000] {processor.py:161} INFO - Started process (PID=251) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.557+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:49:25.560+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.559+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.582+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.617+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:25.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.642+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:49:25.675+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.125 seconds
[2025-06-04T02:49:57.431+0000] {processor.py:161} INFO - Started process (PID=260) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.433+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:49:57.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.435+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.457+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.488+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:57.511+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.511+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:49:58.678+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 1.251 seconds
[2025-06-04T02:50:28.989+0000] {processor.py:161} INFO - Started process (PID=269) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:28.990+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:50:28.996+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.995+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:29.017+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:29.052+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:29.052+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:29.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:29.087+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:50:29.121+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:51:00.461+0000] {processor.py:161} INFO - Started process (PID=278) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.462+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:51:00.465+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.465+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.488+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.523+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.523+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:00.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.546+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:51:00.570+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T02:51:32.715+0000] {processor.py:161} INFO - Started process (PID=287) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.716+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:51:32.719+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.718+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.747+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.786+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.786+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:32.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.814+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:51:32.839+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:52:03.499+0000] {processor.py:161} INFO - Started process (PID=296) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:52:03.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.525+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.559+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.559+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:03.587+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.586+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:52:03.610+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:52:36.474+0000] {processor.py:161} INFO - Started process (PID=305) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.476+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:52:36.479+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.478+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.549+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.608+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.608+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:36.654+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.654+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:52:36.681+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.218 seconds
[2025-06-04T02:53:07.902+0000] {processor.py:161} INFO - Started process (PID=308) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.904+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:53:07.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.906+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.929+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.967+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:07.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.992+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:53:08.016+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T02:53:39.623+0000] {processor.py:161} INFO - Started process (PID=317) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.625+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:53:39.628+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.627+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.657+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.704+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.704+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:39.728+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.728+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:53:39.753+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.134 seconds
[2025-06-04T02:54:18.290+0000] {processor.py:161} INFO - Started process (PID=332) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.291+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:54:18.295+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.295+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.395+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.448+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:18.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.501+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:54:18.547+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.266 seconds
[2025-06-04T02:54:48.588+0000] {processor.py:161} INFO - Started process (PID=335) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.590+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:54:48.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.616+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.650+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.650+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:48.673+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.672+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:54:48.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
[2025-06-04T02:55:18.838+0000] {processor.py:161} INFO - Started process (PID=344) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.842+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:55:18.844+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.844+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.867+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.914+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:18.948+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.948+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:55:18.982+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.149 seconds
[2025-06-04T02:55:49.966+0000] {processor.py:161} INFO - Started process (PID=353) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:49.968+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:55:49.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.970+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:49.998+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:50.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:50.066+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:50.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:50.115+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:55:50.162+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.200 seconds
[2025-06-04T02:56:20.259+0000] {processor.py:161} INFO - Started process (PID=363) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.261+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:56:20.263+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.263+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.283+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.325+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.324+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:20.353+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.353+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:56:20.376+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:56:50.619+0000] {processor.py:161} INFO - Started process (PID=372) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.621+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:56:50.624+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.623+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.651+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.700+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.699+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:50.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.731+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:56:50.754+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T02:57:21.823+0000] {processor.py:161} INFO - Started process (PID=381) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.825+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:57:21.828+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.827+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.852+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.895+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.894+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:21.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.923+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:57:21.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.134 seconds
[2025-06-04T02:57:53.839+0000] {processor.py:161} INFO - Started process (PID=390) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.840+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:57:53.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.843+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.870+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.920+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.920+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:53.970+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.970+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:57:54.008+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.174 seconds
[2025-06-04T02:58:26.361+0000] {processor.py:161} INFO - Started process (PID=399) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.363+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:58:26.365+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.392+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.431+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:26.459+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.459+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:58:26.492+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:58:57.710+0000] {processor.py:161} INFO - Started process (PID=408) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.712+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:58:57.715+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.715+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.739+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.775+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.775+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:57.803+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.802+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:58:57.827+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:59:28.899+0000] {processor.py:161} INFO - Started process (PID=417) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.900+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:59:28.903+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.902+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.927+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.966+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:59:28.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.991+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:59:29.012+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:00:01.070+0000] {processor.py:161} INFO - Started process (PID=426) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.072+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:00:01.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.074+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.099+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.131+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:01.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.166+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:00:01.191+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T03:00:32.338+0000] {processor.py:161} INFO - Started process (PID=435) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.346+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:00:32.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.348+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.383+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.428+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.428+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:32.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.453+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:00:32.480+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.147 seconds
[2025-06-04T03:01:03.804+0000] {processor.py:161} INFO - Started process (PID=444) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.806+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:01:03.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.810+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.839+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.877+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.877+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:03.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.902+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:01:03.935+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:01:36.032+0000] {processor.py:161} INFO - Started process (PID=453) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:01:36.036+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.035+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.059+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.091+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:36.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.114+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:01:36.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T03:02:07.219+0000] {processor.py:161} INFO - Started process (PID=462) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.223+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:02:07.225+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.225+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.248+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.289+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.289+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:07.311+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.311+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:02:07.338+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.124 seconds
[2025-06-04T03:02:39.391+0000] {processor.py:161} INFO - Started process (PID=471) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.393+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:02:39.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.396+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.419+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.455+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.455+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:39.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.484+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:02:39.509+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T03:03:11.121+0000] {processor.py:161} INFO - Started process (PID=480) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.123+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:03:11.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.125+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.148+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.193+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:11.228+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.228+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:03:11.254+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:03:43.190+0000] {processor.py:161} INFO - Started process (PID=489) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.192+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:03:43.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.193+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.221+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.255+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:43.278+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.278+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:03:43.302+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T03:04:14.293+0000] {processor.py:161} INFO - Started process (PID=498) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.294+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:04:14.296+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.296+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.318+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.352+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.352+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:14.374+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.373+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:04:14.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
