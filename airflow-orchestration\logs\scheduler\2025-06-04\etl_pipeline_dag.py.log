[2025-06-04T01:41:02.893+0000] {processor.py:161} INFO - Started process (PID=120) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:02.907+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:41:02.995+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:02.993+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:03.047+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:03.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.398+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.425+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.425+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.441+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.441+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:etl_pipeline_daily
[2025-06-04T01:41:03.442+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.442+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:03.472+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.472+0000] {dag.py:3058} INFO - Creating ORM DAG for etl_pipeline_daily
[2025-06-04T01:41:03.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:03.503+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:41:03.549+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.698 seconds
[2025-06-04T01:41:33.708+0000] {processor.py:161} INFO - Started process (PID=128) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.713+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:41:33.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.720+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.773+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:41:33.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.864+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:41:33.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:41:33.915+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:41:33.946+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.254 seconds
[2025-06-04T01:42:04.085+0000] {processor.py:161} INFO - Started process (PID=136) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.088+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:42:04.091+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.090+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.120+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:04.155+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.154+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:04.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:04.179+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:42:04.202+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T01:42:35.063+0000] {processor.py:161} INFO - Started process (PID=144) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.065+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:42:35.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.068+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.119+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:42:35.161+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.161+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:42:35.197+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:42:35.197+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:42:35.239+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.180 seconds
[2025-06-04T01:43:05.463+0000] {processor.py:161} INFO - Started process (PID=152) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.464+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:43:05.593+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.738+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:05.927+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:05.927+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:06.056+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:06.056+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:43:06.143+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.688 seconds
[2025-06-04T01:43:36.480+0000] {processor.py:161} INFO - Started process (PID=154) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.481+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:43:36.485+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.485+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.512+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:43:36.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.546+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:43:36.570+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:43:36.570+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:43:36.592+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T01:44:06.668+0000] {processor.py:161} INFO - Started process (PID=162) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.670+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:44:06.674+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.674+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.709+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:06.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.753+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:06.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:06.783+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:44:06.808+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.146 seconds
[2025-06-04T01:44:37.550+0000] {processor.py:161} INFO - Started process (PID=170) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.552+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:44:37.555+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.554+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.593+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:44:37.629+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.629+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:44:37.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:44:37.660+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:44:37.685+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.140 seconds
[2025-06-04T01:45:08.503+0000] {processor.py:161} INFO - Started process (PID=178) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.504+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:45:08.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.506+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.535+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:08.567+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.567+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:08.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:08.591+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:45:08.619+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T01:45:39.619+0000] {processor.py:161} INFO - Started process (PID=185) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.620+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:45:39.623+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.623+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.648+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:45:39.682+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.682+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:45:39.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:45:39.709+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:45:39.733+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:46:09.969+0000] {processor.py:161} INFO - Started process (PID=195) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:09.970+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:46:09.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:09.973+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:10.001+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:10.032+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.032+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:10.058+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:10.058+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:46:10.081+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T01:46:40.882+0000] {processor.py:161} INFO - Started process (PID=204) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.883+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:46:40.887+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.886+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.919+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:46:40.952+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.952+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:46:40.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:46:40.978+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:46:41.006+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T01:47:11.099+0000] {processor.py:161} INFO - Started process (PID=213) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.101+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:47:11.105+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.103+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.128+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:11.163+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.163+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:11.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:11.186+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:47:11.210+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T01:47:41.384+0000] {processor.py:161} INFO - Started process (PID=222) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.386+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:47:41.388+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.388+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.418+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:47:41.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.449+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:47:41.471+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:47:41.471+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:47:41.491+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.111 seconds
[2025-06-04T01:48:11.657+0000] {processor.py:161} INFO - Started process (PID=231) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.658+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:48:11.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.660+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.683+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:11.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.714+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:11.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:11.738+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:48:11.761+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T01:48:42.496+0000] {processor.py:161} INFO - Started process (PID=240) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.498+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:48:42.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.524+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:48:42.554+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.554+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:48:42.578+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:48:42.578+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:48:42.612+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T01:49:12.826+0000] {processor.py:161} INFO - Started process (PID=249) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.828+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:49:12.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.831+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.853+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:12.896+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.895+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:12.934+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:12.934+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:49:12.980+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.160 seconds
[2025-06-04T01:49:43.081+0000] {processor.py:161} INFO - Started process (PID=257) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.083+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:49:43.086+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.085+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.108+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:49:43.142+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.141+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:49:43.162+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:49:43.162+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:49:43.186+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T01:50:13.295+0000] {processor.py:161} INFO - Started process (PID=266) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.296+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:50:13.299+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.299+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.326+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:13.385+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.385+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:13.413+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:13.413+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:50:13.441+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.150 seconds
[2025-06-04T01:50:44.317+0000] {processor.py:161} INFO - Started process (PID=275) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.319+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:50:44.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.321+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.357+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:50:44.401+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.401+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:50:44.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:50:44.431+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:50:44.462+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.148 seconds
[2025-06-04T01:51:14.671+0000] {processor.py:161} INFO - Started process (PID=284) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.672+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:51:14.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.675+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.700+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:14.737+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.737+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:14.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:14.762+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:51:14.786+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T01:51:44.949+0000] {processor.py:161} INFO - Started process (PID=292) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:44.951+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:51:44.953+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:44.953+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:44.994+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:51:45.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.042+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:51:45.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:51:45.068+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:51:45.098+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.156 seconds
[2025-06-04T01:52:16.128+0000] {processor.py:161} INFO - Started process (PID=301) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:52:16.135+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.135+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.159+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:16.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.193+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:16.218+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:16.218+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:52:16.241+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:52:47.266+0000] {processor.py:161} INFO - Started process (PID=310) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.267+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:52:47.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.269+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.295+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:52:47.357+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.357+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:52:47.425+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:52:47.425+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:52:47.448+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.192 seconds
[2025-06-04T01:53:19.452+0000] {processor.py:161} INFO - Started process (PID=320) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.454+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:53:19.456+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.455+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.479+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:19.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:19.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:19.536+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:53:19.561+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.113 seconds
[2025-06-04T01:53:50.937+0000] {processor.py:161} INFO - Started process (PID=329) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:50.938+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:53:50.941+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:50.941+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:50.968+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:53:51.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:51.012+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:53:51.039+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:53:51.039+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:53:51.069+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T01:54:21.762+0000] {processor.py:161} INFO - Started process (PID=338) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.763+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:54:21.765+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.765+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.787+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:21.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.820+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:21.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:21.843+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:54:21.864+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T01:54:52.295+0000] {processor.py:161} INFO - Started process (PID=347) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.297+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:54:52.299+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.299+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.326+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:54:52.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.379+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:54:52.411+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:54:52.411+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:54:52.446+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.164 seconds
[2025-06-04T01:55:22.628+0000] {processor.py:161} INFO - Started process (PID=356) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.630+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:55:22.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.633+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.655+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:22.692+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:22.721+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:22.721+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:55:22.741+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T01:55:53.002+0000] {processor.py:161} INFO - Started process (PID=365) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.003+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:55:53.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.006+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.030+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:55:53.064+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.064+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:55:53.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:55:53.088+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:55:53.117+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T01:56:24.067+0000] {processor.py:161} INFO - Started process (PID=374) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.069+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:56:24.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.071+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.094+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:24.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.125+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:24.179+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:24.179+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:56:24.235+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.172 seconds
[2025-06-04T01:56:55.124+0000] {processor.py:161} INFO - Started process (PID=383) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.126+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:56:55.129+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.129+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.151+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:56:55.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.187+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:56:55.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:56:55.211+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:56:55.235+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T01:57:26.156+0000] {processor.py:161} INFO - Started process (PID=392) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.157+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:57:26.159+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.159+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.190+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:26.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.223+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:26.248+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:26.248+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:57:26.267+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T01:57:56.361+0000] {processor.py:161} INFO - Started process (PID=402) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.363+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:57:56.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.391+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:57:56.436+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.436+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:57:56.467+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:57:56.466+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:57:56.490+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.135 seconds
[2025-06-04T01:58:27.255+0000] {processor.py:161} INFO - Started process (PID=411) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.263+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:58:27.284+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.283+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.319+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:27.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.348+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:27.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:27.372+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:58:27.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.148 seconds
[2025-06-04T01:58:58.846+0000] {processor.py:161} INFO - Started process (PID=420) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:58.848+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:58:59.001+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:58.991+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:59.084+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:58:59.269+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:59.269+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:58:59.348+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:58:59.348+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:58:59.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.553 seconds
[2025-06-04T01:59:30.165+0000] {processor.py:161} INFO - Started process (PID=428) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.167+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T01:59:30.169+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.169+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.199+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T01:59:30.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.236+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T01:59:30.260+0000] {logging_mixin.py:188} INFO - [2025-06-04T01:59:30.259+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T01:59:30.283+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:00:00.413+0000] {processor.py:161} INFO - Started process (PID=431) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.414+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:00:00.417+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.417+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.448+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:00.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.484+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:00.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:00.516+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:00:00.541+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:00:30.596+0000] {processor.py:161} INFO - Started process (PID=439) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.598+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:00:30.600+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.600+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.625+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:00:30.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.662+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:00:30.689+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:00:30.689+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:00:30.713+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:01:01.663+0000] {processor.py:161} INFO - Started process (PID=448) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.664+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:01:01.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.666+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.705+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:01.741+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.741+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:01.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:01.771+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:01:01.790+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T02:01:31.873+0000] {processor.py:161} INFO - Started process (PID=457) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.874+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:01:31.880+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.879+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.900+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:01:31.930+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.930+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:01:31.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:01:31.953+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:01:31.974+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.105 seconds
[2025-06-04T02:02:02.061+0000] {processor.py:161} INFO - Started process (PID=467) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.063+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:02:02.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.065+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.092+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:02.124+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.123+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:02.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:02.148+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:02:02.172+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:02:32.316+0000] {processor.py:161} INFO - Started process (PID=476) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.318+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:02:32.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.320+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.363+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:02:32.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.397+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:02:32.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:02:32.422+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:02:32.442+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T02:03:03.302+0000] {processor.py:161} INFO - Started process (PID=485) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.303+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:03:03.306+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.305+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.338+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:03.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.380+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:03.406+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:03.406+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:03:03.434+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:03:33.489+0000] {processor.py:161} INFO - Started process (PID=494) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.491+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:03:33.493+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.493+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.517+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:03:33.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.548+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:03:33.579+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:03:33.579+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:03:33.603+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T02:04:03.810+0000] {processor.py:161} INFO - Started process (PID=503) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.812+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:04:03.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.813+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.838+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:03.870+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.869+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:03.891+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:03.891+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:04:03.913+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
[2025-06-04T02:04:34.047+0000] {processor.py:161} INFO - Started process (PID=512) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.048+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:04:34.051+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.050+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.080+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:04:34.112+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.112+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:04:34.133+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:04:34.133+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:04:34.158+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:05:04.239+0000] {processor.py:161} INFO - Started process (PID=522) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:05:04.243+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.265+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:04.298+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.297+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:04.321+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:04.321+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:05:04.342+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T02:05:34.484+0000] {processor.py:161} INFO - Started process (PID=530) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.486+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:05:34.489+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.488+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.516+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:05:34.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:05:34.580+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:05:34.579+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:05:34.606+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T02:06:04.796+0000] {processor.py:161} INFO - Started process (PID=539) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.797+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:06:04.799+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.799+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.824+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:04.857+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.857+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:04.878+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:04.878+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:06:04.899+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T02:06:35.002+0000] {processor.py:161} INFO - Started process (PID=548) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.004+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:06:35.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.005+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.028+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:06:35.063+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.063+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:06:35.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:06:35.087+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:06:35.124+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T02:07:06.356+0000] {processor.py:161} INFO - Started process (PID=557) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.361+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:07:06.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.363+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.389+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:06.434+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.434+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:06.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:06.468+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:07:06.504+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.153 seconds
[2025-06-04T02:07:36.604+0000] {processor.py:161} INFO - Started process (PID=566) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.606+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:07:36.608+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.608+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.628+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:07:36.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.658+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:07:36.685+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:07:36.684+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:07:36.707+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T02:08:07.185+0000] {processor.py:161} INFO - Started process (PID=575) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.187+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:08:07.191+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.214+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:07.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:07.297+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:07.297+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:08:07.319+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.138 seconds
[2025-06-04T02:08:38.644+0000] {processor.py:161} INFO - Started process (PID=584) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.647+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:08:38.650+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.650+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.673+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:08:38.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.712+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:08:38.741+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:08:38.741+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:08:38.763+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:09:10.632+0000] {processor.py:161} INFO - Started process (PID=593) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.633+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:09:10.636+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.635+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.657+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:10.690+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.690+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:10.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:10.713+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:09:10.734+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
[2025-06-04T02:09:42.820+0000] {processor.py:161} INFO - Started process (PID=602) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.822+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:09:42.825+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.825+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.847+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:09:42.878+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.878+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:09:42.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:09:42.902+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:09:42.921+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.105 seconds
[2025-06-04T02:10:14.288+0000] {processor.py:161} INFO - Started process (PID=611) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.290+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:10:14.294+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.293+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.317+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:14.354+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.354+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:14.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:14.380+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:10:14.404+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T02:10:46.502+0000] {processor.py:161} INFO - Started process (PID=620) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.503+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:10:46.506+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.505+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.539+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:10:46.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.575+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:10:46.607+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:10:46.607+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:10:46.635+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T02:11:17.723+0000] {processor.py:161} INFO - Started process (PID=629) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.725+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:11:17.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.727+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.752+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:17.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:17.807+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:17.806+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:11:17.828+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T02:11:48.955+0000] {processor.py:161} INFO - Started process (PID=638) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:48.957+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:11:48.960+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:48.959+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:48.986+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:11:49.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.023+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:11:49.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:11:49.050+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:11:49.078+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.128 seconds
[2025-06-04T02:17:18.674+0000] {processor.py:161} INFO - Started process (PID=119) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.696+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:17:18.699+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.699+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.731+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:17:18.859+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.858+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:17:18.879+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:17:18.879+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:17:18.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.234 seconds
[2025-06-04T02:20:35.621+0000] {processor.py:161} INFO - Started process (PID=63) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.641+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:20:35.644+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.643+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.666+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:20:35.795+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.795+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:20:35.820+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:20:35.819+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:20:35.918+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.303 seconds
[2025-06-04T02:21:00.690+0000] {processor.py:161} INFO - Started process (PID=67) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.691+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:00.694+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.694+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.717+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:00.730+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.730+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:00.752+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:00.752+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:00.924+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.238 seconds
[2025-06-04T02:21:17.963+0000] {processor.py:161} INFO - Started process (PID=57) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:17.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:17.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:17.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:17.992+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:18.026+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:18.026+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:18.050+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:18.050+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:18.074+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:21:48.143+0000] {processor.py:161} INFO - Started process (PID=66) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:21:48.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.171+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:21:48.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.240+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:21:48.291+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:21:48.291+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:21:48.318+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.179 seconds
[2025-06-04T02:22:18.748+0000] {processor.py:161} INFO - Started process (PID=75) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.750+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:22:18.752+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.752+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.779+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:18.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.814+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:18.845+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:18.845+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:22:18.870+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:22:49.071+0000] {processor.py:161} INFO - Started process (PID=85) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.073+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:22:49.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.075+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.101+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:22:49.136+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.136+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:22:49.159+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:22:49.158+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:22:49.186+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.119 seconds
[2025-06-04T02:38:10.398+0000] {processor.py:161} INFO - Started process (PID=53) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.405+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:38:10.408+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.407+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.433+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:10.552+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.552+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:10.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:10.574+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:38:10.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.209 seconds
[2025-06-04T02:38:40.673+0000] {processor.py:161} INFO - Started process (PID=62) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.674+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:38:40.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.676+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.701+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:38:40.732+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.732+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:38:40.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:38:40.753+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:38:40.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T02:39:11.340+0000] {processor.py:161} INFO - Started process (PID=71) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.342+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:39:11.345+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.344+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.396+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:11.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.539+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:11.572+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:11.572+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:39:11.600+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.273 seconds
[2025-06-04T02:39:42.627+0000] {processor.py:161} INFO - Started process (PID=80) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.628+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:39:42.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.632+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.667+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:39:42.705+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.704+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:39:42.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:39:42.734+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:39:42.756+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:40:12.876+0000] {processor.py:161} INFO - Started process (PID=89) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.877+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:40:12.880+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.880+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.903+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:12.934+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.933+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:12.955+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:12.955+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:40:12.976+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.104 seconds
[2025-06-04T02:40:43.067+0000] {processor.py:161} INFO - Started process (PID=98) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.069+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:40:43.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.071+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.097+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:40:43.157+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.157+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:40:43.178+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:40:43.178+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:40:43.203+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.147 seconds
[2025-06-04T02:41:13.387+0000] {processor.py:161} INFO - Started process (PID=107) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.418+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:41:13.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.449+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.505+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:13.536+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.536+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:13.558+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:13.558+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:41:13.580+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.198 seconds
[2025-06-04T02:41:44.086+0000] {processor.py:161} INFO - Started process (PID=116) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.088+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:41:44.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.092+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.118+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:41:44.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.148+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:41:44.178+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:41:44.177+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:41:44.206+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:42:14.276+0000] {processor.py:161} INFO - Started process (PID=126) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.277+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:42:14.280+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.279+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.303+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:14.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.339+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:14.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:14.363+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:42:14.384+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T02:42:44.725+0000] {processor.py:161} INFO - Started process (PID=135) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.729+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:42:44.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.731+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.752+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:42:44.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.783+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:42:44.817+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:42:44.817+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:42:44.843+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:43:15.215+0000] {processor.py:161} INFO - Started process (PID=144) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.218+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:43:15.221+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.221+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.253+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:15.292+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.292+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:15.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:15.320+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:43:15.343+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T02:43:45.918+0000] {processor.py:161} INFO - Started process (PID=153) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:45.921+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:43:45.923+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:45.923+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:46.202+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:43:46.270+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:46.270+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:43:46.315+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:43:46.315+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:43:46.373+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.459 seconds
[2025-06-04T02:44:17.239+0000] {processor.py:161} INFO - Started process (PID=162) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:44:17.242+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.282+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:17.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.320+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:17.358+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:17.357+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:44:17.376+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.142 seconds
[2025-06-04T02:44:48.192+0000] {processor.py:161} INFO - Started process (PID=170) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.194+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:44:48.196+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.196+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.230+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:44:48.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.290+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:44:48.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:44:48.339+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:44:48.390+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.205 seconds
[2025-06-04T02:45:18.915+0000] {processor.py:161} INFO - Started process (PID=179) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.917+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:45:18.920+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.920+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.954+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:18.994+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:18.993+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:19.017+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:19.017+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:45:19.039+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:45:49.326+0000] {processor.py:161} INFO - Started process (PID=188) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.339+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:45:49.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.342+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.371+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:45:49.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.409+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:45:49.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:45:49.435+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:45:49.466+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.150 seconds
[2025-06-04T02:46:19.654+0000] {processor.py:161} INFO - Started process (PID=197) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.656+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:46:19.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.658+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.683+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:19.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.718+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:19.744+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:19.744+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:46:19.767+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T02:46:50.321+0000] {processor.py:161} INFO - Started process (PID=206) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.323+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:46:50.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.326+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.348+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:46:50.380+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.379+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:46:50.403+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:46:50.403+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:46:50.426+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T02:47:20.659+0000] {processor.py:161} INFO - Started process (PID=215) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.660+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:47:20.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.665+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.691+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:20.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.731+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:20.756+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:20.756+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:47:20.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:47:51.469+0000] {processor.py:161} INFO - Started process (PID=224) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.470+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:47:51.473+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.472+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.504+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:47:51.538+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.538+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:47:51.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:47:51.561+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:47:51.586+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T02:48:21.837+0000] {processor.py:161} INFO - Started process (PID=233) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.838+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:48:21.846+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.846+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.871+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:21.907+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.906+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:21.931+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:21.931+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:48:21.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:48:53.268+0000] {processor.py:161} INFO - Started process (PID=242) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.269+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:48:53.273+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.273+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.298+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:48:53.336+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.336+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:48:53.366+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:48:53.366+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:48:53.392+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T02:49:25.556+0000] {processor.py:161} INFO - Started process (PID=251) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.557+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:49:25.560+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.559+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.582+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:25.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.617+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:25.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:25.642+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:49:25.675+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.125 seconds
[2025-06-04T02:49:57.431+0000] {processor.py:161} INFO - Started process (PID=260) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.433+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:49:57.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.435+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.457+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:49:57.488+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.488+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:49:57.511+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:49:57.511+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:49:58.678+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 1.251 seconds
[2025-06-04T02:50:28.989+0000] {processor.py:161} INFO - Started process (PID=269) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:28.990+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:50:28.996+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:28.995+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:29.017+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:50:29.052+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:29.052+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:50:29.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:50:29.087+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:50:29.121+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:51:00.461+0000] {processor.py:161} INFO - Started process (PID=278) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.462+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:51:00.465+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.465+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.488+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:00.523+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.523+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:00.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:00.546+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:51:00.570+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T02:51:32.715+0000] {processor.py:161} INFO - Started process (PID=287) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.716+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:51:32.719+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.718+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.747+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:51:32.786+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.786+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:51:32.814+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:51:32.814+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:51:32.839+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T02:52:03.499+0000] {processor.py:161} INFO - Started process (PID=296) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:52:03.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.525+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:03.559+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.559+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:03.587+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:03.586+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:52:03.610+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T02:52:36.474+0000] {processor.py:161} INFO - Started process (PID=305) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.476+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:52:36.479+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.478+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.549+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:52:36.608+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.608+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:52:36.654+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:52:36.654+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:52:36.681+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.218 seconds
[2025-06-04T02:53:07.902+0000] {processor.py:161} INFO - Started process (PID=308) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.904+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:53:07.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.906+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.929+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:07.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.967+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:07.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:07.992+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:53:08.016+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T02:53:39.623+0000] {processor.py:161} INFO - Started process (PID=317) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.625+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:53:39.628+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.627+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.657+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:53:39.704+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.704+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:53:39.728+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:53:39.728+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:53:39.753+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.134 seconds
[2025-06-04T02:54:18.290+0000] {processor.py:161} INFO - Started process (PID=332) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.291+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:54:18.295+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.295+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.395+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:18.449+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.448+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:18.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:18.501+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:54:18.547+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.266 seconds
[2025-06-04T02:54:48.588+0000] {processor.py:161} INFO - Started process (PID=335) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.590+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:54:48.592+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.592+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.616+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:54:48.650+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.650+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:54:48.673+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:54:48.672+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:54:48.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
[2025-06-04T02:55:18.838+0000] {processor.py:161} INFO - Started process (PID=344) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.842+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:55:18.844+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.844+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.867+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:18.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.914+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:18.948+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:18.948+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:55:18.982+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.149 seconds
[2025-06-04T02:55:49.966+0000] {processor.py:161} INFO - Started process (PID=353) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:49.968+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:55:49.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:49.970+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:49.998+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:55:50.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:50.066+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:55:50.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:55:50.115+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:55:50.162+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.200 seconds
[2025-06-04T02:56:20.259+0000] {processor.py:161} INFO - Started process (PID=363) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.261+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:56:20.263+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.263+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.283+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:20.325+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.324+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:20.353+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:20.353+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:56:20.376+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T02:56:50.619+0000] {processor.py:161} INFO - Started process (PID=372) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.621+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:56:50.624+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.623+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.651+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:56:50.700+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.699+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:56:50.731+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:56:50.731+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:56:50.754+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T02:57:21.823+0000] {processor.py:161} INFO - Started process (PID=381) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.825+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:57:21.828+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.827+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.852+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:21.895+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.894+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:21.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:21.923+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:57:21.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.134 seconds
[2025-06-04T02:57:53.839+0000] {processor.py:161} INFO - Started process (PID=390) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.840+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:57:53.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.843+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.870+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:57:53.920+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.920+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:57:53.970+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:57:53.970+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:57:54.008+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.174 seconds
[2025-06-04T02:58:26.361+0000] {processor.py:161} INFO - Started process (PID=399) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.363+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:58:26.365+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.392+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:26.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.431+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:26.459+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:26.459+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:58:26.492+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T02:58:57.710+0000] {processor.py:161} INFO - Started process (PID=408) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.712+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:58:57.715+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.715+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.739+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:58:57.775+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.775+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:58:57.803+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:58:57.802+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:58:57.827+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T02:59:28.899+0000] {processor.py:161} INFO - Started process (PID=417) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.900+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T02:59:28.903+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.902+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.927+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T02:59:28.967+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.966+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T02:59:28.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T02:59:28.991+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T02:59:29.012+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:00:01.070+0000] {processor.py:161} INFO - Started process (PID=426) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.072+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:00:01.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.074+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.099+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:01.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.131+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:01.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:01.166+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:00:01.191+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T03:00:32.338+0000] {processor.py:161} INFO - Started process (PID=435) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.346+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:00:32.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.348+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.383+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:00:32.428+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.428+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:00:32.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:00:32.453+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:00:32.480+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.147 seconds
[2025-06-04T03:01:03.804+0000] {processor.py:161} INFO - Started process (PID=444) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.806+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:01:03.810+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.810+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.839+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:03.877+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.877+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:03.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:03.902+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:01:03.935+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:01:36.032+0000] {processor.py:161} INFO - Started process (PID=453) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:01:36.036+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.035+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.059+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:01:36.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.091+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:01:36.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:01:36.114+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:01:36.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T03:02:07.219+0000] {processor.py:161} INFO - Started process (PID=462) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.223+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:02:07.225+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.225+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.248+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:07.289+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.289+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:07.311+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:07.311+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:02:07.338+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.124 seconds
[2025-06-04T03:02:39.391+0000] {processor.py:161} INFO - Started process (PID=471) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.393+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:02:39.396+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.396+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.419+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:02:39.455+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.455+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:02:39.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:02:39.484+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:02:39.509+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T03:03:11.121+0000] {processor.py:161} INFO - Started process (PID=480) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.123+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:03:11.126+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.125+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.148+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:11.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.193+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:11.228+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:11.228+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:03:11.254+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:03:43.190+0000] {processor.py:161} INFO - Started process (PID=489) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.192+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:03:43.194+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.193+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.221+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:03:43.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.255+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:03:43.278+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:03:43.278+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:03:43.302+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T03:04:14.293+0000] {processor.py:161} INFO - Started process (PID=498) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.294+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:04:14.296+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.296+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.318+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:14.352+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.352+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:14.374+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:14.373+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:04:14.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.106 seconds
[2025-06-04T03:04:45.887+0000] {processor.py:161} INFO - Started process (PID=507) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:45.888+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:04:45.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.890+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:45.911+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:04:45.946+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.946+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:04:45.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:04:45.983+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:04:46.008+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T03:05:18.349+0000] {processor.py:161} INFO - Started process (PID=516) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:18.352+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:05:18.355+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.355+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:18.383+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:18.417+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.417+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:18.443+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:18.443+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:05:18.465+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T03:05:49.647+0000] {processor.py:161} INFO - Started process (PID=525) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:49.648+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:05:49.651+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.650+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:49.675+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:05:49.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.713+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:05:49.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:05:49.736+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:05:49.763+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:06:22.352+0000] {processor.py:161} INFO - Started process (PID=533) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:22.355+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:06:22.365+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.365+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:22.414+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:22.496+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.496+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:22.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:22.547+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:06:22.717+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.369 seconds
[2025-06-04T03:06:53.016+0000] {processor.py:161} INFO - Started process (PID=542) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:53.017+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:06:53.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:53.021+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:53.045+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:06:53.106+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:53.105+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:06:53.140+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:06:53.140+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:06:53.172+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.160 seconds
[2025-06-04T03:07:25.098+0000] {processor.py:161} INFO - Started process (PID=552) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:25.113+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:07:25.129+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:25.129+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:25.437+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:25.544+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:25.543+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:25.621+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:25.621+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:07:25.721+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.640 seconds
[2025-06-04T03:07:56.686+0000] {processor.py:161} INFO - Started process (PID=562) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:56.687+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:07:56.701+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:56.700+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:56.867+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:07:56.917+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:56.917+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:07:56.947+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:07:56.947+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:07:56.974+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.292 seconds
[2025-06-04T03:08:29.117+0000] {processor.py:161} INFO - Started process (PID=572) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:08:29.122+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:08:29.131+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:29.130+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:08:29.192+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:08:29.236+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:29.236+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:08:29.277+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:08:29.276+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:08:29.314+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.201 seconds
[2025-06-04T03:09:00.445+0000] {processor.py:161} INFO - Started process (PID=581) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:00.447+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:09:00.450+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:00.450+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:00.474+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:00.509+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:00.509+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:00.534+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:00.534+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:09:00.560+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T03:09:32.768+0000] {processor.py:161} INFO - Started process (PID=591) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:32.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:09:32.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:32.772+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:32.806+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:09:32.853+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:32.853+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:09:32.890+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:09:32.889+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:09:32.960+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.200 seconds
[2025-06-04T03:10:03.038+0000] {processor.py:161} INFO - Started process (PID=600) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:03.040+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:10:03.043+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:03.042+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:03.064+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:03.093+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:03.093+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:03.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:03.115+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:10:03.140+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.107 seconds
[2025-06-04T03:10:33.244+0000] {processor.py:161} INFO - Started process (PID=609) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:33.246+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:10:33.249+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:33.249+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:33.288+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:10:33.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:33.342+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:10:33.444+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:10:33.444+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:10:33.596+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.359 seconds
[2025-06-04T03:11:03.782+0000] {processor.py:161} INFO - Started process (PID=618) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:03.785+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:11:03.790+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:03.790+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:03.823+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:03.860+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:03.860+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:03.897+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:03.896+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:11:04.007+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.230 seconds
[2025-06-04T03:11:34.723+0000] {processor.py:161} INFO - Started process (PID=627) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:34.724+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:11:34.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:34.727+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:34.754+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:11:34.793+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:34.793+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:11:34.821+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:11:34.821+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:11:35.392+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.675 seconds
[2025-06-04T03:12:05.859+0000] {processor.py:161} INFO - Started process (PID=636) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:05.861+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:12:05.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:05.864+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:05.900+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:05.940+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:05.939+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:05.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:05.962+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:12:05.989+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:12:37.061+0000] {processor.py:161} INFO - Started process (PID=645) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:37.062+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:12:37.065+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:37.064+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:37.095+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:12:37.137+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:37.136+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:12:37.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:12:37.166+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:12:37.199+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.144 seconds
[2025-06-04T03:13:07.497+0000] {processor.py:161} INFO - Started process (PID=654) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:07.498+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:13:07.501+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:07.501+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:07.529+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:08.816+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:08.816+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:08.843+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:08.842+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:13:08.870+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T03:13:41.128+0000] {processor.py:161} INFO - Started process (PID=664) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:41.129+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:13:41.133+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.132+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:41.156+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:13:41.189+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.189+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:13:41.219+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:13:41.218+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:13:41.242+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T03:14:12.335+0000] {processor.py:161} INFO - Started process (PID=673) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:12.337+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:14:12.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.339+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:12.363+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:12.399+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.397+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:12.426+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:12.426+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:14:12.451+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:14:44.571+0000] {processor.py:161} INFO - Started process (PID=682) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:44.573+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:14:44.575+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.575+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:44.599+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:14:44.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.630+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:14:44.653+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:14:44.652+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:14:44.676+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T03:15:16.895+0000] {processor.py:161} INFO - Started process (PID=691) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:16.897+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:15:16.899+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.899+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:16.920+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:16.957+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.956+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:16.982+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:16.981+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:15:17.002+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.111 seconds
[2025-06-04T03:15:47.982+0000] {processor.py:161} INFO - Started process (PID=700) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:47.984+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:15:47.988+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:47.987+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:48.007+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:15:48.047+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:48.047+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:15:48.077+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:15:48.077+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:15:48.106+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.128 seconds
[2025-06-04T03:16:20.298+0000] {processor.py:161} INFO - Started process (PID=709) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:20.302+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:16:20.305+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:20.304+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:20.337+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:20.406+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:20.405+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:20.430+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:20.430+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:16:20.457+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.164 seconds
[2025-06-04T03:16:51.343+0000] {processor.py:161} INFO - Started process (PID=718) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:51.344+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:16:51.346+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.346+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:51.369+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:16:51.414+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.414+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:16:51.441+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:16:51.440+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:16:51.464+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.126 seconds
[2025-06-04T03:17:23.706+0000] {processor.py:161} INFO - Started process (PID=727) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:23.707+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:17:23.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.709+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:23.732+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:23.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.923+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:23.946+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:23.946+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:17:23.970+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.270 seconds
[2025-06-04T03:17:54.911+0000] {processor.py:161} INFO - Started process (PID=736) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:54.912+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:17:54.914+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.914+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:54.938+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:17:54.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.972+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:17:54.997+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:17:54.997+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:17:55.018+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
[2025-06-04T03:18:26.374+0000] {processor.py:161} INFO - Started process (PID=745) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:26.377+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:18:26.379+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.379+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:26.401+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:26.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.610+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:26.632+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:26.631+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:18:26.658+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.288 seconds
[2025-06-04T03:18:58.084+0000] {processor.py:161} INFO - Started process (PID=754) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:58.086+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:18:58.088+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:58.088+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:58.114+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:18:58.147+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:58.147+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:18:58.172+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:18:58.171+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:18:58.202+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T03:19:30.567+0000] {processor.py:161} INFO - Started process (PID=762) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:19:30.569+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:19:30.571+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.571+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:19:30.609+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:19:30.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.643+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:19:30.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:19:30.665+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:19:30.691+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.128 seconds
[2025-06-04T03:20:01.901+0000] {processor.py:161} INFO - Started process (PID=771) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:01.902+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:20:01.906+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.906+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:01.930+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:01.964+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.963+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:01.987+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:01.987+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:20:02.010+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.113 seconds
[2025-06-04T03:20:34.029+0000] {processor.py:161} INFO - Started process (PID=781) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:34.030+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:20:34.033+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:34.032+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:34.057+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:20:34.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:34.094+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:20:34.117+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:20:34.117+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:20:34.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
[2025-06-04T03:21:05.406+0000] {processor.py:161} INFO - Started process (PID=790) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:05.407+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:21:05.410+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.410+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:05.432+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:05.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.468+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:05.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:05.495+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:21:05.524+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:21:37.717+0000] {processor.py:161} INFO - Started process (PID=799) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:37.719+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:21:37.723+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.722+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:37.758+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:21:37.790+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.790+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:21:37.811+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:21:37.811+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:21:37.836+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.124 seconds
[2025-06-04T03:22:10.038+0000] {processor.py:161} INFO - Started process (PID=808) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:10.039+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:22:10.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:10.041+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:10.069+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:10.114+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:10.113+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:10.147+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:10.146+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:22:10.183+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.150 seconds
[2025-06-04T03:22:41.286+0000] {processor.py:161} INFO - Started process (PID=817) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:41.288+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:22:41.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.290+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:41.320+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:22:41.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.371+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:22:41.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:22:41.404+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:22:41.426+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.144 seconds
[2025-06-04T03:23:12.767+0000] {processor.py:161} INFO - Started process (PID=826) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:12.769+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:23:12.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:12.802+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:12.838+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.838+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:12.864+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:12.864+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:23:12.890+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T03:23:45.301+0000] {processor.py:161} INFO - Started process (PID=835) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:45.303+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:23:45.307+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.307+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:45.329+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:23:45.364+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.363+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:23:45.387+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:23:45.387+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:23:45.408+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.111 seconds
[2025-06-04T03:24:16.609+0000] {processor.py:161} INFO - Started process (PID=844) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:16.610+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:24:16.615+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.614+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:16.639+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:16.691+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:16.729+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:16.728+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:24:16.752+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.149 seconds
[2025-06-04T03:24:48.162+0000] {processor.py:161} INFO - Started process (PID=853) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:48.163+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:24:48.165+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:48.165+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:48.191+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:24:48.224+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:48.224+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:24:48.246+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:24:48.246+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:24:48.270+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T03:25:20.427+0000] {processor.py:161} INFO - Started process (PID=862) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:20.429+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:25:20.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:20.431+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:20.459+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:20.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:20.495+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:20.520+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:20.519+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:25:20.543+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:25:52.685+0000] {processor.py:161} INFO - Started process (PID=870) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:52.686+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:25:52.689+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:52.689+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:52.718+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:25:52.751+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:52.751+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:25:52.777+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:25:52.776+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:25:52.800+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T03:26:23.958+0000] {processor.py:161} INFO - Started process (PID=879) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:23.960+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:26:23.971+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:23.968+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:24.012+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:24.059+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:24.057+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:24.111+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:24.111+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:26:24.145+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.202 seconds
[2025-06-04T03:26:56.237+0000] {processor.py:161} INFO - Started process (PID=888) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:56.240+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:26:56.242+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:56.242+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:56.265+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:26:56.299+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:56.299+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:26:56.331+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:26:56.331+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:26:56.353+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:27:27.533+0000] {processor.py:161} INFO - Started process (PID=897) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:27.535+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:27:27.538+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:27.538+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:27.570+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:27.607+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:27.607+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:27:27.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:27.629+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:27:27.657+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T03:27:59.933+0000] {processor.py:161} INFO - Started process (PID=906) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:59.934+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:27:59.937+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:59.936+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:59.962+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:27:59.996+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:27:59.996+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:00.024+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:00.024+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:28:00.046+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:28:31.340+0000] {processor.py:161} INFO - Started process (PID=914) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:28:31.341+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:28:31.345+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:31.345+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:28:31.376+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:28:31.409+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:31.409+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:28:31.436+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:28:31.436+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:28:31.790+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.455 seconds
[2025-06-04T03:29:03.442+0000] {processor.py:161} INFO - Started process (PID=923) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:03.443+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:29:03.446+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:03.446+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:03.471+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:03.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:03.512+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:03.535+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:03.534+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:29:03.557+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:29:33.652+0000] {processor.py:161} INFO - Started process (PID=931) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:33.655+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:29:33.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:33.658+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:33.690+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:29:33.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:33.727+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:29:33.753+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:29:33.753+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:29:33.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T03:30:04.335+0000] {processor.py:161} INFO - Started process (PID=940) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:04.339+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:30:04.341+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:04.341+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:04.372+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:04.412+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:04.412+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:04.443+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:04.442+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:30:04.470+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.139 seconds
[2025-06-04T03:30:34.568+0000] {processor.py:161} INFO - Started process (PID=949) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:34.571+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:30:34.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:34.573+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:34.598+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:30:34.634+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:34.634+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:30:34.657+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:30:34.657+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:30:34.687+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.124 seconds
[2025-06-04T03:31:04.906+0000] {processor.py:161} INFO - Started process (PID=958) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:04.908+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:31:04.912+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:04.912+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:04.939+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:04.975+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:04.975+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:05.002+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:05.001+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:31:05.030+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T03:31:35.229+0000] {processor.py:161} INFO - Started process (PID=967) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:35.232+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:31:35.234+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:35.234+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:35.259+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:31:35.300+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:35.300+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:31:35.327+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:31:35.327+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:31:35.353+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.129 seconds
[2025-06-04T03:32:05.581+0000] {processor.py:161} INFO - Started process (PID=976) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:05.583+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:32:05.587+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:05.586+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:05.622+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:05.660+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:05.659+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:05.686+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:05.686+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:32:05.708+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.133 seconds
[2025-06-04T03:32:35.868+0000] {processor.py:161} INFO - Started process (PID=985) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:35.871+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:32:35.874+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:35.873+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:35.901+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:32:35.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:35.949+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:32:35.980+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:32:35.980+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:32:36.010+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.147 seconds
[2025-06-04T03:33:06.311+0000] {processor.py:161} INFO - Started process (PID=994) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:06.312+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:33:06.315+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:06.314+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:06.343+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:06.379+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:06.378+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:06.410+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:06.410+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:33:06.436+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T03:33:36.714+0000] {processor.py:161} INFO - Started process (PID=1003) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:36.715+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:33:36.720+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:36.719+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:36.776+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:33:36.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:36.831+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:33:36.863+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:33:36.863+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:33:36.886+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.177 seconds
[2025-06-04T03:34:07.183+0000] {processor.py:161} INFO - Started process (PID=1012) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:07.185+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:34:07.188+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:07.187+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:07.211+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:07.248+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:07.246+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:07.274+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:07.274+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:34:07.300+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T03:34:37.365+0000] {processor.py:161} INFO - Started process (PID=1021) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:37.366+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:34:37.369+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:37.369+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:37.405+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:34:37.438+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:37.438+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:34:37.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:34:37.466+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:34:37.491+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T03:35:07.671+0000] {processor.py:161} INFO - Started process (PID=1029) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:07.673+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:35:07.678+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:07.678+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:07.701+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:07.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:07.739+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:07.766+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:07.765+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:35:07.794+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.127 seconds
[2025-06-04T03:35:38.061+0000] {processor.py:161} INFO - Started process (PID=1032) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:38.063+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:35:38.068+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:38.067+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:38.099+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:35:38.132+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:38.132+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:35:38.156+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:35:38.156+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:35:38.177+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.121 seconds
[2025-06-04T03:36:08.320+0000] {processor.py:161} INFO - Started process (PID=1041) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:08.327+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:36:08.330+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:08.329+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:08.353+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:08.389+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:08.389+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:36:08.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:08.416+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:36:08.448+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.132 seconds
[2025-06-04T03:36:38.778+0000] {processor.py:161} INFO - Started process (PID=1050) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:38.781+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:36:38.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:38.784+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:38.805+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:36:38.852+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:38.852+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:36:38.881+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:36:38.880+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:36:38.908+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.135 seconds
[2025-06-04T03:37:09.332+0000] {processor.py:161} INFO - Started process (PID=1059) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:09.333+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:37:09.335+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:09.335+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:09.362+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:09.402+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:09.402+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:09.440+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:09.440+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:37:09.464+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:37:39.756+0000] {processor.py:161} INFO - Started process (PID=1068) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:39.758+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:37:39.761+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:39.761+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:39.804+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:37:39.839+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:39.839+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:37:39.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:37:39.866+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:37:39.892+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T03:38:10.194+0000] {processor.py:161} INFO - Started process (PID=1077) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:10.197+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:38:10.200+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:10.200+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:10.235+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:10.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:10.268+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:10.291+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:10.291+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:38:10.328+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.138 seconds
[2025-06-04T03:38:40.675+0000] {processor.py:161} INFO - Started process (PID=1085) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:40.676+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:38:40.679+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:40.678+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:40.707+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:38:40.743+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:40.743+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:38:40.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:38:40.768+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:38:40.799+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T03:39:10.968+0000] {processor.py:161} INFO - Started process (PID=1094) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:10.970+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:39:10.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:10.973+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:11.001+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:11.036+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:11.036+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:11.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:11.065+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:39:11.092+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.132 seconds
[2025-06-04T03:39:41.462+0000] {processor.py:161} INFO - Started process (PID=1103) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:41.463+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:39:41.485+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.484+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:41.508+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:39:41.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.546+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:39:41.581+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:39:41.581+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:39:41.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.145 seconds
[2025-06-04T03:40:13.860+0000] {processor.py:161} INFO - Started process (PID=1112) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:13.862+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:40:13.866+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.866+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:13.887+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:13.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.924+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:13.950+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:13.950+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:40:13.976+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.120 seconds
[2025-06-04T03:40:45.376+0000] {processor.py:161} INFO - Started process (PID=1120) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:45.380+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:40:45.383+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.383+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:45.406+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:40:45.444+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.444+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:40:45.466+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:40:45.466+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:40:45.491+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.119 seconds
[2025-06-04T03:41:17.651+0000] {processor.py:161} INFO - Started process (PID=1128) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:17.653+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:41:17.655+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.655+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:17.677+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:17.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.713+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:17.738+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:17.737+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:41:17.763+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.119 seconds
[2025-06-04T03:41:49.147+0000] {processor.py:161} INFO - Started process (PID=1137) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:49.150+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:41:49.152+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.152+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:49.175+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:41:49.210+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.210+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:41:49.233+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:41:49.233+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:41:49.254+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.111 seconds
[2025-06-04T03:42:21.527+0000] {processor.py:161} INFO - Started process (PID=1146) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:21.528+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:42:21.532+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.531+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:21.555+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:21.590+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.590+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:21.615+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:21.614+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:42:21.635+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T03:42:53.946+0000] {processor.py:161} INFO - Started process (PID=1155) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:53.948+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:42:53.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:53.968+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:54.048+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:42:54.142+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:54.141+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:42:54.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:42:54.213+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:42:54.275+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.343 seconds
[2025-06-04T03:43:25.576+0000] {processor.py:161} INFO - Started process (PID=1163) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:25.578+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:43:25.580+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.580+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:25.601+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:25.640+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.640+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:25.662+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:25.662+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:43:25.684+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.113 seconds
[2025-06-04T03:43:56.988+0000] {processor.py:161} INFO - Started process (PID=1172) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:57.004+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:43:57.007+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:57.006+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:57.034+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:43:57.073+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:57.073+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:43:57.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:43:57.095+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:43:57.120+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.136 seconds
[2025-06-04T03:44:29.594+0000] {processor.py:161} INFO - Started process (PID=1181) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:44:29.596+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:44:29.598+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.598+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:44:29.657+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:44:29.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.712+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:44:29.759+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:44:29.758+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:44:29.792+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.202 seconds
[2025-06-04T03:45:00.954+0000] {processor.py:161} INFO - Started process (PID=1190) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:00.955+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:45:00.957+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:00.957+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:00.982+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:01.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:01.023+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:01.056+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:01.055+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:45:01.086+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:45:33.272+0000] {processor.py:161} INFO - Started process (PID=1199) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:33.275+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:45:33.278+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.278+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:33.299+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:45:33.336+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.336+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:45:33.362+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:45:33.361+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:45:33.382+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T03:46:04.749+0000] {processor.py:161} INFO - Started process (PID=1207) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:04.751+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:46:04.754+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.754+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:04.778+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:04.811+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.811+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:04.834+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:04.834+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:46:04.858+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.114 seconds
[2025-06-04T03:46:37.188+0000] {processor.py:161} INFO - Started process (PID=1216) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:37.189+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:46:37.192+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:37.218+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:46:37.257+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.257+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:46:37.282+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:46:37.281+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:46:37.319+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.140 seconds
[2025-06-04T03:47:09.025+0000] {processor.py:161} INFO - Started process (PID=1225) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:09.026+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:47:09.029+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:09.028+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:09.050+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:09.085+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:09.084+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:09.108+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:09.107+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:47:09.130+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
[2025-06-04T03:47:40.513+0000] {processor.py:161} INFO - Started process (PID=1234) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:40.514+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:47:40.518+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:40.518+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:40.538+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:47:40.577+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:40.577+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:47:40.603+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:47:40.602+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:47:40.627+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T03:48:13.031+0000] {processor.py:161} INFO - Started process (PID=1244) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:13.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:48:13.035+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:13.035+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:13.059+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:13.094+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:13.094+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:13.120+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:13.119+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:48:13.161+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.135 seconds
[2025-06-04T03:48:44.652+0000] {processor.py:161} INFO - Started process (PID=1253) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:44.654+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:48:44.656+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:44.656+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:44.681+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:48:44.732+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:44.732+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:48:44.807+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:48:44.807+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:48:44.882+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.235 seconds
[2025-06-04T03:49:17.023+0000] {processor.py:161} INFO - Started process (PID=1262) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:17.025+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:49:17.029+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:17.028+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:17.058+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:17.098+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:17.097+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:17.128+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:17.127+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:49:17.160+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.141 seconds
[2025-06-04T03:49:48.472+0000] {processor.py:161} INFO - Started process (PID=1271) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:48.475+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:49:48.482+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:48.481+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:48.519+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:49:48.556+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:48.555+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:49:48.579+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:49:48.579+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:49:48.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.138 seconds
[2025-06-04T03:50:20.977+0000] {processor.py:161} INFO - Started process (PID=1281) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:20.978+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:50:20.982+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:20.981+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:21.015+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:21.083+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:21.083+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:21.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:21.120+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:50:21.167+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.200 seconds
[2025-06-04T03:50:52.552+0000] {processor.py:161} INFO - Started process (PID=1291) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:52.554+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:50:52.557+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:52.556+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:52.581+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:50:52.619+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:52.618+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:50:52.647+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:50:52.647+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:50:52.674+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.127 seconds
[2025-06-04T03:51:24.969+0000] {processor.py:161} INFO - Started process (PID=1300) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:24.970+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:51:24.973+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:24.973+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:25.000+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:25.037+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:25.036+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:25.062+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:25.061+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:51:25.085+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T03:51:56.508+0000] {processor.py:161} INFO - Started process (PID=1309) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:56.509+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:51:56.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:56.512+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:56.544+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:51:56.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:56.585+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:51:56.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:51:56.612+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:51:56.640+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:52:28.993+0000] {processor.py:161} INFO - Started process (PID=1318) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:52:28.995+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:52:28.997+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:28.997+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:52:29.023+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:52:29.056+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:29.056+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:52:29.082+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:52:29.082+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:52:29.111+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:53:00.502+0000] {processor.py:161} INFO - Started process (PID=1328) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:00.504+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:53:00.506+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:00.506+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:00.536+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:00.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:00.572+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:00.600+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:00.600+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:53:00.625+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.130 seconds
[2025-06-04T03:53:32.847+0000] {processor.py:161} INFO - Started process (PID=1336) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:32.848+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:53:32.851+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:32.850+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:32.882+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:53:32.922+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:32.921+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:53:32.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:53:32.948+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:53:32.980+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:54:04.408+0000] {processor.py:161} INFO - Started process (PID=1345) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:04.409+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:54:04.413+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:04.412+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:04.434+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:04.468+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:04.468+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:04.505+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:04.505+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:54:04.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.140 seconds
[2025-06-04T03:54:36.963+0000] {processor.py:161} INFO - Started process (PID=1355) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:36.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:54:36.969+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:36.968+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:37.002+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:54:37.042+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:37.041+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:54:37.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:54:37.066+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:54:37.087+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.131 seconds
[2025-06-04T03:55:08.425+0000] {processor.py:161} INFO - Started process (PID=1364) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:08.426+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:55:08.429+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:08.428+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:08.452+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:08.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:08.490+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:08.516+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:08.515+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:55:08.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.123 seconds
[2025-06-04T03:55:40.841+0000] {processor.py:161} INFO - Started process (PID=1373) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:40.843+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:55:40.847+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.846+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:40.869+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:55:40.902+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.902+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:55:40.930+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:55:40.930+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:55:40.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.116 seconds
[2025-06-04T03:56:12.347+0000] {processor.py:161} INFO - Started process (PID=1382) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:12.349+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:56:12.351+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:12.351+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:12.378+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:12.428+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:12.427+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:12.491+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:12.491+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:56:12.549+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.206 seconds
[2025-06-04T03:56:44.614+0000] {processor.py:161} INFO - Started process (PID=1391) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:44.615+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:56:44.618+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:44.618+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:44.649+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:56:44.683+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:44.682+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:56:44.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:56:44.708+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:56:44.733+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.125 seconds
[2025-06-04T03:57:14.958+0000] {processor.py:161} INFO - Started process (PID=1400) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:14.960+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:57:14.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:14.962+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:14.997+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:15.060+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:15.059+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:16.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:16.342+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:57:16.379+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.193 seconds
[2025-06-04T03:57:48.585+0000] {processor.py:161} INFO - Started process (PID=1409) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:48.587+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:57:48.589+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:48.589+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:48.616+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:57:48.648+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:48.648+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:57:48.675+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:57:48.675+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:57:48.697+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.115 seconds
[2025-06-04T03:58:18.972+0000] {processor.py:161} INFO - Started process (PID=1418) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:18.974+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:58:18.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:18.978+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:19.015+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:19.058+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:19.058+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:19.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:19.095+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:58:19.126+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.159 seconds
[2025-06-04T03:58:49.617+0000] {processor.py:161} INFO - Started process (PID=1427) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:49.619+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:58:49.622+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:49.621+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:49.654+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:58:49.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:49.692+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:58:49.722+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:58:49.722+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:58:49.748+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.137 seconds
[2025-06-04T03:59:20.027+0000] {processor.py:161} INFO - Started process (PID=1436) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:20.028+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:59:20.032+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:20.030+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:20.054+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:20.085+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:20.085+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:20.108+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:20.108+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:59:20.132+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.109 seconds
[2025-06-04T03:59:50.410+0000] {processor.py:161} INFO - Started process (PID=1445) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:50.413+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T03:59:50.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:50.422+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:50.450+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T03:59:50.480+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:50.479+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T03:59:50.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T03:59:50.503+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T03:59:50.524+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.118 seconds
[2025-06-04T04:00:20.926+0000] {processor.py:161} INFO - Started process (PID=1453) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:20.927+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:00:20.930+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:20.929+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:20.955+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:20.992+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:20.992+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:21.035+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:21.035+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:00:21.155+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.233 seconds
[2025-06-04T04:00:51.247+0000] {processor.py:161} INFO - Started process (PID=1463) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:51.249+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:00:51.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:51.252+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:51.273+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:00:51.307+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:51.306+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:00:51.330+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:00:51.330+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:00:51.351+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.108 seconds
[2025-06-04T04:01:21.768+0000] {processor.py:161} INFO - Started process (PID=1472) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:21.778+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:01:21.783+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:21.782+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:21.883+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:21.949+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:21.949+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:21.985+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:21.985+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:01:22.014+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.251 seconds
[2025-06-04T04:01:52.253+0000] {processor.py:161} INFO - Started process (PID=1481) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:52.256+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:01:52.259+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:52.258+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:52.290+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:01:52.326+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:52.326+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:01:52.350+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:01:52.350+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:01:52.373+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.128 seconds
[2025-06-04T04:02:22.640+0000] {processor.py:161} INFO - Started process (PID=1490) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:22.642+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:02:22.645+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:22.645+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:22.669+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:22.709+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:22.709+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:22.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:22.734+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:02:22.758+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.122 seconds
[2025-06-04T04:02:53.130+0000] {processor.py:161} INFO - Started process (PID=1493) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:53.132+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:02:53.136+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:53.135+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:53.171+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:02:53.204+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:53.204+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:02:53.231+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:02:53.231+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:02:53.258+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.136 seconds
[2025-06-04T04:03:23.765+0000] {processor.py:161} INFO - Started process (PID=1502) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:23.770+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:03:23.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:23.772+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:23.800+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:23.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:23.832+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:23.854+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:23.853+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:03:23.878+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.117 seconds
[2025-06-04T04:03:54.194+0000] {processor.py:161} INFO - Started process (PID=1511) to work on /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:54.196+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/etl_pipeline_dag.py for tasks to queue
[2025-06-04T04:03:54.198+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:54.198+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:54.225+0000] {processor.py:840} INFO - DAG(s) 'etl_pipeline_daily' retrieved from /opt/airflow/dags/etl_pipeline_dag.py
[2025-06-04T04:03:54.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:54.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T04:03:54.278+0000] {logging_mixin.py:188} INFO - [2025-06-04T04:03:54.278+0000] {dag.py:3823} INFO - Setting next_dagrun for etl_pipeline_daily to 2025-06-03 06:00:00+00:00, run_after=2025-06-04 06:00:00+00:00
[2025-06-04T04:03:54.302+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/etl_pipeline_dag.py took 0.112 seconds
