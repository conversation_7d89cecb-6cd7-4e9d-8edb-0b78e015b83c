#!/usr/bin/env python3
"""
Create Snowflake External Stages for Live Data
Sets up Snowflake external stages, file formats, and storage integrations for S3 live data.
"""

import snowflake.connector
import logging
import yaml
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SnowflakeStagesSetup:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize Snowflake stages setup."""
        self.config = self._load_config(config_file)
        self.connection_params = {
            'account': self.config['snowflake']['account'],
            'user': self.config['snowflake']['user'],
            'password': 'Asdfjkll1234!@#$',  # From experiment 1
            'warehouse': self.config['snowflake']['warehouse'],
            'database': self.config['snowflake']['database'],
            'schema': self.config['snowflake']['schema'],
            'role': self.config['snowflake']['role']
        }
        
        # S3 configuration
        self.bucket_name = self.config['s3']['input_bucket']
        self.users_prefix = self.config['s3']['users_prefix']
        self.orders_prefix = self.config['s3']['orders_prefix']
        self.events_prefix = self.config['s3']['events_prefix']
        
        logger.info("✅ Snowflake Stages Setup initialized")

    def _load_config(self, config_file):
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {config_file} not found")
            raise

    def connect_to_snowflake(self):
        """Connect to Snowflake."""
        try:
            logger.info("🔄 Connecting to Snowflake...")
            conn = snowflake.connector.connect(**self.connection_params)
            cursor = conn.cursor()
            
            # Set context
            cursor.execute(f"USE DATABASE {self.config['snowflake']['database']}")
            cursor.execute(f"USE SCHEMA {self.config['snowflake']['schema']}")
            
            logger.info("✅ Connected to Snowflake successfully")
            return conn, cursor
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Snowflake: {e}")
            raise

    def create_schema_if_not_exists(self, cursor):
        """Create schema for live data if it doesn't exist."""
        try:
            schema_name = self.config['snowflake']['schema']
            logger.info(f"🔄 Creating schema {schema_name}...")
            
            cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
            cursor.execute(f"USE SCHEMA {schema_name}")
            
            logger.info(f"✅ Schema {schema_name} ready")
            
        except Exception as e:
            logger.error(f"❌ Failed to create schema: {e}")
            raise

    def create_storage_integration(self, cursor, role_arn):
        """Create storage integration for S3 access."""
        try:
            logger.info("🔄 Creating storage integration...")
            
            # External ID from Snowflake documentation for ap-southeast-2
            external_id = "SVLFKJI_SFCRole=2_L/MM2FeX9Dp2V/C="
            
            sql = f"""
            CREATE OR REPLACE STORAGE INTEGRATION S3_LIVE_INTEGRATION
              TYPE = EXTERNAL_STAGE
              STORAGE_PROVIDER = 'S3'
              ENABLED = TRUE
              STORAGE_AWS_ROLE_ARN = '{role_arn}'
              STORAGE_AWS_EXTERNAL_ID = '{external_id}'
              STORAGE_ALLOWED_LOCATIONS = ('s3://{self.bucket_name}/live-data/')
              COMMENT = 'Storage integration for live data pipeline'
            """
            
            cursor.execute(sql)
            logger.info("✅ Storage integration created successfully")
            
            # Show integration details
            cursor.execute("DESC STORAGE INTEGRATION S3_LIVE_INTEGRATION")
            results = cursor.fetchall()
            for row in results:
                if row[0] in ['STORAGE_AWS_IAM_USER_ARN', 'STORAGE_AWS_EXTERNAL_ID']:
                    logger.info(f"   {row[0]}: {row[1]}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create storage integration: {e}")
            raise

    def create_file_format(self, cursor):
        """Create file format for CSV files."""
        try:
            logger.info("🔄 Creating file format...")
            
            sql = """
            CREATE OR REPLACE FILE FORMAT CSV_LIVE_FORMAT
              TYPE = 'CSV'
              FIELD_DELIMITER = ','
              RECORD_DELIMITER = '\\n'
              SKIP_HEADER = 1
              FIELD_OPTIONALLY_ENCLOSED_BY = '"'
              TRIM_SPACE = TRUE
              ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
              ESCAPE = 'NONE'
              ESCAPE_UNENCLOSED_FIELD = '\\134'
              DATE_FORMAT = 'AUTO'
              TIMESTAMP_FORMAT = 'AUTO'
              NULL_IF = ('NULL', 'null', '', 'N/A', 'n/a')
              COMMENT = 'CSV format for live data pipeline'
            """
            
            cursor.execute(sql)
            logger.info("✅ File format created successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to create file format: {e}")
            raise

    def create_external_stages(self, cursor):
        """Create external stages for each data type."""
        try:
            stages = [
                ('S3_LIVE_USERS_STAGE', self.users_prefix, 'Live users data stage'),
                ('S3_LIVE_ORDERS_STAGE', self.orders_prefix, 'Live orders data stage'),
                ('S3_LIVE_EVENTS_STAGE', self.events_prefix, 'Live events data stage')
            ]
            
            for stage_name, prefix, comment in stages:
                logger.info(f"🔄 Creating stage {stage_name}...")
                
                sql = f"""
                CREATE OR REPLACE STAGE {stage_name}
                  STORAGE_INTEGRATION = S3_LIVE_INTEGRATION
                  URL = 's3://{self.bucket_name}/{prefix}/'
                  FILE_FORMAT = CSV_LIVE_FORMAT
                  COMMENT = '{comment}'
                """
                
                cursor.execute(sql)
                logger.info(f"✅ Stage {stage_name} created successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to create external stages: {e}")
            raise

    def create_external_tables(self, cursor):
        """Create external tables for direct querying."""
        try:
            logger.info("🔄 Creating external tables...")
            
            # Users external table
            users_sql = """
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_USERS (
              id STRING AS (value:c1::STRING),
              firstname STRING AS (value:c2::STRING),
              lastname STRING AS (value:c3::STRING),
              email STRING AS (value:c4::STRING),
              address STRING AS (value:c5::STRING),
              canal STRING AS (value:c6::STRING),
              country STRING AS (value:c7::STRING),
              creation_date STRING AS (value:c8::STRING),
              last_activity_date STRING AS (value:c9::STRING),
              gender NUMBER AS (value:c10::NUMBER),
              age_group NUMBER AS (value:c11::NUMBER),
              churn BOOLEAN AS (value:c12::BOOLEAN),
              batch_id STRING AS (value:c13::STRING),
              generated_at STRING AS (value:c14::STRING)
            )
            WITH LOCATION = @S3_LIVE_USERS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
            """
            
            cursor.execute(users_sql)
            logger.info("✅ External table EXT_LIVE_USERS created")
            
            # Orders external table
            orders_sql = """
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_ORDERS (
              id STRING AS (value:c1::STRING),
              user_id STRING AS (value:c2::STRING),
              transaction_date STRING AS (value:c3::STRING),
              item_count NUMBER AS (value:c4::NUMBER),
              amount NUMBER AS (value:c5::NUMBER),
              batch_id STRING AS (value:c6::STRING),
              generated_at STRING AS (value:c7::STRING)
            )
            WITH LOCATION = @S3_LIVE_ORDERS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
            """
            
            cursor.execute(orders_sql)
            logger.info("✅ External table EXT_LIVE_ORDERS created")
            
            # Events external table
            events_sql = """
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_EVENTS (
              user_id STRING AS (value:c1::STRING),
              event_id STRING AS (value:c2::STRING),
              platform STRING AS (value:c3::STRING),
              date STRING AS (value:c4::STRING),
              action STRING AS (value:c5::STRING),
              session_id STRING AS (value:c6::STRING),
              url STRING AS (value:c7::STRING),
              batch_id STRING AS (value:c8::STRING),
              generated_at STRING AS (value:c9::STRING)
            )
            WITH LOCATION = @S3_LIVE_EVENTS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
            """
            
            cursor.execute(events_sql)
            logger.info("✅ External table EXT_LIVE_EVENTS created")
            
        except Exception as e:
            logger.error(f"❌ Failed to create external tables: {e}")
            raise

    def test_stages(self, cursor):
        """Test the external stages by listing files."""
        try:
            logger.info("🧪 Testing external stages...")
            
            stages = ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE']
            
            for stage in stages:
                try:
                    cursor.execute(f"LIST @{stage}")
                    results = cursor.fetchall()
                    logger.info(f"✅ {stage}: {len(results)} files found")
                except Exception as e:
                    logger.warning(f"⚠️ {stage}: No files found or access issue - {e}")
            
        except Exception as e:
            logger.error(f"❌ Failed to test stages: {e}")

    def setup_snowflake_stages(self, role_arn):
        """Complete Snowflake stages setup process."""
        logger.info("🚀 Starting Snowflake external stages setup...")
        
        try:
            # Connect to Snowflake
            conn, cursor = self.connect_to_snowflake()
            
            # Create schema
            self.create_schema_if_not_exists(cursor)
            
            # Create storage integration
            self.create_storage_integration(cursor, role_arn)
            
            # Create file format
            self.create_file_format(cursor)
            
            # Create external stages
            self.create_external_stages(cursor)
            
            # Create external tables
            self.create_external_tables(cursor)
            
            # Test stages
            self.test_stages(cursor)
            
            # Close connection
            cursor.close()
            conn.close()
            
            logger.info("🎉 Snowflake external stages setup completed successfully!")
            
            return {
                'storage_integration': 'S3_LIVE_INTEGRATION',
                'file_format': 'CSV_LIVE_FORMAT',
                'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'],
                'external_tables': ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
            }
            
        except Exception as e:
            logger.error(f"❌ Snowflake stages setup failed: {e}")
            raise

    def generate_test_queries(self):
        """Generate test queries for external tables."""
        queries = [
            "-- Test queries for Snowflake external tables",
            "",
            "-- Check users data",
            "SELECT COUNT(*) as user_count FROM EXT_LIVE_USERS;",
            "SELECT * FROM EXT_LIVE_USERS LIMIT 5;",
            "",
            "-- Check orders data", 
            "SELECT COUNT(*) as order_count FROM EXT_LIVE_ORDERS;",
            "SELECT * FROM EXT_LIVE_ORDERS LIMIT 5;",
            "",
            "-- Check events data",
            "SELECT COUNT(*) as event_count FROM EXT_LIVE_EVENTS;",
            "SELECT * FROM EXT_LIVE_EVENTS LIMIT 5;",
            "",
            "-- Data freshness check",
            "SELECT ",
            "  MAX(generated_at) as latest_users_data",
            "FROM EXT_LIVE_USERS;",
            "",
            "SELECT ",
            "  MAX(generated_at) as latest_orders_data",
            "FROM EXT_LIVE_ORDERS;",
            "",
            "SELECT ",
            "  MAX(generated_at) as latest_events_data",
            "FROM EXT_LIVE_EVENTS;"
        ]
        
        # Save queries to file
        queries_file = Path("config/test_queries.sql")
        with open(queries_file, 'w') as f:
            f.write('\n'.join(queries))
        
        logger.info(f"✅ Test queries saved to {queries_file}")
        return queries

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Create Snowflake External Stages')
    parser.add_argument('--role-arn', required=True, help='AWS IAM Role ARN for Snowflake')
    
    args = parser.parse_args()
    
    setup = SnowflakeStagesSetup()
    
    try:
        # Generate test queries
        setup.generate_test_queries()
        
        # Run setup
        result = setup.setup_snowflake_stages(args.role_arn)
        
        logger.info("\n📋 SETUP SUMMARY:")
        logger.info(f"✅ Storage Integration: {result['storage_integration']}")
        logger.info(f"✅ File Format: {result['file_format']}")
        logger.info(f"✅ External Stages: {', '.join(result['stages'])}")
        logger.info(f"✅ External Tables: {', '.join(result['external_tables'])}")
        logger.info("\n🎯 Next steps:")
        logger.info("1. Run live_data_generator.py to generate test data")
        logger.info("2. Test external tables with queries in config/test_queries.sql")
        logger.info("3. Set up dbt models to consume live data")
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
